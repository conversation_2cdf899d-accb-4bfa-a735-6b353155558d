<template>
    <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px"
                :minHeight="100" :showOkBtn="false">
        <template #insertFooter>
        </template>
        <!-- 表单 -->
        <a-row class="dynamic-form ">
            <a-form :colon="false" size="middle" layout= "horizontal" 
            labelAlign= "right" 
 :labelCol="{ style: { width: '100px' } }"             :model="dataForm"  ref="formRef">
            <a-row :gutter="15">
                <!-- 具体表单 -->
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="no" >
<template #label>编号 
</template>     <XundaInput    v-model:value="dataForm.no"
 placeholder="请输入"   disabled
 detailed  allowClear  :style='{"width":"100%"}' :maskConfig = "maskConfig.no"    >
    </XundaInput>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="name" >
<template #label>名称 
</template>     <XundaInput    v-model:value="dataForm.name"
 placeholder="请输入"   disabled
 detailed  allowClear  :style='{"width":"100%"}' :maskConfig = "maskConfig.name"    >
    </XundaInput>
        </a-form-item>
    </a-col>
                <!-- 表单结束 -->
            </a-row>
            </a-form>
        </a-row>
    </BasicModal>
    <!-- 有关联表单详情：开始 -->
    <RelationDetail ref="relationDetailRef" />
    <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
    import { getDetailInfo } from './helper/api';
    import { getConfigData } from '@/api/onlineDev/visualDev';
    import { reactive, toRefs, nextTick, ref, computed, unref ,toRaw} from 'vue';
    import { BasicModal, useModal } from '@/components/Modal';
    // 有关联表单详情
    import RelationDetail from '@/views/common/dynamicModel/list/detail/index.vue';
    // 表单权限
    import { usePermission } from '@/hooks/web/usePermission';
    import { useMessage } from '@/hooks/web/useMessage';
    import { CaretRightOutlined } from '@ant-design/icons-vue';
    import { buildUUID } from '@/utils/uuid';
    import { useI18n } from '@/hooks/web/useI18n';

    interface State {
        dataForm: any;
        title: string;
        maskConfig: any;
        locationScope: any;
    }

    defineOptions({ name: 'Detail' });
    const { createMessage, createConfirm } = useMessage();
    const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
    
    const { t } = useI18n();
    const relationDetailRef = ref<any>(null);
    const state = reactive<State>({
        dataForm:{},
        title: t('common.detailText','详情'),
        maskConfig:{
        no: {"prefixType":1,"useUnrealMask":false,"maskType":1,"unrealMaskLength":1,"prefixLimit":0,"suffixLimit":0,"filler":"*","prefixSpecifyChar":"","suffixType":1,"ignoreChar":"","suffixSpecifyChar":""} ,
        name: {"prefixType":1,"useUnrealMask":false,"maskType":1,"unrealMaskLength":1,"prefixLimit":0,"suffixLimit":0,"filler":"*","prefixSpecifyChar":"","suffixType":1,"ignoreChar":"","suffixSpecifyChar":""} ,
}
,
        locationScope:{
}
,
    });
    const { title, dataForm, maskConfig } = toRefs(state);
    // 表单权限
    const { hasFormP } = usePermission();

    defineExpose({ init });

    function init(data) {
        state.dataForm.id = data.id;
            openModal();
        nextTick(() => {
            setTimeout(initData, 0);
        });
    }
    function initData() {
        changeLoading(true);
        if (state.dataForm.id) {
            getData(state.dataForm.id);
        } else {
            closeModal();
        }
    }
    function getData(id) {
        getDetailInfo(id).then((res) => {
            state.dataForm = res.data || {};
            nextTick(() => {
                changeLoading(false);
            });
        });
    }

    function toDetail(modelId, id) {
        if (!id) return;
        getConfigData(modelId).then((res) => {
            if (!res.data || !res.data.formData) return;
            const formConf = JSON.parse(res.data.formData);
            formConf.popupType = 'general';
            const data = { id, formConf, modelId };
            relationDetailRef.value?.init(data);
        });
    }
    function setFormProps(data) {
        setModalProps(data);
    }
    function changeLoading(loading) {
        setFormProps({ loading });
    }
    
</script>
