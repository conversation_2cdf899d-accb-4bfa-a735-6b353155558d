import { defHttp } from '@/utils/http/axios';

const questionnaireRecordApi = '/api/flowup/questionnaireRecord';
// 获取列表
export function getList(data) {
  return defHttp.post({ url: questionnaireRecordApi + '/getList', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: questionnaireRecordApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: questionnaireRecordApi + '/' + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: questionnaireRecordApi + '/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: questionnaireRecordApi + '/detail/' + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: questionnaireRecordApi + '/' + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: questionnaireRecordApi + '/batchRemove', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: questionnaireRecordApi + '/export', data });
}

// 填写问卷
export function doQuestionnaireRecord(data) {
  return defHttp.put({ url: questionnaireRecordApi + '/doQuestionnaireRecord', data });
}