<template>
  <BasicModal v-bind="$attrs" @register="registerModal" defaultFullscreen :minHeight="100"
    :cancelText="t('common.cancelText', '取消')" :okText="t('common.okText', '确定')" @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left"
        :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="title">
              <template #label>问卷标题 </template>
              <XundaInput v-model:value="dataForm.title" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }"
                :maskConfig="maskConfig.title" :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="12" class="ant-col-item">
            <a-form-item name="physicianId">
              <template #label>问卷医师 </template>
              <MyRelationForm v-model:value="dataForm.physicianId" placeholder="请选择"
                :templateJson="state.interfaceRes.physicianId" :allowClear="true" :style="{ width: '100%' }"
                :showSearch="false" :field="'physicianId'" modelId="661876504619124229"
                :columnOptions="optionsObj.pipeBedPhysiciancolumnOptions" :get-field-data-select="getPhysicianList"
                :get-data-change="getPhysicianInfo" relationField="name" popupWidth="800px" hasPage :pageSize="20">
              </MyRelationForm>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item-rest name="config">
              <Questionnaire title="问卷配置" v-model:value="dataForm.config" :questions="dataForm.config"> </Questionnaire>
            </a-form-item-rest>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
import { create, update, getInfo } from './api';
import { getList as getPhysicianList, getInfo as getPhysicianInfo } from '@/views/flow-up/physician/api';
import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
import MyRelationForm from '@/components/MyRelationForm/MyRelationForm.vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance } from 'ant-design-vue';
import Questionnaire from '@/components/FormItem/questionnaire/Questionnaire.vue';
// 表单权限
import { usePermission } from '@/hooks/web/usePermission';
interface State {
  dataForm: any;
  tableRows: any;
  dataRule: any;
  optionsObj: any;
  childIndex: any;
  isEdit: any;
  interfaceRes: any;
  //可选范围默认值
  ableAll: any;
  //掩码配置
  maskConfig: any;
  //定位属性
  locationScope: any;

  title: string;
  continueText: string;
  allList: any[];
  currIndex: number;
  isContinue: boolean;
  submitType: number;
  showContinueBtn: boolean;
}

const emit = defineEmits(['reload']);
const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const [registerModal, { openModal, setModalProps }] = useModal();
const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    name: undefined,
    age: undefined,
    sex: '',
    idCard: undefined,
    addressDetail: '',
    admittingDiagnosis: undefined,
    dischargeDate: undefined,
    admissionDate: undefined,
    pipeBedPhysician: '',
  },

  tableRows: {},
  dataRule: {
    title: [
      {
        required: true,
        message: t('sys.validate.textRequiredSuffix', '不能为空'),
        trigger: 'blur',
      },
    ],
  },

  optionsObj: {
    pipeBedPhysiciancolumnOptions: [
      { label: '姓名', value: 'name' },
      { label: '性别', value: 'sex' },
      { label: '手机号', value: 'phone' },
      { label: '身份证号', value: 'idCard' },
      { label: '民族', value: 'nation' },
      { label: '单位任职', value: 'position' },
    ],
  },

  childIndex: -1,
  isEdit: false,
  interfaceRes: {
    age: [],
    pipeBedPhysician: [],
    dischargeDate: [],
    name: [],
    idCard: [],
    admittingDiagnosis: [],
    sex: [],
    addressDetail: [],
    admissionDate: [],
  },
  //可选范围默认值
  ableAll: {},

  //掩码配置
  maskConfig: {
    fname: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    fidCard: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    fadmittingDiagnosis: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
  },

  //定位属性
  locationScope: {
    faddressDetail: [],
  },

  title: '',
  continueText: '',
  allList: [],
  currIndex: 0,
  isContinue: false,
  submitType: 0,
  showContinueBtn: true,
});
const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

const getPrevDisabled = computed(() => state.currIndex === 0);
const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
// 表单权限
const { hasFormP } = usePermission();

defineExpose({ init });

function init(data) {
  state.submitType = 0;
  state.isContinue = false;
  state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
  state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
  setFormProps({ continueLoading: false });
  state.dataForm.id = data.id;
  openModal();
  state.allList = data.allList;
  state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
  nextTick(() => {
    getForm().resetFields();
    setTimeout(initData, 0);
  });
}
function initData() {
  changeLoading(true);
  if (state.dataForm.id) {
    getData(state.dataForm.id);
  } else {
    // 设置默认值
    state.dataForm = {
      name: undefined,
      age: undefined,
      sex: '',
      idCard: undefined,
      addressDetail: '',
      admittingDiagnosis: undefined,
      dischargeDate: undefined,
      admissionDate: undefined,
      pipeBedPhysician: '',
    };
    if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
    state.childIndex = -1;
    changeLoading(false);
  }
}
function getForm() {
  const form = unref(formRef);
  if (!form) {
    throw new Error('form is null!');
  }
  return form;
}
function getData(id) {
  getInfo(id).then(res => {
    state.dataForm = res.data || {};
    state.childIndex = -1;
    changeLoading(false);
  });
}
async function handleSubmit(type) {
  try {
    const values = await getForm()?.validate();
    if (!values) return;
    setFormProps({ confirmLoading: true });
    const formMethod = state.dataForm.id ? update : create;
    console.log('values', state.dataForm);
    formMethod(state.dataForm)
      .then(res => {
        createMessage.success(res.msg);
        setFormProps({ confirmLoading: false });
        if (state.submitType == 1) {
          initData();
          state.isContinue = true;
        } else {
          setFormProps({ open: false });
          emit('reload');
        }
      })
      .catch(() => {
        setFormProps({ confirmLoading: false });
      });
  } catch (_) { }
}

// 跳转上一个
function handlePrev() {
  state.currIndex--;
  handleGetNewInfo();
}

// 跳转下一个
function handleNext() {
  state.currIndex++;
  handleGetNewInfo();
}

// 重新获取编辑信息
function handleGetNewInfo() {
  changeLoading(true);
  getForm().resetFields();
  const id = state.allList[state.currIndex].id;
  getData(id);
}

function setFormProps(data) {
  setModalProps(data);
}
function changeLoading(loading) {
  setModalProps({ loading });
}

// 关闭弹窗
async function onClose() {
  if (state.isContinue) emit('reload');
  return true;
}
</script>
