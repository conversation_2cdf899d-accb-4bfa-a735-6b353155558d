<template>
  <BasicModal v-bind="$attrs" @register="registerModal" defaultFullscreen :minHeight="100"
    :cancelText="t('common.cancelText', '取消')" :okText="t('common.okText', '确定')" @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left"
        :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef">
        <a-row :gutter="15">
          <a-col :span="24" class="ant-col-item">
            <a-tabs v-model:activeKey="state.activetab" tabPosition="top" class="mb-20">
              <a-tab-pane tab="个人信息" key="1" forceRender>
                <a-row :gutter="15">
                  <a-col :span="16" class="ant-col-item">
                    <a-row :gutter="15">
                      <a-col :span="24" class="ant-col-item">
                        <a-form-item name="name">
                          <template #label>姓名 </template>
                          <XundaInput v-model:value="dataForm.name" placeholder="请输入" :allowClear="true"
                            :style="{ width: '100%' }" :maskConfig="maskConfig.name" :showCount="false">
                          </XundaInput>
                        </a-form-item>
                      </a-col>
                      <a-col :span="24" class="ant-col-item">
                        <a-form-item name="sex">
                          <template #label>性别 </template>
                          <XundaSelect v-model:value="dataForm.sex" placeholder="请选择"
                            :templateJson="state.interfaceRes.sex" :allowClear="true" :style="{ width: '100%' }"
                            :showSearch="false" :options="optionsObj.sexOptions" :fieldNames="optionsObj.defaultProps">
                          </XundaSelect>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col :span="4" class="ant-col-item">
                    <a-form-item :labelCol="{ style: { width: '50px' } }" name="picture">
                      <template #label>照片 </template>
                      <XundaUploadImgSingle v-model:value="dataForm.picture"> </XundaUploadImgSingle>
                    </a-form-item>
                  </a-col>

                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="birthdate">
                      <template #label>出生日期 </template>
                      <XundaDatePicker v-model:value="dataForm.birthdate" placeholder="请选择" :allowClear="true"
                        :style="{ width: '100%' }" format="yyyy-MM-dd" :startTime="getRelationDate(false, 1, 1, '', '')"
                        :endTime="getRelationDate(false, 1, 1, '', '')">
                      </XundaDatePicker>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="nation">
                      <template #label>民族 </template>
                      <XundaSelect v-model:value="dataForm.nation" placeholder="请选择"
                        :templateJson="state.interfaceRes.nation" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.nationOptions" :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="politicalGroup">
                      <template #label>党派 </template>
                      <XundaSelect v-model:value="dataForm.politicalGroup" placeholder="请选择"
                        :templateJson="state.interfaceRes.politicalGroup" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.politicalGroupOptions"
                        :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="nativePlace">
                      <template #label>籍贯 </template>
                      <XundaInput v-model:value="dataForm.nativePlace" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.nativePlace" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="phone">
                      <template #label>手机号 </template>
                      <XundaInput v-model:value="dataForm.phone" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.phone" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="idCard">
                      <template #label>身份证号 </template>
                      <XundaInput v-model:value="dataForm.idCard" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.idCard" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>

                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="address">
                      <template #label>家庭住址 </template>
                      <XundaInput v-model:value="dataForm.address" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.address" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>

                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="position">
                      <template #label>单位任职 </template>

                      <XundaSelect v-model:value="dataForm.position" placeholder="请选择"
                        :templateJson="state.interfaceRes.position" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.positionOptions" :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>

                  <!-- 医师级别 -->

                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="medicalLevel">
                      <template #label>医师级别 </template>
                      <XundaSelect v-model:value="dataForm.medicalLevel" placeholder="请选择"
                        :templateJson="state.interfaceRes.medicalLevel" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.medicalLevelOptions"
                        :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="vocationalStatus">
                      <template #label>执业状况 </template>
                      <XundaSelect v-model:value="dataForm.vocationalStatus" placeholder="请选择"
                        :templateJson="state.interfaceRes.vocationalStatus" :allowClear="true"
                        :style="{ width: '100%' }" :showSearch="false" :options="optionsObj.vocationalStatusOptions"
                        :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-row :gutter="15"><a-col :span="12" class="ant-col-item">
                        <a-form-item name="certificateNo">
                          <template #label>执业证书编号 </template>
                          <XundaInput v-model:value="dataForm.certificateNo" placeholder="请输入" :allowClear="true"
                            :style="{ width: '100%' }" :maskConfig="maskConfig.certificateNo" :showCount="false">
                          </XundaInput>
                        </a-form-item>
                      </a-col>
                      <a-col :span="12" class="ant-col-item">
                        <a-form-item name="certificate">
                          <template #label>执业证书 </template>
                          <XundaUploadFile v-model:value="dataForm.certificate" :fileSize="10" sizeUnit="MB" :limit="9"
                            pathType="defaultPath" timeFormat="YYYY" buttonText="点击上传">
                          </XundaUploadFile>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-row :gutter="15">
                      <a-col :span="12" class="ant-col-item">
                        <a-form-item name="qualificationCertificateNo">
                          <template #label>资格证书编号 </template>
                          <XundaInput v-model:value="dataForm.qualificationCertificateNo" placeholder="请输入"
                            :allowClear="true" :style="{ width: '100%' }"
                            :maskConfig="maskConfig.qualificationCertificateNo" :showCount="false">
                          </XundaInput>
                        </a-form-item>
                      </a-col>
                      <a-col :span="12" class="ant-col-item">
                        <a-form-item name="qualificationCertificate">
                          <template #label>资格证书 </template>
                          <XundaUploadFile v-model:value="dataForm.qualificationCertificate" :fileSize="10"
                            sizeUnit="MB" :limit="9" pathType="defaultPath" timeFormat="YYYY" buttonText="点击上传">
                          </XundaUploadFile>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-row :gutter="15">
                      <a-col :span="12" class="ant-col-item">
                        <a-form-item name="workCertificateNo">
                          <template #label>工作证书编号 </template>
                          <XundaInput v-model:value="dataForm.workCertificateNo" placeholder="请输入" :allowClear="true"
                            :style="{ width: '100%' }" :maskConfig="maskConfig.workCertificateNo" :showCount="false">
                          </XundaInput>
                        </a-form-item>
                      </a-col>
                      <a-col :span="12" class="ant-col-item">
                        <a-form-item name="workCertificate">
                          <template #label>工作证书 </template>
                          <XundaUploadFile v-model:value="dataForm.workCertificate" :fileSize="10" sizeUnit="MB"
                            :limit="9" pathType="defaultPath" timeFormat="YYYY" buttonText="点击上传">
                          </XundaUploadFile>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="technicalTitle">
                      <TechnicalTitleItem title="技术职称" :is-edit="true" v-model:value="dataForm.majorField"
                        :data-source="dataForm.technicalTitle"></TechnicalTitleItem>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="remarks">
                      <template #label>备注说明 </template>
                      <XundaTextarea v-model:value="dataForm.remarks" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.remarks" :showCount="false">
                      </XundaTextarea>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-tab-pane>
              <a-tab-pane tab="专业方向" key="2" forceRender>
                <a-row :gutter="15">
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="graduationSchool">
                      <template #label>毕业学校 </template>
                      <XundaInput v-model:value="dataForm.graduationSchool" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.graduationSchool" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>

                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="graduationTime">
                      <template #label>毕业时间 </template>
                      <XundaDatePicker v-model:value="dataForm.graduationTime" placeholder="请选择" :allowClear="true"
                        :style="{ width: '100%' }" format="yyyy-MM-dd" :startTime="getRelationDate(false, 1, 1, '', '')"
                        :endTime="getRelationDate(false, 1, 1, '', '')">
                      </XundaDatePicker>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="educationLevel">
                      <template #label>学历 </template>
                      <XundaSelect v-model:value="dataForm.educationLevel" placeholder="请选择"
                        :templateJson="state.interfaceRes.position" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.educationLevelOptions"
                        :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="degree">
                      <template #label>学位 </template>
                      <XundaSelect v-model:value="dataForm.educationLevel" placeholder="请选择"
                        :templateJson="state.interfaceRes.degree" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.degreeOptions" :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="major">
                      <template #label>专业 </template>
                      <XundaSelect v-model:value="dataForm.major" placeholder="请选择"
                        :templateJson="state.interfaceRes.position" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.majorOptions" :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="workingSeniority">
                      <template #label>从业时间 </template>
                      <XundaDatePicker v-model:value="dataForm.workingSeniority" placeholder="请选择" :allowClear="true"
                        :style="{ width: '100%' }" format="yyyy-MM-dd" :startTime="getRelationDate(false, 1, 1, '', '')"
                        :endTime="getRelationDate(false, 1, 1, '', '')">
                      </XundaDatePicker>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="workType">
                      <template #label>从业类型 </template>
                      <XundaSelect v-model:value="dataForm.workType" placeholder="请选择"
                        :templateJson="state.interfaceRes.workType" :allowClear="true" :style="{ width: '100%' }"
                        :showSearch="false" :options="optionsObj.workTypeOptions" :fieldNames="optionsObj.defaultProps">
                      </XundaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="majorField">
                      <MajorFieldItem title="专业方向" :is-edit="true" v-model:value="dataForm.majorField"
                        :data-source="dataForm.majorField"></MajorFieldItem>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-tab-pane>
              <a-tab-pane tab="工作经历" key="3" forceRender>
                <a-row :gutter="15">
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="provinceAndCity">
                      <template #label>省份 </template>
                      <XundaAreaSelect v-model:value="dataForm.provinceAndCity" placeholder="请选择" :allowClear="true"
                        :style="{ width: '100%' }" :showSearch="false" :level="1">
                      </XundaAreaSelect>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="hospitalName">
                      <template #label>医院名称 </template>
                      <XundaInput v-model:value="dataForm.hospitalName" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.hospitalName" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="department">
                      <template #label>科室 </template>
                      <XundaInput v-model:value="dataForm.department" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.department" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="departmentName">
                      <template #label>科室名称 </template>
                      <XundaInput v-model:value="dataForm.departmentName" placeholder="请输入" :allowClear="true"
                        :style="{ width: '100%' }" :maskConfig="maskConfig.departmentName" :showCount="false">
                      </XundaInput>
                    </a-form-item>
                  </a-col>
                  <!-- 工作经历是一个表形式，包含所在单位 开始时间 结束时间 任职情况 -->
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="workHistory">
                      <FormItemTable title="工作经历" show-action-column :columns="workHistoryColumns"
                        v-model:value="dataForm.workHistory" :data-source="dataForm.workHistory"></FormItemTable>
                    </a-form-item>
                  </a-col>
                  <!-- 学术任职是一个表形式，包含任职类别 学会名称 类别 届数 职务 开始时间 结束时间 -->
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="academicPosition">
                      <FormItemTable title="学术任职" show-action-column :columns="academicPositionColumns"
                        v-model:value="dataForm.academicPosition" :data-source="dataForm.academicPosition">
                      </FormItemTable>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-tab-pane>
            </a-tabs>
          </a-col>
          <!-- 具体表单 -->
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
import { create, update, getInfo } from './api';
import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
import { XundaUploadImgSingle } from '@/components/Xunda';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance } from 'ant-design-vue';
// 表单权限
import { usePermission } from '@/hooks/web/usePermission';
import { getRelationDate } from '@/utils/myUtil';
import {
  getPhysicianAllDictionaryData,
  workHistoryColumns,
  academicPositionColumns,
} from '@/views/flow-up/physician/index';
import FormItemTable from '@/components/FormItemTable/FormItemTable.vue';

import TechnicalTitleItem from './TechnicalTitleItem.vue';
import MajorFieldItem from './MajorFieldItem.vue';

interface State {
  dataForm: any;
  tableRows: any;
  dataRule: any;
  optionsObj: any;
  childIndex: any;
  isEdit: any;
  interfaceRes: any;
  //可选范围默认值
  ableAll: any;
  //掩码配置
  maskConfig: any;
  //定位属性
  locationScope: any;

  title: string;
  continueText: string;
  allList: any[];
  currIndex: number;
  isContinue: boolean;
  submitType: number;
  showContinueBtn: boolean;
  activetab: string;
  selectedctWorkHistoryRowKeys: any[];
  selectedctAcademicPositionRowKeys: any[];
}

const emit = defineEmits(['reload']);
const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const [registerModal, { openModal, setModalProps }] = useModal();
const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    name: undefined,
    sex: '',
    picture: undefined,
    phone: undefined,
    nativePlace: undefined,
    idCard: undefined,
    birthdate: undefined,
    nation: '',
    politicalGroup: undefined,
    address: undefined,
    position: undefined,
    technicalTitle: undefined,
    vocationalStatus: undefined,
    certificate: [],
    graduationSchool: undefined,
    graduationTime: undefined,
    major: undefined,
    degree: undefined,
    educationLevel: undefined,
    workType: undefined,
    workingSeniority: undefined,
    majorField: undefined,
    provinceAndCity: undefined,
    hospitalName: undefined,
    department: undefined,
    academicPosition: undefined,
    workHistory: [],
  },

  tableRows: {},

  dataRule: {
    name: [
      {
        required: true,
        message: t('sys.validate.textRequiredSuffix', '不能为空'),
        trigger: 'blur',
      },
    ],
    sex: [
      {
        required: true,
        message: t('sys.validate.arrayRequiredPrefix	', '请至少选择一个'),
        trigger: 'change',
      },
    ],
    phone: [
      {
        pattern: /^1[3456789]\d{9}$|^0\d{2,3}-?\d{7,8}$/,
        message: t('sys.validate.phone', '请输入正确的联系方式'),
        trigger: 'blur',
      },
    ],
    idCard: [
      {
        pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: t('sys.validate.idCard', '请输入正确的身份证号码'),
        trigger: 'blur',
      },
    ],
    birthdate: [
      {
        required: true,
        message: t('sys.validate.textRequiredSuffix', '不能为空'),
        trigger: 'change',
      },
    ],
  },

  optionsObj: {
    defaultProps: { label: 'fullName', value: 'enCode' },
    sexOptions: [],
    nationOptions: [],
    positionOptions: [],
    medicalLevelOptions: [],
    vocationalStatusOptions: [],
    politicalGroupOptions: [],
    workTypeOptions: [],
  },

  childIndex: -1,
  isEdit: false,
  interfaceRes: {
    hospitalName: [],
    position: [],
    educationLevel: [],
    workHistory: [],
    sex: [],
    workType: [],
    address: [],
    vocationalStatus: [],
    degree: [],
    provinceAndCity: [],
    name: [],
    idCard: [],
    department: [],
    technicalTitle: [],
    major: [],
    majorField: [],
    nativePlace: [],
    birthdate: [],
    nation: [],
    graduationSchool: [],
    academicPosition: [],
    graduationTime: [],
    certificate: [],
    politicalGroup: [],
    workingSeniority: [],
    phone: [],
  },
  //可选范围默认值
  ableAll: {},

  //掩码配置
  maskConfig: {
    name: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    phone: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    nativePlace: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    idCard: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    politicalGroup: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    address: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    position: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    technicalTitle: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    vocationalStatus: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    graduationSchool: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    major: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    degree: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    educationLevel: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    workType: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    majorField: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    hospitalName: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    department: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    academicPosition: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    workHistory: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
  },

  //定位属性
  locationScope: {},

  title: '',
  continueText: '',
  allList: [],
  currIndex: 0,
  isContinue: false,
  submitType: 0,
  showContinueBtn: true,
  activetab: '1',
  selectedctWorkHistoryRowKeys: [],
  selectedctAcademicPositionRowKeys: [],
});

const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

const getPrevDisabled = computed(() => state.currIndex === 0);
const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
// 表单权限
const { hasFormP } = usePermission();

defineExpose({ init });

function init(data) {
  state.submitType = 0;
  state.activetab = '1';
  state.isContinue = false;
  state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
  state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
  setFormProps({ continueLoading: false });
  state.dataForm.id = data.id;
  openModal();
  state.allList = data.allList;
  state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
  nextTick(() => {
    getForm().resetFields();
    setTimeout(initData, 0);
  });
}
function initData() {
  changeLoading(true);
  if (state.dataForm.id) {
    getData(state.dataForm.id);
  } else {
    //初始化options
    getDictionaryOptions();
    // 设置默认值
    state.dataForm = {
      name: undefined,
      sex: '',
      picture: undefined,
      phone: undefined,
      nativePlace: undefined,
      idCard: undefined,
      birthdate: undefined,
      nation: '',
      politicalGroup: undefined,
      address: undefined,
      position: undefined,
      technicalTitle: undefined,
      vocationalStatus: undefined,
      certificate: [],
      graduationSchool: undefined,
      graduationTime: undefined,
      major: undefined,
      degree: undefined,
      educationLevel: undefined,
      workType: undefined,
      workingSeniority: undefined,
      majorField: undefined,
      provinceAndCity: undefined,
      hospitalName: undefined,
      department: undefined,
      academicPosition: undefined,
      workHistory: [],
    };
    if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
    state.childIndex = -1;
    changeLoading(false);
  }
}
function getForm() {
  const form = unref(formRef);
  if (!form) {
    throw new Error('form is null!');
  }
  return form;
}
function getData(id) {
  state.activetab = '1';
  getInfo(id).then(res => {
    state.dataForm = res.data || {};
    getDictionaryOptions();
    state.childIndex = -1;
    changeLoading(false);
  });
}
async function handleSubmit(type) {
  try {
    const values = await getForm()?.validate();
    if (!values) return;
    const formMethod = state.dataForm.id ? update : create;
    if (state.dataForm.workHistory && state.dataForm.workHistory.length > 0) {
      let index1 = state.dataForm.workHistory.findIndex(item => !item.unit);
      if (index1 !== -1) {
        let index = index1 + 1;
        createMessage.error('请填写工作经历第' + index + '条的所在单位');
        return;
      }
    }

    if (state.dataForm.academicPosition && state.dataForm.academicPosition.length > 0) {
      let index2 = state.dataForm.academicPosition.findIndex(item => !item.societyName);
      if (index2 !== -1) {
        let index = index2 + 1;
        createMessage.error('请填写学术任职第' + index + '条的学会名称');
        return;
      }
    }
    setFormProps({ confirmLoading: true });
    formMethod(state.dataForm)
      .then(res => {
        createMessage.success(res.msg);
        setFormProps({ confirmLoading: false });
        if (state.submitType == 1) {
          initData();
          state.isContinue = true;
        } else {
          setFormProps({ open: false });
          emit('reload');
        }
      })
      .catch(() => {
        setFormProps({ confirmLoading: false });
      });
  } catch (_) { }
}
function handlePrev() {
  state.currIndex--;
  handleGetNewInfo();
}
function handleNext() {
  state.currIndex++;
  handleGetNewInfo();
}
function handleGetNewInfo() {
  changeLoading(true);
  getForm().resetFields();
  const id = state.allList[state.currIndex].id;
  getData(id);
}
function setFormProps(data) {
  setModalProps(data);
}
function changeLoading(loading) {
  setModalProps({ loading });
}
async function onClose() {
  if (state.isContinue) emit('reload');
  return true;
}

//数据选项--数据字典初始化方法
function getDictionaryOptions() {
  getPhysicianAllDictionaryData(state.optionsObj);
}
</script>
