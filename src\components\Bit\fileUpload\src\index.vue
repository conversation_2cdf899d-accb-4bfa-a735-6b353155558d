<template>
  <div class="custom-upload-container">
    <!-- 上传组件：集成了批量上传和单文件上传 -->
    <a-upload
      :file-list="fileList"
      :remove="handleRemove"
      :before-upload="beforeUpload"
      :customRequest="customRequest"
      :accept="accept"
      :multiple="multiple"
      :showUploadList="false"
      :disabled="disabled">
      <a-button :disabled="disabled || loading" type="primary" preIcon="icon-ym icon-ym-btn-upload">
        {{ buttonText || t('component.upload.clickUpload') }}
      </a-button>
    </a-upload>
    <a-tooltip :title="tipContent" placement="top">
      <QuestionCircleOutlined v-if="tipContent" class="upload-tip-icon" />
    </a-tooltip>

    <!-- 提示文本 -->
    <div class="upload-file__tip" v-if="tipText">{{ tipText }}</div>

    <!-- 文件列表显示 -->
    <div class="upload-file-list" v-if="showFileList">
      <div class="upload-file-list__item" v-for="(file, index) in fileList" :key="file.fileId || file.uid">
        <!-- 文件名称区域，点击可预览 -->
        <a class="upload-file-list__item-name" :title="file.name + (file.fileSize ? '(' + toFileSize(file.fileSize) + ')' : '')" @click="handlePreview(file)">
          <PaperClipOutlined v-if="showIcon" />
          {{ file.name }}{{ file.fileSize ? `(${toFileSize(file.fileSize)})` : '' }}
        </a>

        <!-- 操作按钮区域 -->
        <span class="upload-file-list__item-actions">
          <EyeOutlined :title="t('component.upload.view')" @click="handlePreview(file)" v-if="showView" />
          <DownloadOutlined :title="t('component.upload.download')" @click="handleDownload(file)" v-if="showDownload" />
          <CloseOutlined :title="t('component.upload.del')" @click="handleRemove(file)" v-show="!disabled" />
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  /**
   * CustomUpload 组件
   *
   * 功能：
   * 1. 支持单文件和多文件上传
   * 2. 多文件模式下支持批量上传（一次请求上传多个文件）
   * 3. 支持文件大小和类型限制
   * 4. 支持自定义上传和下载接口
   * 5. 支持文件预览和下载
   *
   * <AUTHOR> Improvement
   */
  import { ref, computed, unref, watch } from 'vue';
  import { PaperClipOutlined, EyeOutlined, DownloadOutlined, CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useGlobSetting } from '@/hooks/setting';
  import { createImgPreview } from '@/components/Preview/index';
  import { toFileSize } from '@/utils/xunda';
  import { downloadByUrl } from '@/utils/file/download';
  import { getToken } from '@/utils/auth';
  import { useLoading } from '@/components/Loading';

  defineOptions({ name: 'CustomUpload', inheritAttrs: false });

  /**
   * 组件属性定义
   */
  const props = defineProps({
    // 文件列表：用于v-model双向绑定
    value: { type: Array, default: () => [] },
    // 是否禁用上传功能
    disabled: { type: Boolean, default: false },
    // 文件大小限制（数值）
    fileSize: { type: Number, default: 10 },
    // 文件大小单位：KB, MB, GB
    sizeUnit: { type: String, default: 'MB' },
    // 文件数量限制，0表示不限制
    limit: { type: Number, default: 0 },
    // 提示文本：显示在上传按钮下方
    tipText: { type: String, default: '' },
    // 提示图标的内容：鼠标悬停时显示
    tipContent: { type: String, default: '' },
    // 是否显示已上传的文件列表
    showFileList: { type: Boolean, default: true },
    // 接受的文件类型：MIME类型或文件扩展名（.jpg, .png等）
    accept: { type: String, default: '*' },
    // 是否允许多文件选择
    multiple: { type: Boolean, default: false },
    // 上传按钮显示的文本
    buttonText: { type: String, default: '点击上传' },
    // 是否在文件名前显示文件图标
    showIcon: { type: Boolean, default: true },
    // 是否显示文件预览按钮
    showView: { type: Boolean, default: true },
    // 是否显示文件下载按钮
    showDownload: { type: Boolean, default: true },
    // 自定义上传接口的URL（相对路径，会与apiUrl拼接）
    customUploadUrl: { type: String, default: '' },
    // 自定义下载接口的URL
    customDownloadUrl: { type: String, default: '' },
    // 额外的上传参数：会附加到FormData中
    uploadParams: { type: Object, default: () => ({}) },
    // 是否显示内部错误提示消息（如果为false，则仅触发error事件，由父组件处理错误提示）
    showErrorMessage: { type: Boolean, default: true },
  });

  const emit = defineEmits(['update:value', 'change', 'success', 'error', 'loading']);
  const { t } = useI18n();
  const { createMessage } = useMessage();
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);
  const fileList = ref([]);

  // 使用全局 loading 状态
  const [openFullLoading, closeFullLoading] = useLoading({
    tip: 'Loading...',
  });

  // 支持的文件类型定义
  const videoTypeList = ['avi', 'wmv', 'mpg', 'mpeg', 'mov', 'rm', 'ram', 'swf', 'flv', 'mp4', 'mp3', 'wma', 'avi', 'rm', 'rmvb', 'flv', 'mpg', 'mkv'];
  const imgTypeList = ['png', 'jpg', 'jpeg', 'bmp', 'gif'];
  const zipTypeList = ['rar', 'zip', 'arj', 'z', '7z'];

  // 单位转换
  const units = {
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024,
  };

  watch(
    () => props.value,
    val => {
      fileList.value = val && Array.isArray(val) ? val : [];
    },
    {
      deep: true,
      immediate: true,
    },
  );

  /**
   * 上传前文件校验函数
   * 检查文件是否符合上传要求（数量限制、大小限制、类型限制）
   *
   * @param {File} file - 要上传的文件对象
   * @returns {boolean} - 是否允许上传
   */
  function beforeUpload(file) {
    // 1. 检查文件数量限制
    const isTopLimit = props.limit ? fileList.value.length >= props.limit : false;
    if (isTopLimit) {
      const errorMsg = t('component.upload.fileMaxNumber', [props.limit]);
      // 触发错误事件
      emit('error', new Error(errorMsg));
      // 根据配置决定是否显示内部错误消息
      if (props.showErrorMessage) {
        createMessage.error(errorMsg);
      }
      return false;
    }

    // 2. 检查文件大小限制
    const unitNum = units[props.sizeUnit]; // 获取单位换算值（KB/MB/GB）
    const isRightSize = file.size / unitNum < props.fileSize;
    if (!isRightSize) {
      const errorMsg = t('component.upload.fileMaxSize', { size: props.fileSize, unit: props.sizeUnit });
      // 触发错误事件
      emit('error', new Error(errorMsg));
      // 根据配置决定是否显示内部错误消息
      if (props.showErrorMessage) {
        createMessage.error(errorMsg);
      }
      return false;
    }

    // 3. 检查文件类型限制
    if (props.accept && props.accept !== '*') {
      const extension = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const fileType = file.type;

      // 3.1 检查文件扩展名是否在accept列表中
      if (props.accept.indexOf(extension) > -1) return true;

      // 3.2 检查MIME类型
      // 支持通配符匹配，如image/*可匹配所有图片类型
      if (props.accept.includes('image/*') && new RegExp('image/*').test(fileType)) return true;
      if (props.accept.includes('video/*') && new RegExp('video/*').test(fileType)) return true;
      if (props.accept.includes('audio/*') && new RegExp('audio/*').test(fileType)) return true;

      // 文件类型不符合要求，显示错误提示
      const errorMsg = t('component.upload.fileTypeCheck', [getAcceptText.value]);
      // 触发错误事件
      emit('error', new Error(errorMsg));
      // 根据配置决定是否显示内部错误消息
      if (props.showErrorMessage) {
        createMessage.error(errorMsg);
      }
      return false;
    }

    // 通过所有检查，允许上传
    return true;
  }

  // 获取接受的文件类型文本描述
  const getAcceptText = computed(() => {
    let txt = '';
    if (props.accept.includes('image/*')) txt += '、' + t('component.upload.image');
    if (props.accept.includes('video/*')) txt += '、' + t('component.upload.video');
    if (props.accept.includes('audio/*')) txt += '、' + t('component.upload.audio');
    if (props.accept.includes('.xls,.xlsx')) txt += '、excel';
    if (props.accept.includes('.doc,.docx')) txt += '、word';
    if (props.accept.includes('.pdf')) txt += '、pdf';
    if (props.accept.includes('.txt')) txt += '、txt';
    return txt.slice(1) || props.accept;
  });

  // 存储批量上传的文件队列
  const uploadQueue = ref([]);

  /**
   * 自定义上传请求处理函数
   * 根据是否开启多文件模式，选择不同的上传策略：
   * 1. 多文件模式：将文件添加到上传队列，统一批量上传
   * 2. 单文件模式：立即上传单个文件
   *
   * @param {Object} options - ant-design-vue的上传配置项
   * @param {File} options.file - 要上传的文件对象
   * @param {Function} options.onProgress - 上传进度回调
   * @param {Function} options.onSuccess - 上传成功回调
   * @param {Function} options.onError - 上传失败回调
   * @returns {boolean|Error} - 上传状态
   */
  async function customRequest(options) {
    const { file, onProgress, onSuccess, onError } = options;
    if (!props.customUploadUrl) {
      return Error('未配置customUploadUrl');
    }

    emit('loading', true); // 触发loading事件
    openFullLoading(); // 设置全局loading状态
    // 检查是否是批量上传模式
    if (props.multiple) {
      // 创建临时文件对象：包含文件信息和回调函数
      const tempFile = {
        name: file.name,
        uid: file.uid,
        fileSize: file.size,
        fileExtension: file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),
        status: 'uploading',
        progress: 0,
        file, // 原始文件对象
        onProgress, // 进度回调
        onSuccess, // 成功回调
        onError, // 错误回调
      };

      // 添加到文件列表显示
      fileList.value.push(tempFile);
      emit('update:value', unref(fileList));
      emit('change', unref(fileList));

      // 添加到上传队列
      uploadQueue.value.push(tempFile);

      // 自动触发批量上传 - 使用延迟以收集同时选择的多个文件
      if (uploadQueue.value.length === 1) {
        // 仅当这是队列中的第一个文件时，才延迟执行批量上传
        setTimeout(() => {
          executeBatchUpload();
        }, 100); // 短暂延迟，收集同时选择的多个文件
      }

      return true;
    }

    // 单文件上传模式
    const formData = new FormData();
    formData.append('file', file); // 添加文件

    // 添加额外参数到FormData
    if (props.uploadParams) {
      Object.keys(props.uploadParams).forEach(key => {
        formData.append(key, props.uploadParams[key]);
      });
    }

    try {
      // 上传进度处理函数
      const updateProgress = event => {
        if (event.lengthComputable) {
          const percent = Math.floor((event.loaded / event.total) * 100);
          onProgress({ percent });
        }
      };

      // 构建上传URL
      let uploadUrl = props.customUploadUrl ? `${apiUrl.value}${props.customUploadUrl}` : `${apiUrl.value}/api/file/upload`;

      // 获取认证头信息
      const getHeaders = computed(() => ({ Authorization: getToken() as string }));

      // 创建XMLHttpRequest请求以支持进度监控
      const xhr = new XMLHttpRequest();
      xhr.open('POST', uploadUrl);

      // 设置认证token
      const token = getHeaders.value.Authorization;
      if (token) {
        xhr.setRequestHeader('Authorization', token);
      }

      // 监听上传进度
      xhr.upload.addEventListener('progress', updateProgress);

      // 处理请求完成
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const result = JSON.parse(xhr.response);
            if (result.code !== 200) {
              emit('loading', false); // 上传成功，关闭loading状态
              closeFullLoading(); // 关闭全局loading状态
              emit('error', result);
              return;
            }

            // 构建文件数据对象
            const fileData = {
              name: file.name,
              fileId: result.data?.fileId || result.data?.name || file.uid,
              fileSize: file.size,
              fileExtension: file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase(),
              url: result.data?.url || '',
            };

            // 更新文件列表并触发事件
            fileList.value.push(fileData);
            emit('update:value', unref(fileList));
            emit('change', unref(fileList));
            emit('success', fileData);
            onSuccess(result);
            emit('loading', false); // 上传成功，关闭loading状态
            closeFullLoading(); // 关闭全局loading状态
          } catch (e) {
            // 处理响应解析错误
            onError(e);
            emit('error', e);
            emit('loading', false); // 上传失败，关闭loading状态
            closeFullLoading(); // 关闭全局loading状态
          }
        } else {
          // 处理HTTP错误
          onError(new Error(xhr.statusText));
          emit('error', new Error(xhr.statusText));
          emit('loading', false); // HTTP错误，关闭loading状态
          closeFullLoading(); // 关闭全局loading状态
        }
      };

      // 处理网络错误
      xhr.onerror = () => {
        onError(new Error('Network Error'));
        emit('error', new Error('Network Error'));
        emit('loading', false); // 网络错误，关闭loading状态
        closeFullLoading(); // 关闭全局loading状态
      };

      // 发送请求
      xhr.send(formData);
    } catch (error) {
      // 处理其他异常
      onError(error);
      emit('error', error);
      emit('loading', false); // 其他异常，关闭loading状态
      closeFullLoading(); // 关闭全局loading状态
    }
  }

  /**
   * 移除文件处理函数
   * 从文件列表中删除指定文件，并更新组件状态
   *
   * @param {Object} file - 要移除的文件对象
   */
  function handleRemove(file) {
    // 通过fileId或uid查找文件在列表中的位置
    const index = fileList.value.findIndex(item => (item.fileId || item.uid) === (file.fileId || file.uid));
    if (index !== -1) {
      // 从数组中删除文件
      fileList.value.splice(index, 1);
      // 更新组件值并触发变更事件
      emit('update:value', unref(fileList));
      emit('change', unref(fileList));
    }
  }

  /**
   * 文件预览处理函数
   * 根据文件类型选择不同的预览方式：
   * - 图片：使用图片预览组件
   * - 视频和压缩包：显示不支持预览的提示
   * - 其他类型：尝试在新窗口打开
   *
   * @param {Object} file - 要预览的文件对象
   */
  function handlePreview(file) {
    // 获取文件扩展名
    const extension = file.fileExtension || file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();

    // 处理视频文件 - 显示不支持预览提示
    if (videoTypeList.includes(extension)) {
      const errorMsg = t('component.upload.videoNoPreview');
      // 触发错误事件
      emit('error', new Error(errorMsg));
      // 根据配置决定是否显示内部错误消息
      if (props.showErrorMessage) {
        createMessage.error(errorMsg);
      }
      return;
    }

    // 处理压缩文件 - 显示不支持预览提示
    if (zipTypeList.includes(extension)) {
      createMessage.error(t('component.upload.zipNoPreview'));
      return;
    }

    // 处理图片文件 - 使用图片预览组件
    if (imgTypeList.includes(extension)) {
      // 构建完整图片URL
      const imageUrl = file.url ? (file.url.startsWith('http') ? file.url : apiUrl.value + file.url) : '';

      if (imageUrl) {
        // 调用图片预览组件
        createImgPreview({ imageList: [imageUrl] });
      }
      return;
    }

    // 处理其他类型文件 - 尝试在新窗口打开
    if (file.url) {
      const fullUrl = file.url.startsWith('http') ? file.url : apiUrl.value + file.url;
      window.open(fullUrl);
    }
  }

  /**
   * 执行批量上传处理
   * 将队列中的所有文件通过一个HTTP请求一起上传到服务器
   * 实现高效的多文件上传
   */
  async function executeBatchUpload() {
    // 如果队列为空，直接返回
    if (uploadQueue.value.length === 0) return;

    try {
      // 构建FormData对象，用于存放文件和参数
      const formData = new FormData();

      // 将所有待上传文件添加到FormData中，使用同一个字段名'files'
      uploadQueue.value.forEach((fileItem, index) => {
        formData.append(`files`, fileItem.file);
      });

      // 添加额外上传参数
      if (props.uploadParams) {
        Object.keys(props.uploadParams).forEach(key => {
          formData.append(key, props.uploadParams[key]);
        });
      }

      // 构建批量上传的URL
      const batchUploadUrl = props.customUploadUrl ? `${apiUrl.value}${props.customUploadUrl}` : `${apiUrl.value}/batchUploaderAttachment`;

      // 获取认证头信息
      const getHeaders = computed(() => ({ Authorization: getToken() as string }));

      // 创建XMLHttpRequest请求
      const xhr = new XMLHttpRequest();
      xhr.open('POST', batchUploadUrl);

      // 设置认证token
      const token = getHeaders.value.Authorization;
      if (token) {
        xhr.setRequestHeader('Authorization', token);
      }

      // 监听上传进度并同步更新所有文件的进度状态
      xhr.upload.addEventListener('progress', event => {
        if (event.lengthComputable) {
          const percent = Math.floor((event.loaded / event.total) * 100);

          // 更新所有队列中文件的进度
          uploadQueue.value.forEach(fileItem => {
            fileItem.progress = percent;

            // 调用文件的进度回调
            if (fileItem.onProgress) {
              fileItem.onProgress({ percent });
            }

            // 同步更新UI显示列表中的进度
            const index = fileList.value.findIndex(item => item.uid === fileItem.uid);
            if (index !== -1) {
              fileList.value[index].progress = percent;
            }
          });
        }
      });

      // 处理请求完成
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            // 解析响应数据
            const result = JSON.parse(xhr.response);
            if (result.code !== 200) {
              emit('error', result);
              throw new Error(result.msg || '批量上传失败');
            }

            // 处理返回的文件数据
            // 注意：后端应返回一个包含所有文件信息的数组
            const filesResult = Array.isArray(result.data) ? result.data : [result.data];

            // 更新每个上传文件的状态
            uploadQueue.value.forEach((fileItem, fileIndex) => {
              // 获取对应的文件结果信息
              const fileResult = filesResult[fileIndex] || {};

              // 在文件列表中查找并更新状态
              const index = fileList.value.findIndex(item => item.uid === fileItem.uid);
              if (index !== -1) {
                // 更新文件对象信息
                fileList.value[index] = {
                  ...fileList.value[index],
                  fileId: fileResult.fileId || fileResult.name || fileItem.uid,
                  url: fileResult.url || '',
                  status: 'done', // 标记为上传完成
                };

                // 触发文件的成功回调
                if (fileItem.onSuccess) {
                  fileItem.onSuccess({
                    ...result,
                    data: fileResult,
                  });
                }

                // 在循环中不触发success事件，将在所有文件处理完后统一触发一次
              }
            });

            // 更新UI显示
            emit('update:value', unref(fileList));
            emit('change', unref(fileList));

            // 所有文件上传完成后，只触发一次success事件
            emit('success', unref(fileList));

            // 清空上传队列，表示所有文件已处理
            uploadQueue.value = [];
            emit('loading', false); // 上传成功，关闭loading状态
            closeFullLoading(); // 关闭全局loading状态
          } catch (e) {
            // 处理响应解析失败或业务逻辑错误
            uploadQueue.value.forEach(fileItem => {
              // 将所有文件标记为上传失败
              const index = fileList.value.findIndex(item => item.uid === fileItem.uid);
              if (index !== -1) {
                fileList.value[index].status = 'error';
              }

              // 调用每个文件的错误回调
              if (fileItem.onError) {
                fileItem.onError(e);
              }
            });

            // 触发组件错误事件
            emit('error', e);

            // 根据配置决定是否显示内部错误消息
            if (props.showErrorMessage) {
              createMessage.error(e.message || t('component.upload.uploadError'));
            }

            // 清空上传队列
            uploadQueue.value = [];
            emit('loading', false); // 上传失败，关闭loading状态
            closeFullLoading(); // 关闭全局loading状态
          }
        } else {
          // 处理HTTP错误状态码
          uploadQueue.value.forEach(fileItem => {
            // 将所有文件标记为上传失败
            const index = fileList.value.findIndex(item => item.uid === fileItem.uid);
            if (index !== -1) {
              fileList.value[index].status = 'error';
            }

            // 调用每个文件的错误回调
            if (fileItem.onError) {
              fileItem.onError(new Error(xhr.statusText));
            }
          });

          // 触发组件错误事件
          emit('error', new Error(xhr.statusText));

          // 根据配置决定是否显示内部错误消息
          if (props.showErrorMessage) {
            createMessage.error(t('component.upload.uploadError'));
          }

          // 清空上传队列
          uploadQueue.value = [];
          emit('loading', false); // HTTP错误，关闭loading状态
          closeFullLoading(); // 关闭全局loading状态
        }
      };

      // 处理网络错误
      xhr.onerror = () => {
        // 网络错误时处理所有队列中的文件
        uploadQueue.value.forEach(fileItem => {
          // 将所有文件标记为上传失败
          const index = fileList.value.findIndex(item => item.uid === fileItem.uid);
          if (index !== -1) {
            fileList.value[index].status = 'error';
          }

          // 调用每个文件的错误回调
          if (fileItem.onError) {
            fileItem.onError(new Error('Network Error'));
          }
        });

        // 触发组件错误事件
        emit('error', new Error('Network Error'));

        // 根据配置决定是否显示内部错误消息
        if (props.showErrorMessage) {
          createMessage.error(t('component.upload.uploadError'));
        }

        // 清空上传队列
        uploadQueue.value = [];
        emit('loading', false); // 网络错误，关闭loading状态
        closeFullLoading(); // 关闭全局loading状态
      };

      // 发送包含所有文件的请求
      xhr.send(formData);
    } catch (error) {
      // 处理其他异常情况
      uploadQueue.value.forEach(fileItem => {
        // 将所有文件标记为上传失败
        const index = fileList.value.findIndex(item => item.uid === fileItem.uid);
        if (index !== -1) {
          fileList.value[index].status = 'error';
        }

        // 调用每个文件的错误回调
        if (fileItem.onError) {
          fileItem.onError(error);
        }
      });

      // 触发组件错误事件
      emit('error', error);

      // 根据配置决定是否显示内部错误消息
      if (props.showErrorMessage) {
        createMessage.error(t('component.upload.uploadError'));
      }

      // 清空上传队列
      uploadQueue.value = [];
      emit('loading', false); // 其他异常，关闭loading状态
      closeFullLoading(); // 关闭全局loading状态
    }
  }

  /**
   * 文件下载处理函数
   * 支持两种下载方式：
   * 1. 通过自定义下载接口获取下载地址
   * 2. 直接使用文件URL下载
   *
   * @param {Object} file - 要下载的文件对象
   */
  async function handleDownload(file) {
    // 检查文件是否有可下载的ID或URL
    if (!file.fileId && !file.url) return;

    try {
      if (props.customDownloadUrl) {
        // 方式1：使用自定义下载接口
        // 请求下载接口获取实际下载URL
        const response = await fetch(`${props.customDownloadUrl}?fileId=${file.fileId}`);
        const result = await response.json();

        // 检查接口返回结果
        if (result.code === 200 && result.data?.url) {
          // 使用工具函数下载文件
          downloadByUrl({ url: result.data.url, fileName: file.name });
        } else {
          // 下载接口返回错误
          createMessage.error(t('component.upload.downloadError'));
        }
      } else if (file.url) {
        // 方式2：直接使用文件URL下载
        // 构建完整的文件URL
        const url = file.url.startsWith('http') ? file.url : apiUrl.value + file.url;
        // 使用工具函数下载文件
        downloadByUrl({ url, fileName: file.name });
      }
    } catch (error) {
      // 处理下载过程中的错误
      console.error('Download error:', error);
      createMessage.error(t('component.upload.downloadError'));
    }
  }
</script>

<style lang="less" scoped>
  .custom-upload-container {
    .upload-tip-icon {
      margin-left: 8px;
      color: rgba(0, 0, 0, 0.45);
      cursor: pointer;
      transition: color 0.3s;
      &:hover {
        color: @primary-color;
      }
    }
    .upload-file__tip {
      line-height: 1.2;
      color: @text-color-secondary;
      margin-top: 5px;
      font-size: 12px;
      word-break: break-all;
    }

    .upload-file-list {
      .upload-file-list__item {
        font-size: 14px;
        color: @text-color-label;
        line-height: 26px;
        margin-top: 5px;
        position: relative;
        box-sizing: border-box;
        border-radius: 4px;
        width: 100%;
        a {
          color: inherit;
        }
        &:first-child {
          margin-top: 10px;
        }
        &:hover {
          background-color: @selected-hover-bg;
          .upload-file-list__item-name {
            color: @primary-color;
          }
        }
        .upload-file-list__item-name {
          margin-right: 70px;
          display: block;
          overflow: hidden;
          padding-left: 4px;
          text-overflow: ellipsis;
          white-space: nowrap;
          .anticon {
            height: 100%;
            margin-right: 5px;
            color: #909399;
          }
        }
        .upload-file-list__item-actions {
          position: absolute;
          right: 0;
          top: 0;

          .anticon {
            margin-right: 10px;
            cursor: pointer;
            opacity: 0.75;
            color: @text-color-label;
            &:last-child {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
</style>
