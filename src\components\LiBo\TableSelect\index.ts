export const liBoTableSelectProps = {
    value: [String, Number, Array] as PropType<String | number | string[] | number[] | [string, number][]>,
    modelId: { type: String, default: '' },
    hasPage: { type: Boolean, default: false },
    pageSize: { type: Number, default: 20 },
    allowClear: { type: Boolean, default: true },
    size: { type: String, default: 'default' },
    disabled: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    popupType: { type: String, default: 'dialog' },
    popupWidth: { type: String, default: '800px' },
    getDataChange: { type: Function, default: () => { } },
    getFieldDataSelect: { type: Function, default: () => { } },
    getConfigData: { type: Function, default: () => { } },
    title: { type: String, default: '' },
    dataSource: { type: Array<any>, default: () => [] },
    canSelected: { type: Boolean, default: false },
    showActionColumn: { type: Boolean, default: false },
    readonly: { type: Boolean, default: false },
    questions: { type: Array<any>, default: () => [] },
    getList: { type: Function, default: (props: any) => { } },
    columns: { type: Array<any>, default: () => [] },
    searchInfo: { type: Object, default: () => { } },
    defaultFullscreen: { type: Boolean, default: false },
    propTitle: { type: String, default: 'title' },
    submite: { type: Function, default: () => { } },
    selectedValues: { type: Array<any>, default: () => [] },
    getSelectedValue: { type: Function, default: () => { } },
};
