import { defHttp } from '@/utils/http/axios';

const newsApi = '/api/flowup/news';

// 获取新闻列表
export function getList(data) {
  return defHttp.post({ url: newsApi + '/getList', data });
}

// 新建新闻
export function create(data) {
  return defHttp.post({ url: newsApi, data });
}

// 修改新闻
export function update(data) {
  return defHttp.put({ url: newsApi + '/' + data.id, data });
}

// 获取新闻详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: newsApi + '/' + id });
}

// 获取新闻详情(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: newsApi + '/detail/' + id });
}

// 删除新闻
export function del(id) {
  return defHttp.delete({ url: newsApi + '/' + id });
}

// 批量删除新闻
export function batchDelete(data) {
  return defHttp.delete({ url: newsApi + '/batchRemove', data });
}

// 导出新闻数据
export function exportData(data) {
  return defHttp.post({ url: newsApi + '/export', data });
}

// 更新新闻发布状态
export function updateStatus(data) {
  return defHttp.post({ url: newsApi + '/updateStatus', data });
}

// 更新新闻置顶状态
export function updateTop(data) {
  return defHttp.post({ url: newsApi + '/updateTop', data });
}

// 增加阅读量
export function increaseViewCount(id) {
  return defHttp.post({ url: newsApi + '/' + id + '/view' });
}

// 点赞/取消点赞
export function toggleLike(id) {
  return defHttp.post({ url: newsApi + '/' + id + '/like' });
}

// 获取新闻评论列表
export function getComments(newsId, params) {
  return defHttp.get({ url: newsApi + '/' + newsId + '/comments', params });
}

// 添加评论
export function addComment(data) {
  return defHttp.post({ url: newsApi + '/comments', data });
}

// 删除评论
export function deleteComment(commentId) {
  return defHttp.delete({ url: newsApi + '/comments/' + commentId });
}

// 获取新闻分类统计
export function getCategoryStats() {
  return defHttp.get({ url: newsApi + '/stats/category' });
}

// 获取热门新闻
export function getHotNews(params) {
  return defHttp.get({ url: newsApi + '/hot', params });
}

// 获取推荐新闻
export function getRecommendNews(params) {
  return defHttp.get({ url: newsApi + '/recommend', params });
}

// 搜索新闻
export function searchNews(params) {
  return defHttp.get({ url: newsApi + '/search', params });
}
