const bpmnTask = 'bpmn:UserTask';
const bpmnStart = 'bpmn:StartEvent';
const bpmnEnd = 'bpmn:EndEvent';
const bpmnGateway = 'bpmn:InclusiveGateway';
const bpmnSequenceFlow = 'bpmn:SequenceFlow';
const bpmnTimer = 'bpmn:IntermediateCatchEvent';
const bpmnSubFlow = 'subFlow';
const bpmnInclusive = 'bpmn:InclusiveGateway';
const bpmnParallel = 'bpmn:ParallelGateway';
const bpmnExclusive = 'bpmn:ExclusiveGateway';
const bpmnLabel = 'label';
const bpmnIncoming = 'bpmn2:incoming';
const bpmnOutgoing = 'bpmn2:outgoing';
const typeStart = 'start';
const typeGateway = 'gateway';
const typeEnd = 'end';
const typeTask = 'approver';
const typeLabel = 'label';
const typeTimer = 'timer';
const typeSubFlow = 'subFlow';
const typeConfluence = 'confluence';

export {
  bpmnTask,
  bpmnStart,
  bpmnEnd,
  bpmnGateway,
  bpmnSequenceFlow,
  bpmnLabel,
  bpmnTimer,
  bpmnSubFlow,
  bpmnInclusive,
  bpmnParallel,
  bpmnExclusive,
  typeStart,
  typeGateway,
  typeEnd,
  typeTask,
  typeLabel,
  typeTimer,
  typeSubFlow,
  typeConfluence,
  bpmnIncoming,
  bpmnOutgoing,
};
