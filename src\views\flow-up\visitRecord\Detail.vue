<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="800px" :minHeight="100" :showOkBtn="false" defaultFullscreen>
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left" :labelCol="{ style: { width: '120px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="patientId">
              <template #label>随访患者 </template>
              <p class="link-text" @click="toPatientDetail(dataForm.patientId)">{{ dataForm.patientName }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="physicianId">
              <template #label>随访医师 </template>
              <p class="link-text" @click="toPhysicianDetail(dataForm.physicianId)">{{ dataForm.physicianName }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="visiteDate">
              <template #label>随访日期 </template> <p>{{ toDateString(dataForm.visiteDate, 'YYYY年MM月DD日') }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="situation">
              <template #label>随访情况 </template>
              <XundaTextarea readonly v-model:value="dataForm.situation" :style="{ width: '100%' }"> </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" v-if="dataForm.attention">
            <a-form-item name="attention">
              <template #label>注意事项 </template>
              <XundaTextarea readonly v-model:value="dataForm.attention" :style="{ width: '100%' }"> </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" v-if="dataForm.advice">
            <a-form-item name="advice">
              <template #label>随访建议 </template>
              <XundaTextarea readonly v-model:value="dataForm.advice" :style="{ width: '100%' }"> </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="advice">
              <template #label>附件 </template>
              <div v-if="dataForm.attachments && dataForm.attachments.length > 0">
                <Bit_KKFileView v-for="item in dataForm.attachments" :key="item.fileId" :file="item" />
              </div>
              <div v-else style="color: #999999">暂无附件 </div>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="selfCare">
              <template #label>生活自理能力情况 </template> <p>{{ dataForm.selfCare }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="medication">
              <template #label>规律服药情况 </template> <p>{{ dataForm.medication }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="pain">
              <template #label>疼痛情况 </template> <p>{{ dataForm.pain }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="dysfunction">
              <template #label>功能障碍情况 </template> <p>{{ dataForm.dysfunction }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="regularReview">
              <template #label>定期复查 </template> <p>{{ dataForm.regularReview }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="facialPalsy">
              <template #label>面瘫 </template> <p>{{ dataForm.facialPalsy }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="epilepsy">
              <template #label>癫痫 </template> <p>{{ dataForm.epilepsy }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="diplopia">
              <template #label>复视 </template> <p>{{ dataForm.diplopia }} </p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="prosthesis">
              <template #label>肢具佩戴情况 </template> <p>{{ dataForm.prosthesis }} </p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <RelationDetail ref="relationDetailRef" />

  <PatientDetail ref="patientDetailRef" />
  <PhysicianDetail ref="physicianDetailRef" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './api';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  // 有关联表单详情
  import RelationDetail from '@/views/common/dynamicModel/list/detail/index.vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { toDateString } from '@/utils/myUtil';
  import { useI18n } from '@/hooks/web/useI18n';
  import PatientDetail from '@/views/flow-up/patient/Detail.vue';
  import PhysicianDetail from '@/views/flow-up/physician/Detail.vue';
  import { XundaTextarea } from '@/components/Xunda';
  import Bit_KKFileView from '@/components/Bit/kkFileView';

  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    locationScope: any;
  }

  defineOptions({ name: 'Detail' });
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const patientDetailRef = ref<any>(null);
  const physicianDetailRef = ref<any>(null);
  const { t } = useI18n();
  const relationDetailRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    maskConfig: {},
    locationScope: {},
  });
  const { title, dataForm, maskConfig } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function toPatientDetail(id) {
    if (!id) return;
    patientDetailRef.value?.init({ id });
  }

  function toPhysicianDetail(id) {
    if (!id) return;
    physicianDetailRef.value?.init({ id });
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }
</script>
