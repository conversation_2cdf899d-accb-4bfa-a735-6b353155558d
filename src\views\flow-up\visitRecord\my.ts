import { FormSchema } from "@/components/Form";
import { useI18n } from "@/hooks/web/useI18n";
import { BasicColumn } from "@/components/Table/src/types/table";
import { toDateString } from "@/utils/myUtil";
const { t } = useI18n();


export const createFormSchenmas: FormSchema[] = [
    {
        field: 'picture',
        label: '照片',
        component: 'UploadImgSingle',
        componentProps: { type: 'annexpic', tipText: '上传照片' },
        rules: [{ required: true, trigger: 'change', message: '必填' }],
    },
    {
        field: 'name',
        label: '照片',
        component: 'Input',
        colProps: { sm: 24, span: 24 },
    },
];

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
    {
        field: 'physicianName',
        label: t('随访医师'),
        component: 'Input',
        componentProps: {
            submitOnPressEnter: true,
        },
    },

    {
        field: 'visiteDate',
        label: t('随访日期'),
        component: 'DatePicker',
        componentProps: {
            submitOnPressEnter: true,
        },
    },

];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
    {
        title: t('随访患者住院号'),
        dataIndex: 'patientAdmissionNo',
        width: 120,
    },
    {
        title: '随访患者',
        dataIndex: 'patientId',
        width: 120,
    },
    {
        title: '随访医师',
        dataIndex: 'physicianId',
        width: 120,
    }, {
        title: '出院日期',
        dataIndex: 'patientDischargeDate',
        width: 120,
        customRender({ record }) {
            return toDateString(record.visiteDate, 'YYYY年MM月DD日')
        }
    },
    {
        title: '随访日期',
        dataIndex: 'visiteDate',
        width: 120,
        customRender({ record }) {
            return toDateString(record.visiteDate, 'YYYY年MM月DD日')
        }
    },
    {
        title: '随访情况',
        dataIndex: 'situation',
        width: 120,
    },
    {
        title: '切口愈合情况',
        dataIndex: 'incisionHealing',
        width: 120,
    }, {
        title: '生活自理能力情况',
        dataIndex: 'selfCare',
        width: 120,
    },
    {
        title: '规律服药情况',
        dataIndex: 'medication',
        width: 120,
    },
    {
        title: '疼痛情况',
        dataIndex: 'pain',
        width: 120,
    },
    {
        title: '功能障碍情况',
        dataIndex: 'dysfunction',
        width: 120,
    }
    ,
    {
        title: '定期复查',
        dataIndex: 'regularReview',
        width: 120,
    },
    {
        title: '面瘫',
        dataIndex: 'facialPalsy',
        width: 120,
    },
    {
        title: '癫痫',
        dataIndex: 'epilepsy',
        width: 120,
    },
    {
        title: '复视',
        dataIndex: 'diplopia',
        width: 120,
    },
    {
        title: '肢具佩戴情况',
        dataIndex: 'prosthesis',
        width: 120,
    },
    {
        title: '备注',
        dataIndex: 'note',
        width: 120,
    }

];