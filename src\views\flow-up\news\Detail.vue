<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="新闻详情" :width="1000" :min-height="600" :footer="null">
    <div class="news-detail-container">
      <a-spin :spinning="loading">
        <div v-if="newsData" class="news-content">
          <!-- 新闻头部信息 -->
          <div class="news-header">
            <h1 class="news-title">{{ newsData.title }}</h1>
            <h2 v-if="newsData.subtitle" class="news-subtitle">{{ newsData.subtitle }}</h2>

            <div class="news-meta">
              <div class="meta-row">
                <span class="meta-item">
                  <Icon icon="ant-design:user-outlined" />
                  作者：{{ newsData.author }}
                </span>
                <span class="meta-item">
                  <Icon icon="ant-design:calendar-outlined" />
                  发布时间：{{ xundaUtils.toDateText(newsData.publishTime) }}
                </span>
                <span class="meta-item">
                  <Icon icon="ant-design:folder-outlined" />
                  分类：{{ getCategoryName(newsData.category) }}
                </span>
              </div>

              <div class="meta-row">
                <span class="meta-item">
                  <Icon icon="ant-design:eye-outlined" />
                  阅读量：{{ newsData.viewCount || 0 }}
                </span>
                <span class="meta-item">
                  <Icon icon="ant-design:like-outlined" />
                  点赞数：{{ newsData.likeCount || 0 }}
                </span>
                <span v-if="newsData.source" class="meta-item">
                  <Icon icon="ant-design:link-outlined" />
                  来源：{{ newsData.source }}
                </span>
              </div>

              <div v-if="newsData.tags" class="meta-row">
                <span class="meta-label">标签：</span>
                <a-tag v-for="tag in getTagList(newsData.tags)" :key="tag" color="blue">
                  {{ tag }}
                </a-tag>
              </div>

              <div class="meta-row">
                <a-tag :color="newsData.status === 1 ? 'green' : 'orange'">
                  {{ newsData.status === 1 ? '已发布' : '草稿' }}
                </a-tag>
                <a-tag v-if="newsData.isTop" color="red">置顶</a-tag>
                <a-tag :color="newsData.allowComment ? 'blue' : 'default'">
                  {{ newsData.allowComment ? '允许评论' : '禁止评论' }}
                </a-tag>
              </div>
            </div>
          </div>

          <!-- 封面图片 -->
          <div v-if="newsData.coverImage" class="news-cover">
            <img :src="newsData.coverImage" :alt="newsData.title" />
          </div>

          <!-- 新闻摘要 -->
          <div v-if="newsData.summary" class="news-summary">
            <h3>摘要</h3>
            <p>{{ newsData.summary }}</p>
          </div>

          <!-- 新闻内容 -->
          <div class="news-body">
            <h3>正文</h3>
            <div class="content-html" v-html="newsData.content"></div>
          </div>

          <!-- 操作按钮 -->
          <div class="news-actions">
            <a-space>
              <a-button type="primary" @click="handleEdit">
                <Icon icon="ant-design:edit-outlined" />
                编辑
              </a-button>
              <a-button :type="newsData.status === 1 ? 'default' : 'primary'" @click="handleToggleStatus">
                <Icon :icon="newsData.status === 1 ? 'ant-design:eye-invisible-outlined' : 'ant-design:eye-outlined'" />
                {{ newsData.status === 1 ? '取消发布' : '发布' }}
              </a-button>
              <a-button @click="handleToggleTop">
                <Icon :icon="newsData.isTop ? 'ant-design:vertical-align-bottom-outlined' : 'ant-design:vertical-align-top-outlined'" />
                {{ newsData.isTop ? '取消置顶' : '置顶' }}
              </a-button>
            </a-space>
          </div>
        </div>
      </a-spin>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Icon } from '@/components/Icon';
  import { getDetailInfo, updateStatus, updateTop } from './api';
  import { useMessage } from '@/hooks/web/useMessage';
  import { xundaUtils } from '@/utils/xunda';

  const emit = defineEmits(['register', 'edit', 'reload']);
  const { createMessage, createConfirm } = useMessage();

  const loading = ref(false);
  const newsData = ref<any>(null);

  const [registerModal, { closeModal }] = useModalInner(async data => {
    loading.value = true;
    try {
      const result = await getDetailInfo(data.id);
      newsData.value = result.data || result;
    } catch (error) {
      console.error('获取新闻详情失败:', error);
      createMessage.error('获取新闻详情失败');
    } finally {
      loading.value = false;
    }
  });

  // 获取分类名称
  function getCategoryName(category: string) {
    const categoryMap = {
      news: '文章',
      video: '视频',
    };
    return categoryMap[category] || category;
  }

  // 获取标签列表
  function getTagList(tags: string) {
    if (!tags) return [];
    return typeof tags === 'string' ? tags.split(',').filter(tag => tag.trim()) : tags;
  }

  // 编辑新闻
  function handleEdit() {
    emit('edit', newsData.value);
    closeModal();
  }

  // 切换发布状态
  function handleToggleStatus() {
    const newStatus = newsData.value.status === 1 ? 0 : 1;
    const statusText = newStatus === 1 ? '发布' : '取消发布';

    createConfirm({
      iconType: 'warning',
      title: '确认操作',
      content: `确定要${statusText}这篇新闻吗？`,
      onOk: async () => {
        try {
          await updateStatus({ id: newsData.value.id, status: newStatus });
          newsData.value.status = newStatus;
          createMessage.success('操作成功');
          emit('reload');
        } catch (error) {
          createMessage.error('操作失败');
        }
      },
    });
  }

  // 切换置顶状态
  function handleToggleTop() {
    const newTopStatus = !newsData.value.isTop;
    const topText = newTopStatus ? '置顶' : '取消置顶';

    createConfirm({
      iconType: 'warning',
      title: '确认操作',
      content: `确定要${topText}这篇新闻吗？`,
      onOk: async () => {
        try {
          await updateTop({ id: newsData.value.id, isTop: newTopStatus });
          newsData.value.isTop = newTopStatus;
          createMessage.success('操作成功');
          emit('reload');
        } catch (error) {
          createMessage.error('操作失败');
        }
      },
    });
  }
</script>

<style lang="less" scoped>
  .news-detail-container {
    padding: 20px;
  }

  .news-content {
    .news-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .news-title {
        font-size: 24px;
        font-weight: bold;
        color: #262626;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .news-subtitle {
        font-size: 16px;
        color: #595959;
        margin-bottom: 16px;
        font-weight: normal;
      }

      .news-meta {
        .meta-row {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          flex-wrap: wrap;
          gap: 16px;

          .meta-item {
            display: flex;
            align-items: center;
            color: #8c8c8c;
            font-size: 14px;

            .anticon {
              margin-right: 4px;
            }
          }

          .meta-label {
            color: #8c8c8c;
            font-size: 14px;
            margin-right: 8px;
          }
        }
      }
    }

    .news-cover {
      margin-bottom: 24px;
      text-align: center;

      img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .news-summary {
      margin-bottom: 24px;
      padding: 16px;
      background-color: #f9f9f9;
      border-radius: 8px;

      h3 {
        margin-bottom: 12px;
        color: #262626;
        font-size: 16px;
      }

      p {
        color: #595959;
        line-height: 1.6;
        margin: 0;
      }
    }

    .news-body {
      margin-bottom: 24px;

      h3 {
        margin-bottom: 16px;
        color: #262626;
        font-size: 16px;
      }

      .content-html {
        line-height: 1.8;
        color: #262626;

        :deep(img) {
          max-width: 100%;
          height: auto;
        }

        :deep(p) {
          margin-bottom: 16px;
        }

        :deep(h1, h2, h3, h4, h5, h6) {
          margin-top: 24px;
          margin-bottom: 16px;
        }
      }
    }

    .news-actions {
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }
</style>
