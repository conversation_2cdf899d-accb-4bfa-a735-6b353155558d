<template>
  <div class="file-viewer-container">
    <div class="file-container" ref="fileContainer">
      <div v-if="loading" class="loading-overlay">
        <a-spin :tip="loadingTip" />
      </div>

      <!-- 统一的文件信息展示 -->
      <div class="file-info">
        <span class="file-name" v-if="showTitle">
          <component :is="fileIcon" class="file-icon" />
          <span class="file-name-text" :title="getFileName">{{ getFileName }}</span>
          <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
          <span v-if="fileType" class="file-type-tag">{{ getFileTypeDisplayName(fileType) }}</span>
        </span>

        <!-- 图标操作模式 -->
        <div class="action-icons" v-if="operationMode === 'icon'">
          <!-- 预览图标，根据previewMode控制显示 -->
          <template v-if="!props.previewMode">
            <a-tooltip title="预览文件">
              <EyeOutlined class="action-icon preview-icon" @click="handlePreviewAction" />
            </a-tooltip>
          </template>
          <a-tooltip title="打印文件">
            <PrinterOutlined class="action-icon print-icon" @click="handlePrint" />
          </a-tooltip>
          <a-tooltip title="下载文件">
            <DownloadOutlined class="action-icon download-icon" @click="handleDownload" />
          </a-tooltip>
          <a-tooltip title="删除文件" v-if="props.allowDelete">
            <DeleteOutlined class="action-icon danger" @click="handleDelete" />
          </a-tooltip>
        </div>

        <!-- 按钮操作模式 -->
        <div class="action-buttons" v-if="operationMode === 'button'">
          <!-- 预览按钮，根据previewMode控制显示 -->
          <template v-if="!props.previewMode">
            <!-- 统一预览按钮 -->
            <a-button type="primary" @click="handlePreviewAction" class="action-btn preview-btn">
              <template #icon><EyeOutlined /></template>
              预览
            </a-button>
          </template>
          <a-button @click="handlePrint" class="action-btn print-btn">
            <template #icon><PrinterOutlined /></template>
            打印
          </a-button>
          <a-button type="primary" @click="handleDownload" class="action-btn download-btn">
            <template #icon><DownloadOutlined /></template>
            下载
          </a-button>
          <a-button danger @click="handleDelete" v-if="props.allowDelete" class="action-btn delete-btn">
            <template #icon><DeleteOutlined /></template>
            删除
          </a-button>
        </div>
      </div>

      <!-- 内联预览区域，根据previewMode控制显示 -->
      <div v-if="props.previewMode" class="inline-preview">
        <!-- PDF 预览 -->
        <div v-if="isPDF" class="pdf-preview" :style="previewHeightStyle">
          <iframe :src="iframeSrc" class="pdf-iframe" ref="fileIframe" @load="handleIframeLoad"></iframe>
        </div>

        <!-- 图片预览 -->
        <div v-else-if="isImage" class="image-preview" :style="previewHeightStyle">
          <a-image
            :src="fileUrl"
            @load="handleIframeLoad"
            :style="imageStyle"
            :preview="{
              visible: previewVisible,
              onVisibleChange: visible => {
                previewVisible = visible;
              },
            }" />
        </div>

        <!-- 其他预览内容 -->
        <div v-else class="other-preview" :style="previewHeightStyle">
          <!-- 文本预览 -->
          <pre v-if="isText" v-html="textContent" class="text-content"></pre>
          <!-- 视频预览 -->
          <video v-else-if="isVideo" controls :src="fileUrl" @loadeddata="handleIframeLoad" class="media-content"> 您的浏览器不支持视频播放 </video>
          <!-- 音频预览 -->
          <audio v-else-if="isAudio" controls :src="fileUrl" @loadeddata="handleIframeLoad" class="media-content"> 您的浏览器不支持音频播放 </audio>
          <!-- Office 文档预览 -->
          <iframe v-else-if="isOffice" :src="officeViewerUrl" class="office-iframe" @load="handleIframeLoad"></iframe>
        </div>
        <!-- 不支持的文件类型 -->
        <div v-if="fileType === FileType.UNKNOWN" class="no-preview">
          <FileExclamationOutlined class="no-preview-icon" />
          <p class="no-preview-text">此文件类型暂不支持预览</p>
          <a-button type="primary" size="small" @click="handleDownload" class="no-preview-download-btn">
            <template #icon><DownloadOutlined /></template>
            下载查看
          </a-button>
        </div>
      </div>

      <!-- 弹出式预览区域 -->
      <div v-show="pdfPreviewVisible || previewVisible" class="preview-content">
        <!-- PDF 预览 -->
        <div v-if="isPDF && pdfPreviewVisible" class="pdf-fullscreen-preview">
          <div class="preview-header">
            <span class="file-name">{{ getFileName }}</span>
            <div>
              <a-button @click="handlePrint">
                <template #icon><PrinterOutlined /></template>
                打印
              </a-button>
              <a-button type="text" @click="closePdfPreview">
                <template #icon><CloseOutlined /></template>
              </a-button>
            </div>
          </div>
          <iframe :src="iframeSrc" class="pdf-iframe" ref="fileIframe" @load="handleIframeLoad"></iframe>
        </div>

        <!-- 图片预览 -->
        <a-image
          v-else-if="isImage"
          :src="fileUrl"
          @load="handleIframeLoad"
          :style="imageStyle"
          :preview="{
            visible: previewVisible,
            onVisibleChange: visible => {
              previewVisible = visible;
            },
          }"
          style="display: none" />

        <!-- 其他预览内容 -->
        <div v-else-if="pdfPreviewVisible" class="fullscreen-preview">
          <div class="preview-header">
            <span class="file-name">{{ getFileName }}</span>
            <div>
              <a-button @click="handlePrint" v-if="isPrintable">
                <template #icon><PrinterOutlined /></template>
                打印
              </a-button>
              <a-button type="text" @click="closePdfPreview">
                <template #icon><CloseOutlined /></template>
              </a-button>
            </div>
          </div>
          <!-- 文本预览 -->
          <pre v-if="isText" v-html="textContent" class="text-content"></pre>
          <!-- 视频预览 -->
          <video v-else-if="isVideo" controls :src="fileUrl" @loadeddata="handleIframeLoad" class="media-content"> 您的浏览器不支持视频播放 </video>
          <!-- 音频预览 -->
          <audio v-else-if="isAudio" controls :src="fileUrl" @loadeddata="handleIframeLoad" class="media-content"> 您的浏览器不支持音频播放 </audio>
          <!-- Office 文档预览 -->
          <iframe v-else-if="isOffice" :src="officeViewerUrl" class="office-iframe" @load="handleIframeLoad"></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
  import {
    PrinterOutlined,
    DownloadOutlined,
    ZoomInOutlined,
    ZoomOutOutlined,
    RetweetOutlined,
    UndoOutlined,
    FileExclamationOutlined,
    EyeOutlined,
    CloseOutlined,
    FilePdfOutlined,
    FileImageOutlined,
    FileTextOutlined,
    FileWordOutlined,
    FileExcelOutlined,
    FilePptOutlined,
    PlaySquareOutlined,
    CustomerServiceOutlined,
    DeleteOutlined,
    SettingOutlined,
    FullscreenOutlined,
  } from '@ant-design/icons-vue';
  import { message as antMessage, Modal } from 'ant-design-vue';
  import { useLoading } from '@/components/Loading';
  import { useGlobSetting } from '@/hooks/setting';
  import { FileType, getFileTypeFromUrl, getFileTypeDisplayName, getFileTypeFromExtension } from './utils/filetype';

  // 自定义消息提示，延长显示时间
  const message = {
    success: (content, duration = 3) => antMessage.success({ content, duration }),
    error: (content, duration = 4) => antMessage.error({ content, duration }),
    warning: (content, duration = 3) => antMessage.warning({ content, duration }),
    info: (content, duration = 3) => antMessage.info({ content, duration }),
  };
  import { getDownloadUrl } from '@/api/basic/common';
  import { downloadByUrl } from '@/utils/file/download';
  import { fileItem } from '@/components/Xunda/Upload/src/props';

  const props = defineProps({
    type: { type: String, default: 'annex' },
    file: {
      type: Object as PropType<fileItem>,
      default: () => ({}),
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    allowDelete: {
      type: Boolean,
      default: false,
    },
    previewMode: {
      type: Boolean,
      default: false,
    },
    operationMode: {
      type: String,
      default: 'button',
    },
    height: {
      type: String,
      default: '100%',
    },
  });

  // 图片预览状态
  const previewVisible = ref(false);
  // PDF预览状态
  const pdfPreviewVisible = ref(false);

  // 获取文件名
  const getFileName = computed(() => {
    if (!props.file || !props.file.name) return '';
    return props.file.name.split('/').pop() || '未知文件';
  });

  // 计算预览区域的高度样式
  const previewHeightStyle = computed(() => {
    return { height: props.height };
  });

  // 显示PDF及其他文件类型预览
  const showPdfPreview = () => {
    // 如果文件类型未知或不支持预览
    if (fileType.value === FileType.UNKNOWN || !canPreview.value) {
      message.warning('该文件类型暂不支持预览，您可以下载后查看');
      return;
    }

    // 对于特定文件类型，可能需要先加载内容
    if (isText.value && !textContent.value) {
      loadTextContent()
        .then(() => {
          pdfPreviewVisible.value = true;
        })
        .catch(error => {
          console.error('加载文本内容失败:', error);
          message.error('文件预览失败，请尝试下载或稍后重试');
        });
    } else {
      // 其他文件类型直接显示预览
      pdfPreviewVisible.value = true;
    }

    console.log('显示预览窗口:', fileType.value);
  };

  // 关闭PDF预览
  const closePdfPreview = () => {
    pdfPreviewVisible.value = false;
  };

  const globSetting = useGlobSetting();
  const apiUrl = computed(() => globSetting.apiUrl);

  const fileType = ref<FileType>(FileType.UNKNOWN);
  const loading = ref(false);
  const scale = ref(1);
  const rotation = ref(0);
  const fileContainer = ref<HTMLElement | null>(null);
  const fileIframe = ref<HTMLIFrameElement | null>(null);
  const textContent = ref('');

  // 使用全屏loading效果
  const [openFullLoading, closeFullLoading] = useLoading({
    tip: 'Loading...',
  });
  const loadingTip = ref('文件加载中...');

  // 在 setup 中添加新的方法和变量
  const MIN_SCALE = 0.5;
  const MAX_SCALE = 3;
  const SCALE_STEP = 0.1;

  // 检测文件类型
  const detectFileType = async () => {
    if (!props.file || !props.file.name) {
      fileType.value = FileType.UNKNOWN;
      return;
    }

    loading.value = true;
    loadingTip.value = '检测文件类型...';

    try {
      // 先尝试从文件名判断类型
      const fileTypeFromName = getFileTypeFromExtension(props.file.name);
      if (fileTypeFromName !== FileType.UNKNOWN) {
        fileType.value = fileTypeFromName;
        console.log('通过文件名检测到文件类型:', fileType.value);
        return;
      }

      // 如果文件名判断失败，尝试从URL获取
      if (props.file.url) {
        fileType.value = await getFileTypeFromUrl(props.file.url);
        console.log('通过URL检测到文件类型:', fileType.value);
      } else {
        fileType.value = FileType.UNKNOWN;
        console.warn('无法获取文件URL，文件类型未知');
      }
      loading.value = false;
    } catch (error) {
      console.error('检测文件类型失败:', error);
      fileType.value = FileType.UNKNOWN;
    } finally {
      loading.value = false;
      loadingTip.value = '文件加载中...';
    }
  };

  // 文件类型判断
  const isPDF = computed(() => fileType.value === FileType.PDF);
  const isImage = computed(() => fileType.value === FileType.IMAGE);
  const isText = computed(() => fileType.value === FileType.TEXT);
  const isVideo = computed(() => fileType.value === FileType.VIDEO);
  const isAudio = computed(() => fileType.value === FileType.AUDIO);
  const isZip = computed(() => fileType.value === FileType.ZIP);
  const isOffice = computed(() => fileType.value === FileType.OFFICE);
  const isPrintable = computed(() => [FileType.PDF, FileType.TEXT, FileType.IMAGE].includes(fileType.value));
  // 判断文件是否可以预览
  const canPreview = computed(() => isPDF.value || isImage.value || isText.value || isVideo.value || isAudio.value || isOffice.value);

  // 文件类型显示名称
  const fileTypeDisplayName = computed(() => getFileTypeDisplayName(fileType.value));

  // 文件图标
  const fileIcon = computed(() => {
    if (isPDF.value) return FilePdfOutlined;
    if (isImage.value) return FileImageOutlined;
    if (isText.value) return FileTextOutlined;
    if (isVideo.value) return PlaySquareOutlined;
    if (isAudio.value) return CustomerServiceOutlined;
    if (isOffice.value) {
      const ext = props.file.name.toLowerCase().split('.').pop();
      switch (ext) {
        case 'doc':
        case 'docx':
          return FileWordOutlined;
        case 'xls':
        case 'xlsx':
          return FileExcelOutlined;
        case 'ppt':
        case 'pptx':
          return FilePptOutlined;
        default:
          return FileTextOutlined;
      }
    }
    return FileExclamationOutlined;
  });

  // 完整的文件URL
  const fileUrl = computed(() => {
    if (!props.file || !props.file.url) return '';
    return `${apiUrl.value}${props.file.url}`;
  });

  // PDF iframe src
  const iframeSrc = computed(() => {
    if (!isPDF.value || !fileUrl.value) return '';
    // page=1 设置初始页面
    // view=FitV 适应页面高度，宽度最大化
    // pagemode=none 不显示文档大纲
    // toolbar=0 不显示工具栏
    // zoom=page-width 确保宽度最大化
    return `${fileUrl.value}#page=1&view=FitV&pagemode=none&toolbar=0&zoom=page-width`;
  });

  // Office 文档预览 URL（使用微软 Office Online 或其他在线预览服务）
  const officeViewerUrl = computed(() => {
    if (!isOffice.value || !fileUrl.value) return '';
    // 这里使用示例 URL，实际使用时需要替换为真实的文档预览服务
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl.value)}`;
  });

  // 图片样式
  const imageStyle = computed(() => ({
    transform: `scale(${scale.value}) rotate(${rotation.value}deg)`,
  }));

  // 处理文件加载完成
  const handleIframeLoad = () => {
    loading.value = false;
  };

  // 打印功能
  const handlePrint = () => {
    if (!isPrintable.value) {
      message.warning('该文件类型不支持打印,请下载后打印');
      return;
    }

    try {
      // 显示全屏loading
      openFullLoading();

      // PDF文件使用当前已加载的iframe进行打印
      if (isPDF.value && fileIframe.value && pdfPreviewVisible.value) {
        // 直接使用已经加载的PDF iframe进行打印
        fileIframe.value.contentWindow?.print();
        // 关闭loading
        closeFullLoading();
        return;
      }

      // 非PDF文件或者PDF未预览时，创建临时iframe
      const printFrame = document.createElement('iframe');
      printFrame.style.position = 'fixed';
      printFrame.style.opacity = '0';
      printFrame.style.width = '0';
      printFrame.style.height = '0';
      printFrame.style.border = '0';
      document.body.appendChild(printFrame);

      // 设置 iframe 的 src
      printFrame.src = fileUrl.value;

      // 等待 iframe 加载完成后打印
      printFrame.onload = () => {
        try {
          // 关闭loading
          closeFullLoading();

          // 短暂延迟以确保内容完全渲染
          setTimeout(() => {
            printFrame.contentWindow?.print();
            // 监听打印操作完成后再移除iframe
            const pollPrinting = setInterval(() => {
              if (!printFrame.contentWindow?.document.queryCommandEnabled('print')) {
                clearInterval(pollPrinting);
                document.body.removeChild(printFrame);
              }
            }, 1000);
          }, 500);
        } catch (error) {
          console.error('打印错误:', error);
          message.error('打印失败，请检查文件状态或稍后重试');
          document.body.removeChild(printFrame);
          closeFullLoading();
        }
      };
    } catch (error) {
      console.error('打印设置错误:', error);
      message.error('打印功能初始化失败，请使用下载后打印');
      closeFullLoading();
    }
  };

  // 下载功能
  const handleDownload = () => {
    if (!props.file) return;
    if (!props.file.fileId) return;
    if (!props.file.url) return;

    // 显示全屏loading
    openFullLoading();

    let fileId = props.file.fileId;
    if (!fileId.includes(',')) {
      fileId = props.file.url?.substring(props.file.url?.lastIndexOf('/') + 1, props.file.url?.length);
    }
    const path = props.file.url?.substring(0, props.file.url?.lastIndexOf('/'));
    const fileType = props.type ?? path.substring(path.lastIndexOf('/') + 1, path.length);

    getDownloadUrl(fileType, fileId)
      .then(res => {
        downloadByUrl({ url: res.data.url, fileName: props.file.name });
        // 下载开始后关闭loading
        closeFullLoading();
      })
      .catch(error => {
        console.error('下载错误:', error);
        message.error('下载失败，请稍后重试');
        closeFullLoading();
      });
  };

  // 定义emit
  const emit = defineEmits(['delete']);

  // 删除功能
  const handleDelete = () => {
    if (!props.file || !props.file.fileId) return;
    // 弹出确认对话框
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${getFileName.value}" 吗？`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        // 触发删除事件，由父组件处理实际删除操作
        emit('delete', props.file);
      },
    });
  };

  // 根据文件类型处理预览操作
  const handlePreviewAction = () => {
    if (!canPreview.value) {
      message.warning('该文件类型暂不支持预览，请尝试下载后查看');
      return;
    }

    if (isImage.value) {
      previewVisible.value = true;
    } else {
      // PDF, Office, Text, Video, Audio 都使用同一种预览方式
      showPdfPreview();
    }
    // 预览文件已启动
  };

  // 处理鼠标滚轮事件
  const handleWheel = (e: WheelEvent) => {
    if (!(isPDF.value || isImage.value) || !e.ctrlKey) return;

    e.preventDefault();

    const delta = e.deltaY > 0 ? -1 : 1;
    const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale.value + delta * SCALE_STEP));

    if (newScale !== scale.value) {
      scale.value = newScale;
    }
  };

  // 加载文本内容
  const loadTextContent = async () => {
    if (!isText.value || !fileUrl.value) return;

    try {
      loading.value = true;
      const response = await fetch(fileUrl.value);
      const text = await response.text();
      textContent.value = text;
    } catch (error) {
      message.error('文本加载失败，可能是文件格式不兼容，请尝试下载查看');
    } finally {
      loading.value = false;
    }
  };

  // 监听URL变化
  watch(
    () => props.file,
    async newUrl => {
      if (newUrl) {
        loading.value = true;
        scale.value = 1;
        rotation.value = 0;

        // 检测文件类型
        await detectFileType();

        if (isText.value) {
          await loadTextContent();
        }
      }
    },
    { immediate: true },
  );

  function formatFileSize(size) {
    if (!size) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    let i = 0;
    while (size >= 1024 && i < units.length - 1) {
      size /= 1024;
      i++;
    }
    return `${size.toFixed(2)} ${units[i]}`;
  }

  // 组件挂载时初始化
  onMounted(() => {
    // 添加滚轮事件监听
    if (fileContainer.value) {
      fileContainer.value.addEventListener('wheel', handleWheel, { passive: false });
    }
  });

  // 组件卸载时清理
  onUnmounted(() => {
    // 移除滚轮事件监听
    if (fileContainer.value) {
      fileContainer.value.removeEventListener('wheel', handleWheel);
    }
  });
</script>

<style lang="less" scoped>
  .file-viewer-container {
    position: relative;
    background: #fff;

    .file-container {
      position: relative;

      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        z-index: 1;
      }

      .pdf-iframe,
      .office-iframe {
        width: 100%;
        height: 100%;
        border: none;
        min-height: 100%;
        display: block;
      }

      .image-preview {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        overflow: hidden; // 添加overflow控制

        img {
          max-width: 100%;
          max-height: 100%;
          transition: transform 0.2s ease;
          transform-origin: center center;
          will-change: transform; // 优化变换性能
        }
      }

      .text-preview {
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        height: 100%;
        overflow: auto;

        pre {
          margin: 0;
          white-space: pre-wrap;
          word-wrap: break-word;
          font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
      }

      .video-preview,
      .audio-preview {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        video,
        audio {
          max-width: 100%;
          max-height: 100%;
        }
      }

      .no-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 20px;
        background: #f8f8f8;
        border-radius: 8px;
        color: #666;
        font-size: 16px;

        .no-preview-icon {
          font-size: 48px;
          margin-bottom: 16px;
          color: #1890ff;
        }

        .no-preview-text {
          margin-bottom: 16px;
          font-size: 14px;
        }

        .no-preview-download-btn {
          margin-top: 8px;
          transition: all 0.3s;

          &:hover {
            transform: translateY(-2px);
          }
        }

        p {
          margin-bottom: 16px;
        }
      }
    }
  }

  // 统一的文件信息样式
  .file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;

    .file-name {
      font-size: 14px;
      color: #333;
      margin-right: 16px;
      margin-bottom: 8px;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        background: #e6f7ff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }

      .file-icon {
        margin-right: 10px;
        font-size: 18px;
        color: #1890ff;
      }

      .file-name-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
      }

      .file-size {
        margin: 0 10px;
        color: #666;
        font-size: 12px;
        background: #f0f0f0;
        padding: 2px 6px;
        border-radius: 4px;
      }

      .file-type-tag {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 4px;
        background: #1890ff;
        color: white;
        margin-left: 6px;
        font-weight: 500;
      }
    }

    .operation-mode {
      margin-right: 16px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .ant-btn {
        box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
      }

      .action-btn {
        font-size: 13px;
        border-radius: 4px;

        &.preview-btn {
          background-color: #1890ff;
          border-color: #1890ff;

          &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
          }
        }

        &.print-btn {
          color: #722ed1;
          border-color: #722ed1;

          &:hover {
            color: #9254de;
            border-color: #9254de;
            background-color: rgba(114, 46, 209, 0.1);
          }
        }

        &.download-btn {
          background-color: #52c41a;
          border-color: #52c41a;

          &:hover {
            background-color: #73d13d;
            border-color: #73d13d;
          }
        }

        &.delete-btn {
          &:hover {
            background-color: #ff4d4f;
            border-color: #ff4d4f;
          }
        }
      }
    }

    .action-icons {
      display: flex;
      gap: 16px;
      flex-shrink: 0;
      padding: 4px 8px;
      background: #f9f9f9;
      border-radius: 4px;

      .action-icon {
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s;
        color: #666;
        padding: 4px;
        border-radius: 50%;

        &.preview-icon {
          color: #1890ff;
        }

        &.print-icon {
          color: #722ed1;
        }

        &.download-icon {
          color: #52c41a;
        }

        &.danger {
          color: #ff4d4f;
        }

        &:hover {
          transform: scale(1.2);
          color: #1890ff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        &.preview-icon:hover {
          background-color: rgba(24, 144, 255, 0.1);
        }

        &.print-icon:hover {
          color: #9254de;
          background-color: rgba(114, 46, 209, 0.1);
        }

        &.download-icon:hover {
          background-color: rgba(82, 196, 26, 0.1);
          color: #73d13d;
        }

        &.danger:hover {
          color: #ff7875;
          background-color: rgba(255, 77, 79, 0.1);
          box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2);
        }
      }
    }
  }

  // 全屏预览样式
  .pdf-fullscreen-preview,
  .fullscreen-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 9999;
    display: flex;
    flex-direction: column;

    .preview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f5f5f5;
      border-bottom: 1px solid #e8e8e8;

      .file-name {
        font-size: 16px;
        color: #333;
        margin-right: 16px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .pdf-iframe,
    .office-iframe,
    .text-content,
    .media-content {
      flex: 1;
      width: 100%;
      height: calc(100vh - 52px); // 减去header高度
      border: none;
      margin: 0;
      padding: 20px;
      box-sizing: border-box;
    }

    .text-content {
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      overflow: auto;
    }

    .media-content {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #000;
      padding: 0;
    }
  }

  // 打印样式
  @media print {
    .toolbar,
    .resize-handle {
      display: none !important;
    }

    .file-container {
      padding: 0;
      overflow: visible;
      height: auto !important;
    }
  }

  // 添加内联预览样式
  .inline-preview {
    margin-top: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    overflow: hidden;

    .pdf-preview,
    .image-preview,
    .other-preview {
      width: 100%;
      /* 高度现在通过:style绑定控制，无需在CSS中设置 */
      min-height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fafafa;
      padding: 16px;

      .pdf-iframe,
      .office-iframe {
        width: 100%;
        height: 100%;
        min-height: 200px;
        border: none;
      }

      .text-content {
        width: 100%;
        height: 100%;
        padding: 16px;
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-family: Consolas, Monaco, 'Andale Mono', monospace;
        overflow: auto;
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
      }

      .media-content {
        max-width: 100%;
        max-height: 100%;
      }

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
  }
</style>
