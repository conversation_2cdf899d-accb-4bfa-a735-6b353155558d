.xunda-content-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  &.xunda-content-wrapper-form {
    flex-direction: column;
    background-color: @component-background;
    .xunda-content-wrapper-form-body {
      flex: 1;
      overflow: hidden;
    }
  }
  .xunda-content-wrapper-left {
    width: 220px;
    background-color: @component-background;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    border-radius: 8px;
    overflow: hidden;
  }
  .xunda-content-wrapper-center {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .xunda-content-wrapper-search-box {
      padding: 10px 10px 0;
      flex-shrink: 0;
      margin-bottom: 10px;
      background-color: @component-background;
      border-radius: 8px;
      overflow: hidden;
      .search-form {
        border-radius: 8px;
      }
    }
    .xunda-content-wrapper-content {
      border-radius: 8px;
      flex: 1;
      overflow: hidden;
      .search-form,
      .ant-table-wrapper {
        border-radius: 8px;
        overflow: hidden;
      }
    }
  }
}
.xunda-content-wrapper-tabs {
  & > .ant-tabs-nav {
    padding: 0 10px;
    margin-bottom: 0;
  }
  &.ant-tabs-card {
    height: 100%;
    & > .ant-tabs-nav {
      margin-bottom: 0;
      padding: 0;
      .ant-tabs-tab {
        border-top: none;
        &:first-child {
          border-left: none;
        }
      }
    }
  }
  &.tabs-contain {
    height: 100%;
    & > .ant-tabs-content-holder {
      height: 100%;
      & > .ant-tabs-content {
        height: 100%;
        overflow: hidden;
        & > .ant-tabs-tabpane {
          height: 100%;
          overflow: auto;
        }
      }
    }
  }
}
.ant-modal.transfer-modal {
  &.member-modal {
    .ant-modal-body > .scrollbar {
      padding: 10px;
      .transfer__body .transfer-pane {
        width: 100%;
      }
    }
  }
  .ant-modal-body {
    padding: 20px 10px;
    & > .scrollbar {
      padding: 20px 10px;
    }
    .scrollbar .scrollbar__wrap {
      margin-bottom: 0 !important;
    }
    .transfer__body {
      line-height: 32px;
      display: flex;
      justify-content: space-around;
      height: 400px;
    }
    .transfer-pane {
      width: 360px;
      &.left-pane {
        .selected-item {
          cursor: pointer;
          justify-content: flex-start;
        }
        .selected-item-user {
          cursor: pointer;
        }
      }
      .transfer-pane__tool {
        margin-bottom: 8px;
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .remove-all-btn {
          cursor: pointer;
          color: @error-color;
        }
      }
      .transfer-pane__body {
        position: relative;
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
        overflow-x: hidden;
        font-size: 14px;
        border: 1px solid @border-color-base;
        border-radius: var(--border-radius);
        &.transfer-pane__body-tab {
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .ant-tabs {
            .ant-tabs-nav {
              margin-bottom: 0;
            }
            .ant-tabs-nav-list {
              width: 100%;
            }
            .ant-tabs-tab {
              flex: auto;
              .ant-tabs-tab-btn {
                width: 100%;
                text-align: center;
              }
            }
          }
          .tree-main {
            flex: 1;
            overflow: auto;
          }
          .pane-tabs {
            flex-shrink: 0;
            &.pane-tabs-single {
              .ant-tabs-tab {
                width: 25%;
                flex: none;
              }
            }
            .ant-tabs-nav-operations {
              display: none !important;
            }
          }
        }
      }
      .ant-tree {
        .ant-tree-treenode {
          &.ant-tree-treenode-selected {
            background-color: @selected-hover-bg;
          }
        }
      }
      .custom-title {
        height: 38px;
        padding: 0 12px;
        line-height: 38px;
        font-size: 14px;
        border-bottom: 1px solid @border-color-base;
      }
      .selected-item {
        width: 100%;
        padding: 0px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.selected-item-user {
          .selected-item-main {
            border-bottom: 1px solid @border-color-base1;
            display: flex;
            align-items: center;
            height: 50px;
            width: 100%;
            box-sizing: border-box;
          }
          .selected-item-headIcon {
            flex-shrink: 0;
            &.icon {
              width: 36px;
              height: 36px;
              text-align: center;
              i {
                font-size: 22px;
                line-height: 36px;
              }
            }
          }
          .selected-item-text {
            min-width: 0;
            flex: 1;
            margin-left: 10px;
            .name {
              height: 20px;
              line-height: 20px;
              font-size: 14px;
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .organize {
              height: 17px;
              line-height: 17px;
              color: #999999;
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        &:hover {
          background-color: @selected-hover-bg;
        }
        span {
          max-width: 90%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .delete-btn:hover {
          color: @error-color;
          cursor: pointer;
        }
      }
      .selected-item-user-multiple {
        padding: 0 12px;
        position: relative;
        .selected-item-title {
          font-size: 14px;
          display: flex;
          align-items: center;
          span {
            padding-left: 6px;
          }
        }
        .selected-item-user {
          padding: 0 15px;
          &:last-child {
            border-bottom: 1px solid @border-color-base1;
            .selected-item-main {
              border-bottom: none;
            }
          }
          .selected-item-main {
            box-sizing: content-box;
          }
        }
        .selected-item-icon {
          width: 36px;
          height: 36px;
          background: linear-gradient(193deg, #a7d6ff 0%, #1990fa 100%);
          border-radius: 50%;
          line-height: 36px;
          color: #ffffff;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
}
.ant-modal.form-script-modal {
  .ant-modal-body {
    height: 70vh;
    & > .scrollbar {
      padding: 0;
      height: 100%;
      .scrollbar__view {
        height: 100%;
        overflow: hidden;
        & > div {
          height: 100%;
          overflow: hidden;
          max-height: 100% !important;
        }
        .form-script-modal-body {
          height: 100%;
          display: flex;
          overflow: hidden;
          padding: 20px;
          box-sizing: border-box;
          .left-board {
            height: 100%;
            width: 220px;
            flex-shrink: 0;
            margin-right: 10px;
            overflow: hidden auto;
          }
          .main-board {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            .main-board-editor {
              flex: 1;
              border: 1px solid @border-color-base1;
            }
            .main-board-tips {
              flex-shrink: 0;
              padding: 8px 16px;
              background-color: @primary-1;
              border-radius: 4px;
              border-left: 5px solid @primary-color;
              margin-top: 20px;

              p {
                line-height: 24px;
                color: @text-color-help-dark;

                span {
                  display: inline-block;
                  padding-right: 10px;
                }
              }
            }
          }
        }
      }
    }
  }
  &.btn-event-modal {
    .form-script-modal-body {
      padding: 20px 50px !important;
      overflow: auto !important;
      .ant-form-item {
        margin-bottom: 20px;
        &.ant-form-item-with-help {
          margin-bottom: 0;
        }
        .ant-form-item-explain {
          height: 20px;
          min-height: 20px;
        }
      }
      .tip {
        display: inline-block;
        vertical-align: middle;
        line-height: 32px;
        color: @text-color-secondary;
      }
    }
  }
}
.ant-modal.formula-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 20px;
    }
  }
  .formula-modal-body {
    .code-editor-area {
      height: 250px;
      border: 1px solid @border-color-base1;
      border-radius: 6px;
      margin-bottom: 10px;
      overflow: hidden;
    }
    .operation-area {
      height: 250px;
      display: flex;
      .area-item {
        height: 250px;
        border: 1px solid @border-color-base1;
        border-radius: 6px;
        width: 220px;
        overflow: hidden;
        &.formula-area {
          margin: 0 20px;
        }
        &.formula-desc-area {
          width: 280px;
          .area-content {
            padding: 10px;
          }
        }
        .area-title {
          padding: 0 10px;
          height: 34px;
          line-height: 34px;
          border-bottom: 1px solid @border-color-base1;
        }
        .area-content {
          height: 216px;
          overflow: hidden auto;
          .formula-desc-wrapper {
            color: #5e6d82;
            overflow-y: auto;
            & > li {
              margin-bottom: 4px;
              word-break: break-all;
              word-wrap: break-word;
              list-style-type: none;
              font-size: 12px;
              line-height: 18px;
            }
            .formula-name {
              color: @primary-color;
            }
          }
        }
      }
    }
  }
}
.xunda-common-page-header {
  height: 60px;
  border-bottom: 1px solid @border-color-base;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex-shrink: 0;
}
.ant-modal.xunda-full-modal {
  .ant-modal-header {
    padding: 0;
    .ant-modal-title {
      font-weight: normal;
    }
    .xunda-full-modal-header {
      padding: 0 20px;
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .header-title {
        height: 60px;
        width: 320px;
        display: flex;
        align-items: center;

        .header-logo {
          display: inline-block;
          width: auto;
          height: 60px;
          vertical-align: top;
          font-size: 30px;
        }
        .header-dot {
          display: inline-block;
          position: relative;
          margin: 0 10px;
          width: 7px;
          height: 7px;
          background: rgba(181, 215, 255, 0.4);
          border-radius: 50%;
          &::after {
            content: '';
            display: block;
            position: absolute;
            width: 3px;
            height: 3px;
            background: #6a9cfa;
            border-radius: 50%;
            left: 2px;
            top: 2px;
          }
        }
        .header-txt {
          line-height: 60px;
          display: inline-block;
          margin: 0;
          font-size: 16px;
          max-width: 150px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
      .options {
        width: 320px;
        justify-content: flex-end;
      }
      .header-steps {
        height: 40px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 20px;
        padding-top: 0 !important;
        overflow: hidden;
        &.ant-steps {
          width: auto;
          &.tab-steps {
            .ant-steps-item {
              margin: 0;
              .ant-steps-item-icon {
                display: none;
              }
              .ant-steps-item-container {
                padding: 0 20px;
              }
              &::after {
                display: none;
              }
            }
          }
          .ant-steps-item {
            width: auto;
            padding-inline-start: 0;
            margin: 0 16px;
            &:first-child {
              margin-left: 0;
            }
            &:last-child {
              margin-right: 0;
            }
            &::after {
              top: 20px;
              inset-inline-start: 110%;
            }
            &.ant-steps-item-active {
              &::before {
                display: none;
              }
              .ant-steps-item-container {
                background-color: #fff;
                border-radius: 20px;
              }
            }
            .ant-steps-item-container {
              margin-inline-start: 0;
              padding-bottom: 0;
              display: flex;
              align-items: center;
              padding: 0 15px;
            }
          }
        }
      }
    }
  }
  .ant-modal-body {
    overflow: hidden;
    background-color: @app-main-background;
    & > .scrollbar {
      padding: 10px;
      background-color: @app-main-background;
      & > .scrollbar__wrap {
        & > .scrollbar__view {
          height: 100%;
          overflow: hidden;
          & > div {
            height: 100% !important;
          }
        }
      }
    }
    .basic-content {
      height: 100%;
      overflow: hidden;
      .basic-form {
        height: 100%;
        padding: 20px;
        border-radius: 8px;
        background-color: @component-background;
        overflow-y: auto;
      }
    }
    .@{namespace}-basic-table {
      height: auto;
    }
  }
}

.ant-modal.xunda-list-modal {
  .ant-modal-body {
    height: 70vh;
    overflow: hidden;
    & > .scrollbar {
      padding: 0;
      & > .scrollbar__bar {
        display: none !important;
      }
      .scrollbar__wrap {
        .scrollbar__view {
          height: 100%;
          overflow: hidden;
          & > div {
            height: 100% !important;
            max-height: 100% !important;
          }
        }
      }
    }
  }
}
.ant-modal.xunda-add-modal {
  &.xunda-preview-modal {
    .add-main {
      .add-item {
        background: #f0fff7;
        &.add-item-left {
          background: #edfbfd;
          .add-icon {
            background: #d6f0ff;
            color: #3399fa;
          }
        }
        .add-icon {
          background: #ccf7e0;
          color: #36ac6c;
        }
        .add-txt {
          height: auto;
        }
      }
    }
  }
  .ant-modal-body {
    overflow: hidden;
    & > .scrollbar {
      padding: 20px;
      .scrollbar__bar {
        display: none !important;
      }
    }
  }
  .add-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .add-item {
      width: 270px;
      height: 136px;
      background: #fef3e6;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding-left: 20px;
      &:hover {
        opacity: 0.9;
      }
      &.add-item-left {
        background: #f1f5ff;
        .add-icon {
          background: #ccd9ff;
          color: #537eff;
        }
      }
      .add-icon {
        width: 56px;
        height: 56px;
        margin-right: 10px;
        background: #fce1bf;
        border-radius: 10px;
        color: #ea986c;
        flex-shrink: 0;
        font-size: 30px;
        line-height: 56px;
        text-align: center;
      }
      .add-txt {
        height: 56px;
        P {
          line-height: 28px;
        }
        .add-title {
          font-size: 18px;
        }
        .add-desc {
          color: @text-color-secondary;
          font-size: 12px;
        }
      }
    }
  }
}
.ant-modal.fixed-height-modal {
  .ant-modal-body {
    height: 70vh;
  }
}
.ant-modal.xunda-flow-list-modal {
  .ant-modal-body > .scrollbar {
    padding: 15px 0;
  }
  .template-search {
    padding: 0 40px 15px;
  }
  .template-list {
    height: 260px;
    .template-item {
      margin: 0 40px;
      height: 40px;
      border-radius: 4px;
      margin-bottom: 10px;
      line-height: 40px;
      padding: 0 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      background-color: @app-main-background;
      cursor: pointer;
      &:hover {
        color: @primary-color;
        background-color: @primary-2;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.ant-modal.xunda-flow-user-list-modal {
  .ant-modal-body > .scrollbar {
    padding: 20px 0;
  }
  .flow-user-list {
    height: 360px;
    .user-item-main {
      margin: 0 100px 15px;
      cursor: pointer;
      height: 70px;
      position: relative;
      border-radius: 4px;
      border: 1px solid @border-color-base1;
      display: flex;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;
      &.active {
        border: 1px solid @primary-color;
        box-shadow: 0 0 6px rgba(6, 58, 108, 0.26);
        .icon-checked {
          display: block;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
      .user-avatar {
        margin-right: 20px;
        flex-shrink: 0;
      }
      .user-text {
        min-width: 0;
        flex: 1;
        line-height: 24px;
        font-size: 14px;
        .user-organize {
          font-size: 12px;
          color: #999999;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
        }
      }
      .icon-checked {
        display: none;
        width: 18px;
        height: 18px;
        border: 18px solid @primary-color;
        border-left: 18px solid transparent !important;
        border-top: 18px solid transparent !important;
        border-bottom-right-radius: 4px;
        position: absolute;
        right: -1px;
        bottom: -1px;
        .anticon {
          position: absolute;
          top: 1px;
          left: 0;
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
}
.ant-modal.icon-modal {
  .icon-modal-title {
    display: flex;
    align-items: center;
    font-weight: normal;
    font-size: 18px;
    .ant-input-search {
      width: 300px;
      margin-left: 10px;
    }
  }
  .ant-modal-body {
    padding: 0 10px !important;
  }
  .main {
    height: 70vh;
    overflow: hidden;
    .ant-tabs,
    .ant-tabs-content {
      height: 100%;
    }
    .ant-tabs-nav {
      margin-bottom: 0 !important;
    }
    .icon-box-list {
      padding-bottom: 8px;
      > .ant-btn {
        margin: 8px 0 0 8px;
        width: 60px;
        height: 60px;
        padding: 0;
        line-height: 60px;
        text-align: center;
        transition: transform 300ms;
        i {
          display: inline-block;
          font-size: 24px;
          transition: 300ms;
        }
        &.is-active {
          color: @primary-color;
          border-color: @primary-color;
        }
        &:hover {
          i {
            transform: scale(1.8);
          }
        }
      }
    }
  }
}

.table-add-action {
  margin-top: 10px;
  border: 1px dashed @border-color-base;
  text-align: center;
  cursor: pointer;
}
.xunda-sub-table.xunda-basic-table-form-container {
  &.xunda-sub-table-full {
    .ant-form {
      width: 100% !important;
    }
  }
  position: relative;
  .ant-form {
    position: absolute;
    left: 0;
    top: 4px;
    z-index: 1;
    width: calc(100% - 80px) !important;
  }
}
.xunda-task-table.xunda-basic-table-form-container {
  .ant-form {
    width: calc(100% - 150px) !important;
  }
}
.ant-table {
  .ant-table-expanded-row-fixed {
    padding: 10px !important;
  }
  .ant-select {
    width: 100%;
  }
  .ant-btn.action-btn {
    padding: 0;
  }
  .ant-table__empty-text {
    line-height: 30px;
    padding: 30px 0;
  }
}

.xunda-sign,
.xunda-signature {
  .sign-tip {
    color: @primary-color;
  }
}

.average-tabs {
  &.ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
    .ant-tabs-nav-list {
      width: 100%;
    }
    .ant-tabs-tab {
      flex: auto;
      padding: 10px 0;
      .ant-tabs-tab-btn {
        width: 100%;
        text-align: center;
      }
    }
  }
}

.common-container-modal {
  .ant-modal-body {
    height: 70vh;
    overflow: hidden;
    .xunda-content-wrapper-left {
      margin-right: 0;
      border-right: 1px solid @border-color-base1;
    }
  }
}
.common-container {
  .disabled-select {
    .ant-select-selection-item {
      color: @primary-color !important;
      text-decoration: underline;
    }
  }
}
.required-sign {
  color: @error-color;
}
.common-container-drawer {
  .common-container-drawer-body {
    height: calc(100% - 60px);
  }
}
.ant-drawer {
  .common-container-drawer {
    .ant-drawer-body {
      padding: 0;
    }
  }
}
.dynamic-form {
  & > .ant-form {
    width: 100%;
  }
}
.word-form {
  margin-bottom: 20px;
  border-top: 1px solid @border-color-base;
  border-left: 1px solid @border-color-base;
  &.word-form-detail {
    .ant-col-item {
      & > .ant-form-item {
        .ant-form-item-label + .ant-form-item-control {
          padding: 8px 10px;
        }
      }
    }
  }
  .table-grid-box {
    margin-bottom: 0;
    td {
      padding-top: 0;
    }
  }
  .ant-row {
    margin: 0 !important;
  }
  .ant-col-item {
    padding: 0 !important;
    border-right: 1px solid @border-color-base;
    border-bottom: 1px solid @border-color-base;
    & > .ant-form-item {
      margin-bottom: 0;
      .ant-form-item-label {
        border-right: 1px solid @border-color-base;
        padding: 5px 0;
      }
      .ant-form-item-label + .ant-form-item-control {
        padding: 5px 0;
      }
      .ant-input,
      .ant-input-number,
      .ant-select,
      .ant-select-selector,
      .ant-input-affix-wrapper,
      .ant-picker {
        border: 0 !important;
        &:focus,
        &-focused {
          box-shadow: unset !important;
        }
      }
      .ant-select-focused {
        .ant-select-selector {
          box-shadow: unset !important;
        }
      }
      .tox-tinymce {
        border: none !important;
      }
    }
  }
  .xunda-basic-caption {
    border-bottom: 0 !important;
  }
  .ant-collapse {
    .ant-collapse-header {
      border-right: 1px solid @border-color-base;
      border-bottom: 1px solid @border-color-base;
    }
    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }

  .ant-card {
    border: none !important;
    &:hover {
      box-shadow: unset !important;
    }
    .ant-card-head {
      border-right: 1px solid @border-color-base;
    }
    .ant-card-body {
      padding: 0;
    }
  }
  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;
      border-right: 1px solid @border-color-base;
    }
  }
  .ant-steps {
    border-right: 1px solid @border-color-base;
  }
  .ant-rate,
  .ant-radio-group,
  .ant-checkbox-group,
  .upload-file-container,
  .upload-img-container {
    padding: 0 11px;
  }
  .ant-switch,
  .xunda-color-picker,
  .ant-slider {
    margin: 0 11px;
  }

  .ant-table {
    td {
      background-color: @component-background !important;
    }
    .ant-switch,
    .xunda-color-picker,
    .ant-slider {
      margin: 0;
    }
    .ant-table-tbody {
      .ant-table-placeholder,
      .ant-table-placeholder .ant-table-cell {
        border-right: none !important;
      }
    }
  }
  .table-add-action {
    border: none;
    margin-top: 0;
    // border-top: 1px solid @border-color-base1;
  }
}
.table-grid-box {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin-bottom: 18px;
  & > tbody {
    & > tr {
      & > td {
        border: var(--borderWidth) var(--borderType) var(--borderColor);
        background-color: var(--backgroundColor);
        overflow: hidden;
        height: 50px;
        padding: 18px 18px 0;
      }
    }
  }
}
.export-modal {
  &.ant-modal .ant-modal-body > .scrollbar {
    padding: 20px;
  }
  .ant-form-item {
    margin-bottom: 20px;
  }
  .export-line {
    line-height: 32px;
    border-bottom: 1px solid @border-color-base1;
    margin-bottom: 5px;
    .export-label {
      font-size: 18px;
      font-weight: bold;
      width: 100%;
      span {
        margin-left: 10px;
        font-size: 14px;
        font-weight: normal;
      }
    }
  }
  .ant-checkbox-wrapper {
    line-height: 32px;
  }
  .options-list {
    width: 100%;
    .ant-checkbox-wrapper {
      width: calc(33.33% - 8px);
      .ant-checkbox + span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & + .ant-checkbox-wrapper {
        margin-left: 0;
      }
    }
  }
  .footer-tip {
    float: left;
    line-height: 32px;
    color: @text-color-secondary;
  }
}
.ant-tabs.common-left-tabs {
  height: 100%;
  flex-shrink: 0;
  margin-right: 10px;
  & > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list {
    padding-top: 10px;
    width: 160px;
    & > .ant-tabs-tab {
      padding: 12px 24px;
      .icon-ym {
        font-size: 14px;
        margin-right: 8px;
      }
      & + .ant-tabs-tab {
        margin-top: 2px;
      }
      &.ant-tabs-tab-active {
        background: @primary-1;
      }
    }
  }
  .ant-tabs-content-holder {
    width: 0;
  }
}
.drag-handler {
  cursor: move;
  font-size: 20px;
}
.xunda-common-search-box {
  padding-top: 10px;
  flex-shrink: 0;
  position: relative;
  &.xunda-common-search-box-modal {
    padding: 10px 10px 0;
  }
  .xunda-common-search-box-right {
    position: absolute;
    right: 10px;
    top: 15px;
    .xunda-common-search-box-right-icon {
      margin-left: 10px;
      svg {
        width: 1.3em;
        height: 1.3em;
      }
    }
  }
}
.flow-com-title {
  height: 60px;
  line-height: 60px;
  text-align: center;
  position: relative;
  margin-bottom: 10px;

  h1 {
    font-size: 18px;
    margin: 0;
    font-weight: 700;
  }

  .number {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
}
.ant-modal.xunda-cron-modal {
  .ant-modal-body {
    padding: 0 10px;
  }
}
.xunda-import-modal {
  .import-main {
    margin: 20px 0;
    height: 480px;
    position: relative;
    .import-preview-table {
      .child-table-column .child-table__row td {
        flex: unset !important;
      }
    }
    .upload {
      display: flex;
      border: 1px solid @border-color-base;
      margin-bottom: 25px;
      &.error-show {
        margin-top: 10px;
        margin-bottom: 15px;
        .up_left {
          height: 120px;
        }
        .up_right {
          padding-top: 20px;
          font-size: 16px;
        }
      }
      .up_left {
        width: 126px;
        height: 140px;
        background: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        img {
          width: 80px;
          height: 80px;
        }
      }
      .up_right {
        margin-left: 30px;
        font-size: 14px;
        padding-top: 30px;
        flex: 1;
        .title {
          font-size: 18px;
          font-weight: bold;
        }
        .tip {
          margin: 15px 0;
          line-height: 16px;
          &.success-tip span {
            color: #67c23a;
          }
          &.error-tip span {
            color: @error-color;
          }
        }
        .ant-btn-link {
          padding: 0 !important;
        }
      }
      .upload-area {
        display: flex;
        padding-right: 200px;
        .ant-upload-select {
          margin-right: 50px;
          flex-shrink: 0;
        }
        .ant-upload-list {
          flex: 1;
        }
        .ant-upload-list-item:first-child {
          margin-top: 5px;
        }
      }
    }
    .success {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 110px;
      .success-title {
        margin: 20px 0;
        font-size: 18px;
        font-weight: bold;
      }
    }
    .conTips {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      .ant-btn-link {
        padding: 0 !important;
      }
    }
  }
}
.xunda-sso-modal {
  .scrollbar {
    padding: 0 !important;
    overflow: hidden;
    .scrollbar__view {
      height: 600px !important;
      & > div {
        max-height: 600px !important;
        height: 600px !important;
      }
    }
  }
}
.xunda-tenant-social-modal {
  .ant-modal-header {
    display: none;
  }
  .scrollbar {
    padding: 0 !important;
  }
  .other-main {
    width: 100%;
    padding-top: 10px;
    height: 500px;
    overflow: hidden;
    background: url('../assets/images/other-login-bg.png') no-repeat center;
    background-size: auto 100%;
    .other-title {
      display: flex;
      height: 50px;
      line-height: 50px;
      justify-content: center;
      align-items: center;
      .other-icon {
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 50%;
        border: 2px solid @primary-color;
        i {
          font-size: 16px;
          color: @primary-color;
        }
      }
      .other-text {
        height: 24px;
        line-height: 24px;
        font-size: 18px;
        font-weight: bold;
        margin: 0 5px;
      }
    }
    .other-body {
      padding: 20px;
      width: 100%;
      height: 440px;
      overflow-x: hidden;
      overflow-y: auto;
      .other-login-card {
        border-radius: 5px;
        border-left: 5px solid #9dc8fa;
        font-size: 12px;
        margin-bottom: 20px;
        background-color: @component-background;
        cursor: pointer;
        &:hover {
          border-color: @primary-color;
          i {
            color: #fff;
          }
        }
      }
      .other-login-des {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 12px;
        font-size: 14px;
        line-height: 20px;
        &.other-login-title {
          margin-top: 0;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 20px;
        }
      }
    }
  }
}
.xunda-release-modal {
  .ant-modal-body > .scrollbar {
    padding: 12px 55px !important;
  }
  .release-main {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .release-item {
      width: 220px;
    }
    .top-item {
      position: relative;
      width: 220px;
      height: 70px;
      cursor: pointer;
      border: 1px solid @border-color-base;
      border-radius: 6px;
      text-align: center;
      color: #606266;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
      &.active {
        border-color: @primary-color;
        color: @primary-color;
        box-shadow: 0 0 6px rgba(6, 58, 108, 0.1);
        .item-icon {
          border-color: @primary-color;
        }
        .icon-checked {
          display: block;
        }
      }
      .item-icon {
        display: inline-block;
        border: 1px solid #606266;
        text-align: center;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        font-size: 16px;
        margin-right: 10px;
        line-height: 26px;
        border-width: 1px;
      }
      .item-title {
        font-size: 16px;
        font-weight: 400;
      }
      .icon-checked {
        display: none;
        width: 18px;
        height: 18px;
        border: 18px solid @primary-color;
        border-left: 18px solid transparent !important;
        border-top: 18px solid transparent !important;
        border-bottom-right-radius: 4px;
        position: absolute;
        right: 0px;
        bottom: 0px;

        .anticon {
          position: absolute;
          top: 1px;
          left: 0;
          font-size: 14px;
          color: #fff;
        }
      }
    }
    .released {
      padding: 5px 10px;
      width: 220px;
      line-height: 30px;
      overflow: auto;
      border-radius: 4px;
      background: @app-main-background;
      max-height: 120px;
      min-height: 30px;
    }
  }
}
.link-text {
  color: @primary-color;
  cursor: pointer;
  user-select: none;
}
.xunda-basic-table {
  .link-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.child-table-box {
  padding: 0 !important;
  vertical-align: top !important;
}
.child-table-column {
  .child-table__row {
    background: transparent;
    border-bottom: 1px solid @border-color-base1;
    display: flex;
    align-items: center;
    min-height: 39px;

    td {
      border: none !important;
      flex-shrink: 0;
      flex: 1;
      &.td-flex-1 {
        flex: 1;
      }
      .cell {
        min-height: 23px !important;
        padding: 8px;
        white-space: pre-wrap;
        &.ellipsis {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &:last-child {
      border-bottom: none !important;
    }
  }
  .expand-more-btn {
    height: 39px;
    text-align: center;
    padding-top: 4px;
  }
}
.xunda-super-query-modal {
  .super-query-main {
    .query-noData {
      text-align: center;
      padding: 20px 0;
      .noData-img {
        width: 160px;
        margin-bottom: 10px;
        display: inline-block;
      }
      .noData-txt {
        color: @text-color-secondary;
      }
    }
  }
}
.xunda-condition-modal {
  .ant-modal-body > .scrollbar {
    padding: 20px !important;
  }
}
.condition-main {
  &.condition-main-bordered {
    border-radius: var(--border-radius);
    border: 1px solid @border-color-base;
    padding: 8px;
  }
  .condition-item {
    background-color: @app-content-background;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    overflow: hidden;
    .condition-item-title {
      font-size: 14px;
      padding: 0 10px;
      line-height: 40px;
      border-bottom: 1px solid @border-color-base;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .icon-ym {
        font-size: 14px;
        cursor: pointer;
      }
    }
    .condition-item-content {
      padding: 0 18px 10px;
      .condition-item-cap {
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }
  .condition-list {
    margin-bottom: 10px;
    overflow: hidden;
  }
  .icon-ym-btn-clearn {
    cursor: pointer;
    font-size: 18px;
    line-height: 32px;
    color: @error-color;
  }
  .ant-select,
  .ant-picker {
    width: 100%;
  }
  .xunda-color-picker {
    display: block !important;
  }
}
.plan-popover {
  .ant-popover-inner-content {
    padding: 0;
    width: 240px;
  }
  .plan-list {
    padding: 6px 0;
    max-height: 182px;
    overflow: auto;
    &-item {
      height: 34px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: @text-color-label;
      font-size: 14px;
      cursor: pointer;
      padding: 0 20px;
      .icon-ym-nav-close {
        font-size: 12px;
      }
      &:hover {
        background-color: @tree-hover-font-color;
      }
    }
    .plan-list-name {
      width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: @primary-color;
      cursor: pointer;
    }
    .icon-ym-nav-close:hover {
      color: @error-color;
    }
  }
  .noData-txt {
    height: 34px;
    color: @text-color-secondary;
    font-size: 14px;
    line-height: 34px;
    text-align: center;
  }
}
.edit-row-action {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  &:hover {
    .edit-row-index {
      display: none;
    }
    .ym-custom {
      display: block;
    }
  }
  .ym-custom {
    display: none;
    cursor: pointer;
  }
}
.short-link-wrapper {
  padding-bottom: 20px;
  &.short-link-wrapper-list {
    padding-bottom: 0;
  }
  .short-link-main {
    position: relative;
    width: 80%;
    height: 100%;
    margin: 0 auto;

    display: flex;
    flex-direction: column;
  }
  .short-link-header {
    height: 60px;
    line-height: 60px;
    text-align: center;
    background-color: @primary-color;
    color: #fff;
    font-size: 16px;
    flex-shrink: 0;
  }
  .icon-qrcode {
    cursor: pointer;
    position: absolute;
    right: -50px;
    top: 5px;
    width: 50px;
    height: 50px;
    font-size: 50px;
    line-height: 50px;
    color: @text-color-base;
  }
  .short-link-content {
    flex: 1;
    overflow: hidden;
    &.short-link-form {
      padding: 10px;
      overflow: auto;
      background-color: @component-background;
    }
  }
  .short-link-footer {
    flex-shrink: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 10px;
    background-color: @component-background;
  }
  .short-link-lock-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .short-link-lock-form {
    .ant-input-affix-wrapper {
      width: 220px;
    }
  }
}
.add-rules-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 0 20px 20px !important;
    }
  }
}
.parameter-box {
  display: flex;
  .icon-ym-btn-edit {
    color: @primary-color;
    cursor: pointer;
    font-size: 16px;
  }
  .icon-ym-delete {
    color: @error-color;
    cursor: pointer;
    font-size: 16px;
  }
  .left-pane {
    width: 350px;
    height: 420px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0 10px 18px 20px;
    .left-pane-list {
      border: 1px solid @border-color-base;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .header {
          background-color: @app-content-background;
          display: flex;
          &.detail-header {
            span {
              width: 170px;
            }
          }
          span {
            font-size: 14px;
            color: @text-color-label;
            padding: 8px;
            display: inline-block;
            line-height: 23px;
            width: 134px;
          }
          .operation {
            flex: 1;
          }
        }
        .search-box {
          border-bottom: 1px solid @border-color-base1;
          .search {
            margin: 10px;
            width: calc(100% - 20px);
          }
        }
      }
    }
    .table-add-action {
      margin-top: 0;
      border-width: 1px 0 0;
    }
  }
  .right-pane {
    flex: 1;
    display: flex;
    flex-direction: column;
    .msg-pane {
      margin: 0 0 18px 10px;
      flex: 1;
      display: flex;
      flex-direction: column;
      .list {
        flex: 1;
      }
    }
    .tox-tinymce {
      height: 370px !important;
    }
  }
}
.result-modal {
  .ant-modal-body > .scrollbar {
    padding: 0 !important;
  }
}
.text-primary {
  color: #188ae2 !important;
}
.text-success {
  color: #0eac5c !important;
}
.text-info {
  color: #35b8e0 !important;
}
.text-warning {
  color: #f9c851 !important;
}
.text-danger {
  color: #ff5b5b !important;
}
.text-pink {
  color: #ff8acc !important;
}
.text-purple {
  color: #5b69bc !important;
}
.text-inverse {
  color: #3b3e47 !important;
}
.text-dark {
  color: #282828 !important;
}
.text-white {
  color: #ffffff !important;
}
.text-color {
  color: #6a6c6f !important;
}
.text-grey {
  color: #999 !important;
}
.i-default {
  color: #6b7a99 !important;
}
.title-color {
  color: #475059 !important;
}
.ant-form-item-control {
  .xunda-color-picker {
    display: block !important;
  }
}
.ant-modal.xunda-modal-portal {
  .ant-modal-body {
    height: 60vh;
    > .scrollbar {
      padding: 0 !important;
    }
  }
}
.socials-list-justAuth {
  padding: 0 40px;
  .socials-item {
    padding: 10px 0;
    border-bottom: 1px solid @border-color-base;
  }
  .socials-item-main {
    display: flex;
    align-items: center;
    padding: 10px;
    height: 100px;
    &:hover {
      background-color: @selected-hover-bg;
    }
    .item-img {
      width: 80px;
      height: 80px;
      display: block;
      margin-right: 14px;
      flex-shrink: 0;
    }
    .item-txt {
      height: 80px;
      flex: 1;
      .item-name {
        line-height: 22px;
        font-size: 16px;
        margin-bottom: 16px;
        font-weight: 600;
      }
      .item-desc {
        color: @text-color-label;
        font-size: 12px;
        line-height: 18px;
      }
    }
    .item-btn {
      width: 70px;
      text-align: right;
      flex-shrink: 0;
    }
  }
}
.xunda-log-detail-modal {
  .scrollbar {
    padding: 0 !important;
  }
}
.portal-toggle-drawer {
  &.common-menus-drawer {
    .main .item .item-list {
      .item-list-item {
        height: 50px;
        line-height: 50px;
        padding: 0 30px;
        border-radius: 8px;
        overflow: hidden;
        color: @text-color-base;
        .icon-ym-header-star-fill {
          visibility: hidden;
          font-size: 14px;
          color: #f8af4b;
        }
        &:hover {
          background-color: @content-bg;
          .icon-ym-header-star-fill {
            visibility: visible;
          }
        }
      }
    }
  }
  .tool {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid @border-color-base1;
    .ant-input-search {
      .ant-input-affix-wrapper {
        border-radius: 0;
        border: none !important;
        &.ant-input-affix-wrapper-focused {
          box-shadow: unset;
        }
      }
      .ant-btn {
        border-radius: 0;
        border: none !important;
        height: 31px;
      }
    }
  }
  .main {
    padding: 10px 20px;
    height: calc(100% - 40px);
    overflow: auto;
    overflow-x: hidden;
    .item {
      .item-title {
        font-size: 12px;
        line-height: 30px;
        color: #999;
      }
      .item-list {
        font-size: 14px;
        color: #707070;
        .item-list-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 45px;
          cursor: pointer;
          .icon {
            font-size: 18px;
            color: #bdbdbd;
          }
          .item-list-item-name {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-right: 10px;
            .icon-ym {
              vertical-align: 0;
              margin-right: 10px;
              font-size: 14px;
            }
          }
          &.active .icon {
            color: @primary-color;
          }
        }
      }
    }
    .noData-txt {
      font-size: 14px;
      color: #909399;
      line-height: 20px;
      text-align: center;
      padding-top: 10px;
    }
  }
}
.input-table-footer-btn {
  padding-top: 10px;
  display: flex;
  flex-wrap: wrap;
}

.login-type-box {
  display: flex;
  align-items: center;
  .circle-box {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;
  }
  .circle-box-primary {
    background-color: @primary-color;
  }
  .circle-box-error {
    background-color: @error-color;
  }
}
.xunda-transfer-modal {
  .ant-modal-body {
    height: 320px;
  }
}
.custom-draggable-list {
  .custom-draggable-item {
    display: flex;
    align-items: center;
    border: 1px dashed @component-background;
    box-sizing: border-box;
    & + .custom-draggable-item {
      margin-top: 4px;
    }
    &.sortable-chosen {
      border: 1px dashed @primary-color;
    }
    .ant-input + .ant-input {
      margin-left: 4px;
    }
    .ant-input-group-addon {
      cursor: pointer;
      padding: 0;
      span {
        display: inline-block;
        line-height: 30px;
        padding: 0 11px;
      }
    }
    .custom-line-icon {
      line-height: 32px;
      font-size: 22px;
      padding: 0 4px;
      color: #606266;
      .icon-ym-btn-clearn {
        font-size: 18px;
      }
      .icon-ym-darg {
        font-size: 20px;
        line-height: 31px;
        display: inline-block;
        cursor: move;
      }
      &.option-drag {
        padding-left: 0;
      }
      &.close-btn {
        padding-right: 0;
      }
    }
    .custom-line-value {
      flex: 1;
      flex-shrink: 0;
      line-height: 32px;
      font-size: 14px;
      user-select: none;
      cursor: pointer;
    }
    .close-btn {
      cursor: pointer;
      color: @error-color;
    }
    .edit-btn {
      cursor: pointer;
      color: @primary-color;
    }
  }
  .add-btn .ant-btn {
    padding: 0;
  }
}
.custom-draggable-dropdown-menu {
  max-height: 400px;
  overflow: auto;
}
.interface-template-json-modal {
  .ant-modal-body {
    height: 40vh;
  }
}
.setting-drawer {
  .setting-drawer-item {
    margin-bottom: 32px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .setting-drawer-cap {
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 12px;
  }
  .setting-drawer-content {
    padding: 0 10px;
    &.bg-content {
      padding: 1px 16px;
      border-radius: var(--border-radius);
    }
  }
  .setting-type-picker {
    display: flex;
    justify-content: space-between;
    &.sysBg-type-picker {
      .type-picker__item {
        width: 77px;
        .type-picker__item-img {
          width: 77px;
        }
      }
    }
    .type-picker__item {
      width: 92px;
      cursor: pointer;
      &.type-picker__item--active {
        .type-picker__item-img {
          border-color: @primary-color;
        }
      }
      .type-picker__item-img {
        position: relative;
        border-radius: 9px;
        width: 92px;
        height: 57px;
        border: 1px solid #e5ebf5;
        box-shadow: 0px 5px 5px rgba(0, 0, 0, 0.16);
      }
      .type-picker__item-title {
        text-align: center;
        margin-top: 10px;
        font-size: 12px;
      }
      .icon-checked {
        position: absolute;
        width: 20px;
        height: 20px;
        right: -9px;
        bottom: -1px;
        background: @primary-color;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        color: #fff;
      }
    }
  }
}
.xunda-device-switch {
  display: flex;
  align-items: center;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.24);
  padding: 2px;
  overflow: hidden;
  .xunda-device-switch-item {
    height: 28px;
    line-height: 28px;
    padding: 0 16px;
    cursor: pointer;
    border-radius: 14px;
    &.xunda-device-switch-item--active {
      background-color: @component-background;
      .icon-ym {
        color: @primary-color;
      }
    }
    .icon-ym {
      font-size: 20px;
      color: @text-color-secondary;
    }
  }
}
.action-bar {
  position: relative;
  height: 42px;
  text-align: center;
  padding: 0 15px;
  box-sizing: border-box;
  border-bottom: 1px solid @border-color-base1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .action-bar-left,
  .action-bar-right {
    display: flex;
    align-items: center;
  }
  .action-bar-btn {
    margin-left: 10px;
    width: 30px !important;
    padding: 0 !important;
    text-align: center;
    i {
      font-size: 20px;
      &.icon-ym-redo,
      &.icon-ym-undo {
        font-size: 14px;
      }
    }
  }
  .action-bar-divider {
    height: 16px;
    margin: 0 6px;
    top: 1px;
    & + .action-bar-btn {
      margin-left: 0;
    }
  }
}
.xunda-version-popover {
  .ant-popover-inner-content {
    padding: 0;
    .version-list {
      height: 250px;
      overflow: auto;
      .version-item {
        display: flex;
        align-items: center;
        margin: 4px 6px;
        height: 36px;
        padding: 0 8px 0 18px;
        border-radius: 6px;
        cursor: pointer;
        &:hover {
          background-color: @selected-hover-bg;
          .version-delete i {
            display: block;
          }
        }
        .version-name {
          flex: 1;
          min-width: 0;
        }
        .version-state {
          width: 52px;
          height: 20px;
          border-radius: 10px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
        }
        .version-delete {
          width: 14px;
          margin-left: 32px;
          i {
            display: none;
            font-size: 14px;
          }
        }
      }
    }
  }
  .add-btn {
    border-top: 1px solid @border-color-base1;
    height: 44px;
    line-height: 44px;
    cursor: pointer;
    text-align: center;
  }
}
.version-tip {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  border-radius: 10px;
  padding: 0 18px;
  margin-left: 20px;
  background-color: @component-background;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  line-height: 32px;
  .icon {
    color: #fcaa28;
    padding-right: 5px;
  }
}
.current-version {
  display: flex;
  align-items: center;
  padding: 4px 10px;
}
.version-badge {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}
.xunda-child-list {
  .ant-collapse {
    background-color: @app-content-background;
  }
  .ant-collapse-borderless > .ant-collapse-item {
    border-bottom: unset;
  }
  .outer-collapse {
    & > .ant-collapse-item {
      & > .ant-collapse-header {
        border-bottom: 1px solid @border-color-base !important;
      }
      & > .ant-collapse-content > .ant-collapse-content-box {
        padding: 4px 0;
      }
    }
  }
  .ant-collapse-content-box {
    padding: 4px 10px;
  }
  .input-table-footer-btn {
    margin-bottom: 10px;
    padding-left: 16px;
  }
}
.field-table-box {
  border: 1px solid @border-color-base;
  border-radius: 4px;
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  overflow: hidden;
  .title {
    height: 38px;
    line-height: 38px;
    display: flex;
    color: @text-color-label;
    font-size: 14px;
    padding: 0 10px;
    flex-shrink: 0;
    justify-content: space-between;
    align-items: center;
  }
  .table-actions {
    flex-shrink: 0;
    border-top: 1px dashed @border-color-base;
    text-align: center;
  }
  .list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .icon-ym-btn-edit {
      color: @primary-color;
      cursor: pointer;
      font-size: 16px;
    }
    .icon-ym-delete {
      color: @error-color;
      cursor: pointer;
      font-size: 16px;
    }
  }
}
