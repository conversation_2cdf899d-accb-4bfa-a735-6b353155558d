<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" v-if="hasFormP('S_Name')">
            <a-form-item name="S_Name">
              <template #label>姓名 </template>
              <XundaInput
                v-model:value="dataForm.S_Name"
                placeholder="请输入"
                disabled
                detailed
                allowClear
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.S_Name">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" v-if="hasFormP('S_No')">
            <a-form-item name="S_No">
              <template #label>学号 </template>
              <XundaInput
                v-model:value="dataForm.S_No"
                placeholder="请输入"
                disabled
                detailed
                allowClear
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.S_No">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item mb-20px" v-if="hasFormP('tableField103')">
            <a-form-item>
              <XundaGroupTitle content="设计子表" :bordered="false" helpMessage="" />
              <a-table
                :data-source="dataForm.tableField103"
                :columns="demo_student_scoreColumns"
                size="small"
                :pagination="false"
                :scroll="{ x: 'max-content' }">
                <template #headerCell="{ column }">
                  <span class="required-sign" v-if="column.required">*</span>
                  {{ column.title }}
                  <BasicHelp :text="column.tipLabel" v-if="column.tipLabel && column.title" />
                </template>
                <template #bodyCell="{ column, index, record }">
                  <template v-if="column.key === 'index'">{{ index + 1 }}</template>
                  <template v-if="column.key === 'SS_Score'">
                    <XundaInputNumber
                      v-model:value="record.SS_Score"
                      placeholder="请输入"
                      disabled
                      detailed
                      :style="{ width: '100%' }"
                      :step="1"
                      :controls="false">
                    </XundaInputNumber>
                  </template>
                  <template v-if="column.key === 'SS_Course'">
                    <p class="link-text" @click="toDetail('608970807279885125', record.SS_Course_id)">{{ record.SS_Course }}</p>
                  </template>
                </template>
              </a-table>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <RelationDetail ref="relationDetailRef" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './helper/api';
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { reactive, toRefs, nextTick, ref, computed, unref, toRaw } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  // 有关联表单详情
  import RelationDetail from '@/views/common/dynamicModel/list/detail/index.vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { CaretRightOutlined } from '@ant-design/icons-vue';
  import { buildUUID } from '@/utils/uuid';
  import { useI18n } from '@/hooks/web/useI18n';

  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    locationScope: any;
  }

  defineOptions({ name: 'Detail' });
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
  const demo_student_scoreColumns: any[] = computed(() => {
    let list = [
      {
        title: '成绩',
        dataIndex: 'SS_Score',
        key: 'SS_Score',
        labelWidth: '',
        span: '24',
        tipLabel: '',
        required: false,
        style: { width: '100%' },
        align: 'left',
        fixed: false,
      },
      {
        title: '课程',
        dataIndex: 'SS_Course',
        key: 'SS_Course',
        labelWidth: '',
        span: '24',
        tipLabel: '',
        required: false,
        style: { width: '100%' },
        align: 'left',
        fixed: false,
      },
    ];
    const indexColumn = { title: '序号', showLabel: true, dataIndex: 'index', key: 'index', align: 'center', fixed: 'left', width: 50 };
    //子表复杂表头-处理
    let columnList = list;
    let complexHeaderList: any[] = [];
    if (complexHeaderList.length) {
      let childColumns: any[] = [];
      let firstChildColumns: string[] = [];
      for (let i = 0; i < complexHeaderList.length; i++) {
        const e = complexHeaderList[i];
        e.title = e.fullNameI18nCode ? t(e.fullNameI18nCode, e.fullName) : e.fullName;
        e.align = e.align;
        e.children = [];
        e.xundaKey = 'complexHeader';
        if (e.childColumns?.length) {
          childColumns.push(...e.childColumns);
          for (let k = 0; k < e.childColumns.length; k++) {
            const item = e.childColumns[k];
            for (let j = 0; j < list.length; j++) {
              const o = list[j];
              if (o.key == item && o.fixed !== 'left' && o.fixed !== 'right') e.children.push({ ...o });
            }
          }
        }
        if (e.children.length) firstChildColumns.push(e.children[0].key);
      }
      complexHeaderList = complexHeaderList.filter(o => o.children.length);
      let newList: any[] = [];
      for (let i = 0; i < list.length; i++) {
        const e = list[i];
        if (!childColumns.includes(e.key) || e.fixed === 'left' || e.fixed === 'right') {
          newList.push(e);
        } else {
          if (firstChildColumns.includes(e.key)) {
            const item = complexHeaderList.find(o => o.childColumns.includes(e.key));
            newList.push(item);
          }
        }
      }
      columnList = newList;
    }
    let columns = [indexColumn, ...columnList];
    const leftFixedList = columns.filter(o => o.fixed === 'left');
    const rightFixedList = columns.filter(o => o.fixed === 'right');
    const noFixedList = columns.filter(o => o.fixed !== 'left' && o.fixed !== 'right');
    return [...leftFixedList, ...noFixedList, ...rightFixedList];
  });

  const { t } = useI18n();
  const relationDetailRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    maskConfig: {
      S_Name: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      S_No: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
    },
    locationScope: {},
  });
  const { title, dataForm, maskConfig } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function toDetail(modelId, id) {
    if (!id) return;
    getConfigData(modelId).then(res => {
      if (!res.data || !res.data.formData) return;
      const formConf = JSON.parse(res.data.formData);
      formConf.popupType = 'general';
      const data = { id, formConf, modelId };
      relationDetailRef.value?.init(data);
    });
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }
</script>
