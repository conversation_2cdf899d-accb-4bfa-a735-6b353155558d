@import 'transition/index.less';
@import 'var/index.less';
@import 'public.less';
@import 'common.less';
@import 'commonFormWrapper.less';
@import 'ant/index.less';
@import './theme.less';
@import './themeColor.less';
@import './sysBg.less';

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;
  line-height: 1.5715;
  overflow: visible;
  overflow-x: hidden;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

body {
  color: rgba(0, 0, 0, 0.85);
}
[data-theme='dark'] body {
  color: #c9d1d9;
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none;
}
