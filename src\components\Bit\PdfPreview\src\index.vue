<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :keyboard="true" :default-fullscreen="true" :mask-closable="true" @cancel="handleClose" destroyOnClose>
    <!-- 主体内容区域 -->
    <div class="pdf-preview-content">
      <div v-if="fileUrl" class="iframe-container">
        <iframe :src="iframeSrc" class="pdf-preview-iframe" frameborder="0" allowfullscreen></iframe>
      </div>
      <div v-else class="pdf-preview-empty">
        <FileExclamationOutlined />
        <p>无法加载文件，请检查文件地址是否正确</p>
      </div>
    </div>

    <!-- 自定义底部区域 -->
    <template #footer>
      <div class="pdf-preview-footer">
        <div class="footer-actions">
          <a-button type="primary" @click="handlePrint">
            <template #icon><PrinterOutlined /></template>
            打印文件
          </a-button>
          <a-button type="primary" @click="handleDownload">
            <template #icon><DownloadOutlined /></template>
            下载文件
          </a-button>
          <a-button danger @click="handleClose">
            <template #icon><CloseOutlined /></template>
            关闭
          </a-button>
        </div>
      </div>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { DownloadOutlined, CloseOutlined, FileExclamationOutlined, PrinterOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { downloadByUrl } from '@/utils/file/download';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { useGlobSetting } from '@/hooks/setting';
  import { getDownloadUrl } from '@/api/basic/common';

  interface FileInfo {
    url: string;
    name?: string;
    fileSize?: Number;
    type?: string;
    fileId?: string;
    fileExtension?: string;
  }

  const emit = defineEmits(['close', 'download', 'print']);
  const { createMessage } = useMessage();

  const globSetting = useGlobSetting();
  const apiUrl = computed(() => globSetting.apiUrl);

  // 文件信息状态
  const fileInfo = ref<FileInfo>({ url: '', fileId: '', name: '', type: 'annex' });

  // Modal相关
  const [registerModal, { closeModal, setModalProps }] = useModalInner(init);

  // 处理PDF预览URL

  const fileUrl = computed(() => {
    const { url } = fileInfo.value;
    if (!url) return '';
    return url.startsWith('http') ? url : `${apiUrl.value}${url}`;
  });
  const iframeSrc = computed(() => {
    if (!fileUrl.value) return '';
    // 准备URL参数
    const params = [
      'pagemode=none', // 不显示侧边栏
      'view=Fit', // 适应一页完整显示
      'toolbar=0', // 显示工具栏
      'navpanes=1', // 显示导航面板
      'scrollbar=1', // 显示滚动条
      'print-media=1', // 使用打印媒体样式
    ];

    // 添加文件名参数，如果有的话
    if (fileInfo.value.name) {
      // 编码文件名以避免URL问题
      params.push('title=' + encodeURIComponent(fileInfo.value.name));
    }

    return fileUrl.value;
    // 组合参数
    return fileUrl.value + '#' + params.join('&');
  });

  // 关闭预览
  const handleClose = () => {
    closeModal();
    emit('close');
  };

  // 下载文件
  const handleDownload = e => {
    e?.stopPropagation();

    if (!fileInfo.value.url) {
      createMessage.error('文件地址不存在，无法下载');
      return;
    }

    try {
      getDownloadUrl(fileInfo.value.type as string, fileInfo.value.fileId).then(res => {
        downloadByUrl({
          url: res.data.url,
          fileName: fileInfo.value.name,
        });
        emit('download', fileInfo.value);
      });
    } catch (error) {
      console.error('下载文件出错:', error);
      createMessage.error('下载文件失败，请稍后重试');
    }
  };

  // 打印文件
  const handlePrint = () => {
    if (!fileUrl.value) {
      createMessage.error('文件地址不存在，无法打印');
      return;
    }

    try {
      // 获取当前页面上已经加载好的PDF的iframe
      const iframeElement = document.querySelector('.pdf-preview-iframe');

      if (iframeElement && iframeElement.contentWindow) {
        try {
          // 直接调用iframe中的打印功能，因为已经是打印布局
          iframeElement.contentWindow.focus(); // 确保iframe获得焦点
          iframeElement.contentWindow.print();

          // 触发打印完成事件
          emit('print', fileInfo.value);
          createMessage.success('正在打印文件');
        } catch (printError) {
          console.error('直接打印失败:', printError);
          createMessage.error('打印失败，浏览器可能阻止了此操作');

          // 如果直接打印失败，提示用户使用浏览器的打印功能
          createMessage.info('请尝试使用浏览器的打印功能(Ctrl+P)打印当前文档');
        }
      } else {
        // 如果找不到iframe或无法访问contentWindow，提示用户手动打印
        createMessage.warning('无法自动打印，请使用浏览器的打印功能(Ctrl+P)打印当前文档');
      }
    } catch (error) {
      console.error('打印准备出错:', error);
      createMessage.error('打印准备失败，请使用浏览器的打印功能');
    }
  };

  // 初始化并打开预览窗口
  function init(file: FileInfo) {
    fileInfo.value = { ...file };
    // 设置Modal标题
    setModalProps({
      title: file.name || '文件预览',
    });
  }

  // 暴露方法
  defineExpose({
    init,
    close: handleClose,
    print: handlePrint,
  });
</script>

<style lang="less" scoped>
  // 标题区域样式
  .pdf-preview-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .title-text {
      font-weight: 500;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .title-actions {
      margin-left: 16px;
    }
  }

  // 头部按钮区域
  .pdf-preview-header-btns {
    display: flex;
    gap: 8px;
    margin-right: 48px; // 为关闭按钮留出空间
  }

  // 内容区域
  .pdf-preview-content {
    position: relative;
    height: calc(100vh - 200px); // 减去头部和底部高度
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 旋转容器
  .iframe-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // iframe
  .pdf-preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: #f0f2f5;
    /* 添加打印友好样式 */
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    /* 确保内容不超出可视区域 */
    overflow: auto;
  }

  // 空状态
  .pdf-preview-empty {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: rgba(0, 0, 0, 0.85);

    .anticon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #bfbfbf;
    }

    p {
      font-size: 16px;
    }
  }
</style>
