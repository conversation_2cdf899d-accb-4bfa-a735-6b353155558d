import { Input, DatePicker } from 'ant-design-vue';

// xunda 组件
import { BasicCaption } from '@/components/Basic';
import { XundaAlert } from '@/components/Xunda/Alert';
import { XundaAreaSelect } from '@/components/Xunda/AreaSelect';
import { XundaAutoComplete } from '@/components/Xunda/AutoComplete';
import { XundaButton } from '@/components/Xunda/Button';
import { XundaCron } from '@/components/Xunda/Cron';
import { XundaCascader } from '@/components/Xunda/Cascader';
import { XundaCheckbox, XundaCheckboxSingle } from '@/components/Xunda/Checkbox';
import { XundaColorPicker } from '@/components/Xunda/ColorPicker';
import { XundaDatePicker, XundaDateRange, XundaTimePicker, XundaTimeRange } from '@/components/Xunda/DatePicker';
import { XundaDivider } from '@/components/Xunda/Divider';
import { XundaIconPicker } from '@/components/Xunda/IconPicker';
import { XundaInput, XundaTextarea, XundaI18nInput } from '@/components/Xunda/Input';
import { XundaInputNumber } from '@/components/Xunda/InputNumber';
import { XundaLink } from '@/components/Xunda/Link';
import { XundaOpenData } from '@/components/Xunda/OpenData';
import { XundaOrganizeSelect, XundaDepSelect, XundaPosSelect, XundaGroupSelect, XundaRoleSelect, XundaUserSelect, XundaUsersSelect } from '@/components/Xunda/Organize';
import { XundaQrcode } from '@/components/Xunda/Qrcode';
import { XundaBarcode } from '@/components/Xunda/Barcode';
import { XundaRadio } from '@/components/Xunda/Radio';
import { XundaSelect } from '@/components/Xunda/Select';
import { XundaRate } from '@/components/Xunda/Rate';
import { XundaSlider } from '@/components/Xunda/Slider';
import { XundaSign } from '@/components/Xunda/Sign';
import { XundaSignature } from '@/components/Xunda/Signature';
import { XundaSwitch } from '@/components/Xunda/Switch';
import { XundaText } from '@/components/Xunda/Text';
import { XundaTreeSelect } from '@/components/Xunda/TreeSelect';
import { XundaUploadFile, XundaUploadImg, XundaUploadImgSingle } from '@/components/Xunda/Upload';
import { Tinymce } from '@/components/Tinymce/index';
import { XundaRelationForm } from '@/components/Xunda/RelationForm';
import { XundaRelationFormAttr } from '@/components/Xunda/RelationFormAttr';
import { XundaPopupSelect, XundaPopupTableSelect } from '@/components/Xunda/PopupSelect';
import { XundaPopupAttr } from '@/components/Xunda/PopupAttr';
import { XundaNumberRange } from '@/components/Xunda/NumberRange';
import { XundaCalculate } from '@/components/Xunda/Calculate';
import { XundaInputTable } from '@/components/Xunda/InputTable';
import { XundaLocation } from '@/components/Xunda/Location';
import { XundaIframe } from '@/components/Xunda/Iframe';
import { XundaTextTag } from '@/components/Xunda/TextTag';

const XundaInputPassword = Input.Password;
XundaInputPassword.name = 'XundaInputPassword';
const XundaInputGroup = Input.Group;
XundaInputGroup.name = 'XundaInputGroup';
const XundaInputSearch = Input.Search;
XundaInputSearch.name = 'XundaInputSearch';
const XundaEditor = Tinymce;
XundaEditor.name = 'XundaEditor';
const XundaGroupTitle = BasicCaption;
XundaGroupTitle.name = 'XundaGroupTitle';
const XundaMonthPicker = DatePicker.MonthPicker;
XundaMonthPicker.name = 'XundaMonthPicker';
const XundaWeekPicker = DatePicker.WeekPicker;
XundaWeekPicker.name = 'XundaWeekPicker';

export {
  XundaAlert,
  XundaAreaSelect,
  XundaAutoComplete,
  XundaButton,
  XundaCron,
  XundaCascader,
  XundaColorPicker,
  XundaCheckbox,
  XundaCheckboxSingle,
  XundaDatePicker,
  XundaDateRange,
  XundaTimePicker,
  XundaTimeRange,
  XundaMonthPicker,
  XundaWeekPicker,
  XundaDivider,
  XundaEditor,
  XundaGroupTitle,
  XundaIconPicker,
  XundaInput,
  XundaInputPassword,
  XundaInputGroup,
  XundaInputSearch,
  XundaTextarea,
  XundaI18nInput,
  XundaInputNumber,
  XundaLink,
  XundaOpenData,
  XundaOrganizeSelect,
  XundaDepSelect,
  XundaPosSelect,
  XundaGroupSelect,
  XundaRoleSelect,
  XundaUserSelect,
  XundaUsersSelect,
  XundaQrcode,
  XundaBarcode,
  XundaRadio,
  XundaRate,
  XundaSelect,
  XundaSlider,
  XundaSign,
  XundaSignature,
  XundaSwitch,
  XundaText,
  XundaTreeSelect,
  XundaUploadFile,
  XundaUploadImg,
  XundaUploadImgSingle,
  XundaRelationForm,
  XundaRelationFormAttr,
  XundaPopupSelect,
  XundaPopupTableSelect,
  XundaPopupAttr,
  XundaNumberRange,
  XundaCalculate,
  XundaInputTable,
  XundaLocation,
  XundaIframe,
  XundaTextTag,
};
