<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    defaultFullscreen
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <a-col :span="24" class="ant-col-item">
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="coverImage" label="封面图片">
                  <XundaUploadImgSingle v-model:value="dataForm.coverImage" type="newscover" accept="image/*" tipText="上传封面图片" />
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="title" label="资讯标题">
                  <a-input v-model:value="dataForm.title" placeholder="请输入资讯标题" :maxlength="100" show-count />
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="subtitle" label="副标题">
                  <a-input v-model:value="dataForm.subtitle" placeholder="请输入副标题（可选）" :maxlength="200" show-count />
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="category" label="资讯分类">
                  <a-select v-model:value="dataForm.category" placeholder="请选择资讯分类">
                    <a-select-option value="news">文章</a-select-option>
                    <a-select-option value="video">视频</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="author" label="作者">
                  <a-input v-model:value="dataForm.author" placeholder="请输入作者姓名" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="tags" label="标签">
                  <a-select v-model:value="dataForm.tags" mode="tags" placeholder="请输入标签，按回车添加" :maxTagCount="5" />
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="summary" label="资讯摘要">
                  <a-textarea v-model:value="dataForm.summary" placeholder="请输入资讯摘要" :rows="3" :maxlength="500" show-count />
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item v-if="dataForm.category !== 'video'" name="content" label="资讯内容">
                  <Editor v-model:value="dataForm.content" :height="400" placeholder="请输入资讯内容" />
                </a-form-item>
                <a-form-item v-else name="videoContent" label="视频上传">
                  <BitFileUpload
                    v-model:value="dataForm.videoContent"
                    buttonText="上传文件(自定义接口)"
                    tipText="使用自定义接口上传文件"
                    :showFileList="false"
                    accept="video/*"
                    :fileSize="100"
                    sizeUnit="MB"
                    :multiple="false"
                    customUploadUrl="/api/flowup/basis/uploaderAttachment"
                    customDownloadUrl="/api/custom/download"
                    @success="handleSuccess"
                    :uploadParams="{ type: 'custom', module: 'registration' }" />
                  <div v-for="item in dataForm.videoContent" :key="item.id">
                    <BitFileViewer
                      v-model:value="dataForm.videoContent"
                      :file="item"
                      :min-height="800"
                      :showDownloadButton="true"
                      :allowDelete="true"
                      @delete="deleteFile"
                      :showContent="true" />
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="15">
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="isTop" label="是否置顶">
                  <a-switch v-model:checked="dataForm.isTop" checked-children="是" un-checked-children="否" />
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="status" label="发布状态">
                  <a-switch v-model:checked="dataForm.status" checked-children="发布" un-checked-children="草稿" />
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="allowComment" label="允许评论">
                  <a-switch v-model:checked="dataForm.allowComment" checked-children="是" un-checked-children="否" />
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item" v-if="dataForm.status">
                <a-form-item name="publishTime" label="发布时间">
                  <a-date-picker v-model:value="dataForm.publishTime" show-time format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发布时间" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { reactive, toRefs, nextTick, ref, unref, computed } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { XundaCheckboxSingle, XundaUploadFile, XundaUploadImgSingle } from '@/components/Xunda';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import type { FormInstance } from 'ant-design-vue';
  import { create, update, getDetailInfo } from './api';
  import { Tinymce as Editor } from '@/components/Tinymce';
  import BitFileUpload from '@/components/Bit/fileUpload';
  import BitFileViewer from '@/components/Bit/fileViewer';
  const { createMessage, createConfirm } = useMessage();

  const emit = defineEmits(['reload']);
  const { t } = useI18n();
  const formRef = ref<FormInstance>();
  const fileList2 = ref([]);

  interface State {
    dataForm: any;
    dataRule: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    activetab: string;
  }

  const state = reactive<State>({
    dataForm: {
      title: undefined,
      subtitle: undefined,
      category: undefined,
      coverImage: undefined,
      tags: [],
      summary: undefined,
      content: undefined,
      videoContent: undefined,
      author: undefined,
      isTop: false,
      status: false,
      allowComment: true,
      publishTime: undefined,
    },
    dataRule: {
      title: [
        { required: true, message: '请输入资讯标题' },
        { max: 100, message: '标题长度不能超过100个字符' },
      ],
      category: [{ required: true, message: '请选择资讯分类' }],
      coverImage: [{ required: true, message: '请上传封面图片' }],
      summary: [
        { required: true, message: '请输入资讯摘要' },
        { max: 500, message: '摘要长度不能超过500个字符' },
      ],
      content: [{ required: true, message: '请输入资讯内容' }],
      videoContent: [{ required: true, message: '请上传视频文件' }],
      author: [{ required: true, message: '请输入作者姓名' }],
    },
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    activetab: '1',
  });

  const { title, continueText, dataRule, dataForm, submitType, activetab } = toRefs(state);

  const [registerModal, { openModal, setModalProps }] = useModal();

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.activetab = '1';
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList || [];
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }

  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getDetailInfo(state.dataForm.id).then(res => {
        state.dataForm = res.data || {};
        // 处理标签数据
        if (state.dataForm.tags && typeof state.dataForm.tags === 'string') {
          state.dataForm.tags = state.dataForm.tags.split(',').filter(tag => tag.trim());
        }
        // 处理发布时间
        if (state.dataForm.publishTime) {
          state.dataForm.publishTime = new Date(state.dataForm.publishTime);
        }
        changeLoading(false);
      });
    } else {
      state.dataForm = {
        title: undefined,
        subtitle: undefined,
        category: undefined,
        coverImage: undefined,
        tags: [],
        summary: undefined,
        content: undefined,
        videoContent: undefined,
        author: undefined,
        isTop: false,
        status: false,
        allowComment: true,
        publishTime: new Date(),
      };
      changeLoading(false);
    }
  }

  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }

  async function handleSubmit() {
    try {
      const validate = await getForm().validate();
      if (!validate) return;
      setModalProps({ confirmLoading: true });

      const formData = { ...state.dataForm };

      // 处理标签数据
      if (formData.tags && Array.isArray(formData.tags)) {
        formData.tags = formData.tags.join(',');
      }

      // 处理发布时间
      if (formData.publishTime) {
        formData.publishTime = formData.publishTime.toISOString();
      }

      const formMethod = state.dataForm.id ? update : create;
      const result = await formMethod(formData);

      createMessage.success(result.msg);
      setModalProps({ confirmLoading: false });
      if (state.submitType == 1) {
        initData();
        state.isContinue = true;
      } else {
        setModalProps({ open: false });
        emit('reload');
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败，请检查输入信息');
      setModalProps({ confirmLoading: false });
    }
  }

  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }

  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }

  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      changeLoading(false);
    });
  }

  function setFormProps(data) {
    setModalProps(data);
  }

  function changeLoading(loading) {
    setModalProps({ loading });
  }

  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function handleSuccess(res) {
    console.log(res, '上传成功');
  }
  /**
   * 删除文件处理函数
   */
  function deleteFile(file: any): void {
    dataForm.value.videoContent = dataForm.value.videoContent.filter(item => item.fileId !== file.fileId);
  }
</script>

<style lang="less" scoped>
  .dynamic-form {
    :deep(.ant-form-item) {
      margin-bottom: 15px;
    }

    :deep(.ant-tabs-nav) {
      margin-bottom: 20px;
    }
  }

  :deep(.ant-form-item-label) {
    width: 100px !important;
  }

  .ant-col-item {
    margin-bottom: 10px;
  }
</style>
