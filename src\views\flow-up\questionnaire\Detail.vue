<template>

  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" defaultFullscreen
    :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left" disabled
        :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="title">
              <template #label>问卷标题 </template>
              <XundaInput v-model:value="dataForm.title" placeholder="请输入" disabled detailed allowClear
                :style="{ width: '100%' }" :maskConfig="maskConfig.title">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="physicianId">
              <template #label>问卷医师 </template>
              <p class="link-text" @click="toDetail('661876504619124229', dataForm.physicianId)">{{
                dataForm.physicianName
              }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="8" class="ant-col-item">
            <a-form-item name="recordId">
              <a-space>
                <p class="link-text" @click="distributeHandle(dataForm)">
                  发放问卷
                </p>
              </a-space>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="config">
              <QuestionnaireAnswer title="问卷配置" v-model:value="dataForm.config" :questions="dataForm.config">
              </QuestionnaireAnswer>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <PhysicianDetail ref="detailRef" />
  <LiBoTableSelect @register="registerLiBoTableSelect" :getList="getPatientList" :searchInfo="patientSearchInfo"
    :defaultFullscreen="false" :columns="patientColumns" @submit="HandleDistribute"
    :getSelectedValue="getSelectedValue" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
import { getDetailInfo, getPatientList, distribute, getquestionnairePatientList } from '@/views/flow-up/questionnaire/api';
import { patientColumns } from '@/views/flow-up/questionnaire/index';
import { reactive, toRefs, nextTick, ref } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
import { usePermission } from '@/hooks/web/usePermission';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import PhysicianDetail from '@/views/flow-up/physician/Detail.vue';
import QuestionnaireAnswer from '@/components/FormItem/questionnaire/QuestionnaireAnswer.vue';
import LiBoTableSelect from '@/components/LiBo/TableSelect/LiBoTableSelect.vue';
interface State {
  dataForm: any;
  title: string;
  maskConfig: any;
  locationScope: any;
  sexOptions: [];
  typeOptions: [];
}

defineOptions({ name: 'Detail' });
const emit = defineEmits(['reload']);
const { createMessage, createConfirm } = useMessage();
const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
const [registerLiBoTableSelect, { openModal: openLiBoTableSelectModel }] = useModal();

const { t } = useI18n();
const detailRef = ref<any>(null);
const visitRecordGridRef = ref<any>(null);
const state = reactive<State>({
  dataForm: {},
  title: t('common.detailText', '详情'),
  maskConfig: {
    name: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    idCard: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    admittingDiagnosis: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
  },
  locationScope: {
    addressDetail: [],
  },
  sexOptions: [],
  typeOptions: [],
});
const patientSearchInfo = {
  moduleId: '661952363199727173',
  superQueryJson: '',
  dataType: 0,
};
const { title, dataForm, maskConfig } = toRefs(state);
// 表单权限
const { hasFormP } = usePermission();

defineExpose({ init });

function init(data) {
  state.dataForm.id = data.id;
  openModal();
  nextTick(() => {
    setTimeout(initData, 0);
  });
}
function initData() {
  changeLoading(true);
  if (state.dataForm.id) {
    getData(state.dataForm.id);
  } else {
    closeModal();
  }
}
function getData(id) {
  getDetailInfo(id).then(res => {
    state.dataForm = res.data || {};
    nextTick(() => {
      changeLoading(false);
      getfsexOptions();
      getTypeOptions();
    });
  });
}

//数据选项--数据字典初始化方法
function getfsexOptions() {
  getDictionaryDataSelector('963255a34ea64a2584c5d1ba269c1fe6').then(res => {
    state.sexOptions = res.data.list;
  });
}

//数据选项--数据字典初始化方法
function getTypeOptions() {
  getDictionaryDataSelector('664466612501358213').then(res => {
    state.typeOptions = res.data.list;
  });
}

function toDetail(modelId, id) {
  if (!id) return;
  detailRef.value?.init({ id });
}
function setFormProps(data) {
  setModalProps(data);
}
function changeLoading(loading) {
  setFormProps({ loading });
}
function distributeHandle(record) {
  openLiBoTableSelectModel(true, record);
}
function HandleDistribute(id, ids, callBack) {
  const query = { questionnaireId: id, patientIds: ids };
  console.log(id, ids);
  distribute(query).then(res => {
    createMessage.success("发放问卷成功");
    if (callBack) callBack();
  });
}
function getSelectedValue(record) {
  return getquestionnairePatientList(record.id);
}
</script>

<style lang="less" scoped></style>