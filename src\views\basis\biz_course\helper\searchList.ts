const searchList = [
	{
		"useScan":false,
		"suffixIcon":"",
		"fullNameI18nCode":[
			""
		],
		"showCount":false,
		"__config__":{
			"formId":101,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"编号",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"bit_example_course",
			"renderKey":1735183086794,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"prop":"no",
		"xundaKey":"input",
		"__vModel__":"no",
		"searchMultiple":false,
		"disabled":false,
		"id":"no",
		"placeholder":"请输入",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		},
		"clearable":true,
		"searchType":2,
		"maxlength":null,
		"fullName":"编号",
		"label":"编号",
		"addonAfter":"",
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"isKeyword":false,
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"prefixIcon":"",
		"labelI18nCode":""
	},
	{
		"useScan":false,
		"suffixIcon":"",
		"fullNameI18nCode":[
			""
		],
		"showCount":false,
		"__config__":{
			"formId":102,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"名称",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"bit_example_course",
			"renderKey":1735183088075,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"prop":"name",
		"xundaKey":"input",
		"__vModel__":"name",
		"searchMultiple":false,
		"disabled":false,
		"id":"name",
		"placeholder":"请输入",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		},
		"clearable":true,
		"searchType":2,
		"maxlength":null,
		"fullName":"名称",
		"label":"名称",
		"addonAfter":"",
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"isKeyword":false,
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"prefixIcon":"",
		"labelI18nCode":""
	}
]
export default searchList