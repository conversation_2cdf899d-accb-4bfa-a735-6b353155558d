@prefix-cls: ~'@{namespace}-basic-process';
@line-color: #a9b4cd;

.flex-center() {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
}

.center-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .center-container-top {
    flex: 40px 0 0;
    display: flex;
    justify-content: space-between;
    .button-utils {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 8px;
      background-color: @app-content-background;
      border-radius: 20px;
      margin-left: 10px;
      box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);
      button {
        padding: 0 8px;
      }
      i {
        font-size: 20px;
      }
      .primary-color {
        color: @primary-color;
      }
    }
    .right-button-utils {
      margin: 0 20px 0 10px;
    }
  }
  .center-container-center {
    flex: 1;
  }
  .scale-slider {
    position: absolute;
    right: 0px;
    z-index: 199;
    display: flex;
    align-items: center;
    .num {
      display: inline-block;
      width: 40px;
      text-align: center;
      font-size: 14px;
    }
    .btn {
      display: inline-block;
      padding: 4px;
      color: @text-color-secondary;
      border: 1px solid @border-color-base;
      border-radius: 3px;
      background: @component-background;
      margin-left: 10px;
      margin-right: 10px;
      cursor: pointer;
    }
  }
}
.card-title {
  height: 43px;
  line-height: 43px;
  padding: 0 12px;
  font-weight: 600;
  overflow: hidden;
  border-bottom: 1px solid @border-color-base1;
}
.@{prefix-cls} {
  display: flex;
  height: 100%;
}

.tips {
  position: absolute;
  left: 20px;
  z-index: 199;
  text-align: left;
  .tips-item {
    line-height: 20px;
    font-size: 14px;
    display: inline-block;
    margin-right: 30px;
    .icon {
      font-size: 20px;
      margin-right: 5px;
      color: #c6c9ce;
      &.past {
        color: #4ed587;
      }
      &.curr {
        color: #a6def8 !important;
      }
    }
  }
}
.xunda-shortcut-key-popover {
  display: flex;
  padding: 20px;
  .left,
  .right {
    width: 180px;
    .item-contain {
      &:nth-child(n + 2) {
        margin-top: 18px;
      }
      .title {
        font-size: 12px;
        color: @text-color-secondary;
        margin-bottom: 6px;
      }
      .items {
        display: flex;
        margin-top: 4px;
        .items-title {
          flex: 1;
          font-size: 13px;
        }
        .items-keys {
          div {
            border: 1px solid #ebecee;
            border-radius: 3px;
            display: inline-block;
            font-size: 12px;
            min-width: 22px;
            padding: 2px 4px;
            text-align: center;
            margin-left: 4px;
          }
        }
      }
    }
  }
}
.xunda-command-history-popover {
  width: 250px;
  height: 320px;
  display: flex;
  flex-direction: column;
  padding: 20px 10px;
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
    i {
      font-size: 16px;
      margin: 0 10px;
    }
  }
  .contain {
    flex: 1;
    overflow: auto;
    .item {
      display: flex;
      align-items: center;
      line-height: 36px;
      border-radius: 7px;
      cursor: pointer;
      &:hover {
        background: rgba(237, 240, 244, 0.39);
      }
      &.current-item {
        color: @primary-color;
      }
      &.past-item {
        color: #a5a5a5;
      }
      i {
        margin: 0 8px 0 10px;
        font-size: 14px;
      }
    }
  }
}

.xunda-flow-common-popover {
  .ant-popover-inner {
    border-radius: 8px;
    overflow: hidden;
    .ant-popover-inner-content {
      padding: unset !important;
    }
  }
}
