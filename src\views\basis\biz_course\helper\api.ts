import { defHttp } from '@/utils/http/axios';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: '/api/example/basis/course/getList', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: '/api/example/basis/course', data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: '/api/example/basis/course/' + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: '/api/example/basis/course/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: '/api/example/basis/course/detail/' + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: '/api/example/basis/course/' + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: '/api/example/basis/course/batchRemove', data });
}
// 导出
export function exportData(data) {
  data.moduleId = '624219679564529989';
  return defHttp.post({ url: '/api/example/basis/student/export', data });
  return defHttp.post({ url: '/api/example/basis/course/export', data });
}
