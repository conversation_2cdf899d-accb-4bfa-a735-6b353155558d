<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="propTitle"
    :width="800"
    @ok="handleSubmit"
    :default-fullscreen="defaultFullscreen"
    class="transfer-modal"
    :can-fullscreen="true"
    v-model:open="visible">
    <div class="transfer__body">
      <div class="transfer-pane left-pane" style="width: 50%">
        <div class="transfer-pane__tool">
          <a-input-search :placeholder="t('common.enterKeyword')" allowClear v-model:value="keyword" @search="handleSearch" />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <BasicTable @register="registerTable" ref="tableRef">
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'action'">
                <!-- <TableAction :actions="[
                  {
                    label: t('选择'),
                    onClick: updateHandle.bind(null, record),
                  },
                ]" /> -->
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <div class="transfer-pane right-pane" style="width: 50%">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <div v-for="(item, i) in selectedData" :key="i" class="selected-item">
              <div class="selected-item-text">
                <p class="name"> </p><p>姓名:{{ item.name }}</p> <p>住院号:{{ item.admissionNo }} </p></div
              >
              <delete-outlined class="delete-btn" @click="removeData(i)" />
            </div>
            <xunda-empty v-if="!selectedData?.length" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { toRefs, ref, reactive, unref } from 'vue';
  import { DeleteOutlined } from '@ant-design/icons-vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { ScrollContainer } from '@/components/Container';
  import { saveInterfaceList } from '@/api/systemData/interfaceOauth';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import { liBoTableSelectProps } from '.';

  interface State {
    treeData: any[];
    dataForm: any;
    keyword: string;
    selectedData: any[];
    selectedDataIds: any[];
  }
  defineOptions({ name: 'LiBoTableSelect', inheritAttrs: false });

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const tableRef = ref<Nullable<TableActionType>>(null);
  const [registerModal, { changeLoading, changeOkLoading, closeModal }] = useModalInner(init);
  const state = reactive<State>({
    treeData: [],
    dataForm: {
      interfaceIdentId: '',
      dataInterfaceIds: [],
    },
    keyword: '',
    selectedData: [],
    selectedDataIds: [],
  });
  const { treeData, selectedData, selectedDataIds, keyword } = toRefs(state);

  const props = defineProps(liBoTableSelectProps);
  const { getList, columns, searchInfo, defaultFullscreen, propTitle, selectedValues, getSelectedValue } = props;

  const visible = ref(false);

  const emit = defineEmits(['register', 'submit']);

  const [registerTable, { reload, setLoading, getFetchParams, getSelectRows, getSelectRowKeys, redoHeight, clearSelectedRowKeys, setSelectedRowKeys }] =
    useTable({
      api: getList as any,
      columns: columns,
      searchInfo: searchInfo,
      immediate: false,
      clickToRowSelect: false,
      afterFetch: data => {
        const list = data.map(o => ({
          ...o,
          // ...state.expandObj,
        }));
        const arr = state.selectedData.map(o => o.id);
        setSelectedRowKeys(arr);
        return list;
      },
      // actionColumn: {
      //   width: 50,
      //   title: t('common.actionText', '操作'),
      //   dataIndex: 'action',
      //   fixed: 'right',
      // },
      rowSelection: {
        type: 'checkbox',
        hideSelectAll: false,
        onSelect: handleSelect,
        onChange: handleSelectChange,
        onSelectAll: handleSelectAll,
        getCheckboxProps: record => ({
          disabled: record.top,
        }),
      },
    });

  function init(data) {
    console.log(data);
    state.dataForm.interfaceIdentId = data.id;
    state.dataForm.dataInterfaceIds = [];
    state.treeData = [];
    state.selectedData = [];
    state.keyword = '';
    visible.value = true;
    initData(data);
  }
  function initData(data) {
    changeLoading(true);
    if (getSelectedValue) {
      getSelectedValue(data).then(res => {
        state.selectedData = res.data.list;
        const arr = state.selectedData.map(o => o.id);
        console.log(arr);
        setSelectedRowKeys(arr);
        changeLoading(false);
        if (getList) {
          reload();
        }
      });
    }
  }

  function handleSelect(record, selected) {
    if (selected) {
      selectedData.value.push(record);
    } else [(selectedData.value = selectedData.value.filter(o => o.id !== record.id))];
  }

  function handleSelectAll(selected, selectedRows, changeRows) {
    debugger;
    if (selected) {
    } else [];
  }

  function handleSelectChange(selectedRowKeys, selectedRows) {
    // state.selectedData = selectedRowKeys;
    // const table = getTable();
    // table.setSelectedRowKeys(selectedRowKeys);
  }
  function handleSearch(value) {
    getTable().setSelectedRowKeys(value);
  }
  function getTable() {
    const table = unref(tableRef);
    if (!table) throw new Error('table is null!');
    return table;
  }
  function removeAll() {
    state.selectedData = [];
    const arr = state.selectedData.map(o => o.id);
    setSelectedRowKeys(arr);
  }
  function removeData(index: number) {
    state.selectedData.splice(index, 1);
    const arr = state.selectedData.map(o => o.id);
    setSelectedRowKeys(arr);
  }

  function handleCancel() {
    visible.value = false;
  }
  function handleSubmit() {
    changeOkLoading(true);

    function callBack() {
      handleCancel();
      changeOkLoading(false);
    }
    const arr = state.selectedData.map(o => o.id);
    state.dataForm.dataInterfaceIds = arr.join(',');
    emit('submit', state.dataForm.interfaceIdentId, arr, callBack);
  }
</script>
