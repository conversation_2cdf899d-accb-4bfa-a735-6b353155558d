<template>
  <div>
    <div>
      <div style="background-color: #ececec; padding: 5px">
        <a-card :bordered="false" style="width: 100%" :headStyle="{ 'text-align': 'center' }"
          :bodyStyle="{ padding: 0 }">
          <template #title>
            <span>单选题</span>
          </template>
          <div v-if="radioQuestions.length > 0" v-for="(item, index) in radioQuestions" :key="index">
            <div style="background-color: #ececec;" :id="item.id">
              <a-form-item :name="item.id">
                <a-card :bordered="false" style="width: 100%" :bodyStyle="{ padding: 0 }">
                  <template #title>
                    <span>单选题 {{ numberToChinese(index + 1) }}</span>
                  </template>
                  <div :id="item.id">
                    <a-form-item :name="item.id">
                      <XundaTextarea v-model:value="item.question" :placeholder="t('common.inputText')" />
                      <a-table :columns="getOptionColumns" :dataSource="item.options" :showHeader="false"
                        :pagination="false">
                        <template #emptyText> </template>
                        <template #bodyCell="{ column, record, index }">
                          <template v-if="column.editComponent === 'Input'">
                            <a-input v-model:value="record[column.dataIndex]" :placeholder="t('common.inputText')" />
                          </template>
                          <template v-if="column.key === 'action' && !readonly">
                            <a-button class="action-btn" type="link" color="error"
                              @click="removeRowOptions(item, index, true)" size="small">
                              {{ t('删除选项') }}
                            </a-button>
                          </template>
                        </template>
                      </a-table>
                      <div class="table-add-action" @click="addOption(item)">
                        <a-button type="link" preIcon="icon-ym icon-ym-btn-add">添加选项</a-button>
                      </div>
                    </a-form-item>
                  </div>
                  <template #extra>
                    <a-space>
                      <a-button type="error" @click="removeQuestion(item, true)">删除</a-button>
                    </a-space>
                  </template>
                  <template #actions>
                    <a-space>
                      <a-button v-if="index === radioQuestions.length - 1" type="success"
                        @click="addQuestion('radio')">继续添加</a-button>
                    </a-space>
                  </template>
                </a-card>
              </a-form-item>
            </div>
          </div>
          <div v-else>
            <a-button type="success" @click="addQuestion('radio')">添加单选题</a-button>
          </div>
        </a-card>
      </div>
      <div style="background-color: #ececec; padding: 5px">
        <a-card :bordered="false" style="width: 100%" :headStyle="{ 'text-align': 'center' }"
          :bodyStyle="{ padding: 0 }">
          <template #title>
            <span>多选题</span>
          </template>
          <div v-if="checkboxQuestions.length > 0" v-for="(item, index) in checkboxQuestions" :key="index">
            <div style="background-color: #ececec;" :id="item.id">
              <a-card :bordered="false" style="width: 100%" :bodyStyle="{ padding: 0 }">
                <template #title>
                  <span>多选题 {{ numberToChinese(index + 1) }}</span>
                </template>
                <div :id="item.id">
                  <a-form-item :name="item.id">
                    <XundaTextarea v-model:value="item.question" :placeholder="t('common.inputText')" />
                    <a-table :columns="getOptionColumns" :dataSource="item.options" :showHeader="false"
                      :pagination="false">
                      <template #emptyText> </template>
                      <template #bodyCell="{ column, record, index }">
                        <template v-if="column.editComponent === 'Input'">
                          <a-input v-model:value="record[column.dataIndex]" :placeholder="t('common.inputText')" />
                        </template>
                        <template v-if="column.key === 'action' && !readonly">
                          <a-button class="action-btn" type="link" color="error"
                            @click="removeRowOptions(item, index, true)" size="small">
                            {{ t('删除选项') }}
                          </a-button>
                        </template>
                      </template>
                    </a-table>
                    <div class="table-add-action" @click="addOption(item)">
                      <a-button type="link" preIcon="icon-ym icon-ym-btn-add">添加选项</a-button>
                    </div>
                  </a-form-item>
                </div>
                <template #extra>
                  <a-space>
                    <a-button type="error" @click="removeQuestion(item, true)">删除</a-button>
                  </a-space>
                </template>
                <template #actions>
                  <a-space>
                    <a-button v-if="index === checkboxQuestions.length - 1" type="success"
                      @click="addQuestion('checkbox')">继续添加</a-button>
                  </a-space>
                </template>
              </a-card>
            </div>
          </div>
          <div v-else>
            <a-button type="success" @click="addQuestion('checkbox')">添加多选题</a-button>
          </div>
        </a-card>
      </div>
      <div style="background-color: #ececec; padding: 5px">
        <a-card :bordered="false" style="width: 100%" :headStyle="{ 'text-align': 'center' }"
          :bodyStyle="{ padding: 0 }">
          <template #title>
            <span>简答题</span>
          </template>
          <div v-if="textAreaQuestions.length > 0" v-for="(item, index) in textAreaQuestions" :key="index">
            <a-card :bordered="false" style="width: 100%" :bodyStyle="{ padding: 0 }">
              <template #title>
                <span>简答题 {{ numberToChinese(index + 1) }}</span>
              </template>
              <div :id="item.id">
                <a-form-item :name="item.id">
                  <XundaTextarea v-model:value="item.question" :placeholder="t('common.inputText')" />
                </a-form-item>
              </div>
              <template #extra>
                <a-space>
                  <a-button type="error" @click="removeQuestion(item, true)">删除</a-button>
                </a-space>
              </template>
              <template #actions>
                <a-space>
                  <a-button v-if="index === textAreaQuestions.length - 1" type="success"
                    @click="addQuestion('textarea')">继续添加</a-button>
                </a-space>
              </template>
            </a-card>
          </div>
          <div v-else>
            <a-button type="warning" @click="addQuestion('textarea')">添加简答题</a-button>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, nextTick, reactive, ref, toRefs } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { relationFormProps, optionsColumn } from '.';
import { buildUUID } from '@/utils/uuid';
import { useI18n } from '@/hooks/web/useI18n';
import { numberToChinese, getDictionaryFullName } from '@/utils/myUtil';
interface State {
  selectedRowKeys: any[];
  defaultProps: any;
  childIndex: any;
}
defineOptions({ name: 'Questionnaire', inheritAttrs: false });

const { t } = useI18n();
const { createMessage, createConfirm } = useMessage();
const props = defineProps(relationFormProps);
const emit = defineEmits(['update:value']);
const state = reactive<State>({
  defaultProps: { label: 'label', value: 'value' },
  childIndex: -1,
  selectedRowKeys: [],
});
const { defaultProps } = toRefs(state);
const radioQuestions = computed(() => {
  let questions = props.questions || [];
  return questions.filter(item => item.type === 'radio');
});

const checkboxQuestions = computed(() => {
  let questions = props.questions || [];
  return questions.filter(item => item.type === 'checkbox');
});

const textAreaQuestions = computed(() => {
  let questions = props.questions || [];
  return questions.filter(item => item.type === 'textarea');
});

const activeKey = ref(['1']);

const betchDeleteQuestion = () => { };

const getRowSelection = computed(() => {
  if (!props.canSelected) return undefined;
  const rowSelection = {
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys: string[]) => {
      state.selectedRowKeys = selectedRowKeys;
    },
  };
  return rowSelection;
});

function addQuestion(type: string) {
  let item = {
    id: buildUUID(),
    type,
    answer: '',
  };
  if (type === 'radio' || type === 'checkbox') {
    addOption(item);
  }
  let questions = props.questions || [];
  emit('update:value', questions);
  questions.push(item);
  nextTick(() => {
    scrollToElement(item.id);
  });
};

function addOption(item: any) {
  let options = item.options || [];
  let optionTitle = 'A';
  if (options.length >= 1) {
    let lastOption = options[options.length - 1];
    let lastOptionTitle = lastOption.title;
    optionTitle = String.fromCharCode(lastOptionTitle.charCodeAt(0) + 1);
  }
  options.push({
    id: buildUUID(),
    answer: '',
    title: optionTitle,
  });
  item.options = options;
};

function removeQuestion(item, showConfirm = false) {
  let questions = props.questions || [];
  if (showConfirm) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除该数据, 是否继续?',
      onOk: () => {
        questions = questions.filter(o => o.id !== item.id);
        emit('update:value', questions);
      },
    });
  } else {
    questions = questions.filter(o => o.id !== item.id);
    emit('update:value', questions);
  }
};

function batchRemoveRow(showConfirm = false) {
  if (!state.selectedRowKeys.length) return createMessage.error('请选择一条数据');
  const handleRemove = () => {
    let questions = props.questions || [];
    questions = questions.filter(o => !state.selectedRowKeys.includes(o.id));
    emit('update:value', questions);
    nextTick(() => {
      state.selectedRowKeys = [];
    });
  };
  if (showConfirm) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除该数据, 是否继续?',
      onOk: () => {
        handleRemove();
      },
    });
  } else {
    handleRemove();
  }
};

const getOptionColumns = computed(() => {
  let columns = optionsColumn || [];
  if (!props.readonly) {
    columns.push({
      title: '操作',
      key: 'action',
      width: 50,
      align: 'center',
      fixed: 'right',
    });
  }
  return columns;
});

function removeRowOptions(item, index, showConfirm = false) {
  let options = item.options || [];
  options.splice(index, 1);
  item.options = options;
};
function scrollToElement(id: string) {
  const element = document.getElementById(id);
  if (element) element.scrollIntoView({ behavior: 'smooth' });
};
</script>

<style scoped lang="scss"></style>
