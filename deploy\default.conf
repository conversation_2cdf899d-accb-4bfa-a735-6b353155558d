server {
  listen       80;
  server_name  localhost;
  root /data/xundasoft/xunda-web-vue3;
  index index.html;

  gzip on;
  gzip_static on;
  gzip_min_length 1k;
  gzip_comp_level 4;
  gzip_proxied any;
  gzip_types text/plain text/xml text/css;
  gzip_vary on;
  gzip_http_version   1.0;
  gzip_disable "MSIE [1-6]\.(?!.*SV1)";

  # XUNDA-START
  # 设置上传文件的大小
  client_max_body_size 100m;

  # 添加头部信息
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
  proxy_set_header X-NginX-Proxy true;
  # This is necessary to pass the correct IP to be hashed
  real_ip_header X-Real-IP;
  proxy_connect_timeout 300;

  # 前端主项目伪静态
  location / {
    try_files $uri $uri/ /index.html;
  }

  # 大屏伪静态
  location /DataV {
    try_files $uri $uri/ /DataV/index.html;
  }

  # 报表伪静态
  location /Report/icons/{
    try_files $uri $uri/ /Report/icons/;
  }

  # 主项目后端接口
  location /api/ {
    proxy_pass http://xunda-gateway-external.java-cloud-v510:30000;
  }

  location /websocket {
    proxy_pass http://xunda-gateway-external.java-cloud-v510:30000/api/message/websocket;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_read_timeout 600s;
  }

  # 报表后端接口
  location /ReportServer/ {
    proxy_pass http://xunda-datareport-external.java-cloud-v510:30007/;
  }

  # Flowable后端接口
  location /api/Flow {
      proxy_pass http://xunda-flowable-external.java-cloud-v510:31000;
  }

  # Univer后端接口
  location /api/Report {
      proxy_pass http://xunda-univer-external.java-cloud-v510:32000;
  }

  # 文件预览
  location /FileServer {
    proxy_pass http://xunda-file-preview-external.java-cloud-v510:30090;
  }

  location ~ /FileServer/*.*\.(js|css)?$ {
    proxy_pass http://xunda-file-preview-external.java-cloud-v510:30090;
  }
  # XUNDA-End
}
