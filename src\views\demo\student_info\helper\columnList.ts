const columnList = [
  {
    useScan: false,
    suffixIcon: '',
    fullNameI18nCode: [''],
    align: 'left',
    showCount: false,
    __config__: {
      formId: 101,
      visibility: ['pc', 'app'],
      noShow: false,
      tipLabel: '',
      tableFixed: 'none',
      dragDisabled: false,
      className: [],
      label: '姓名',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'demo_student_info',
      renderKey: 1727332852322,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      xundaKey: 'input',
      tag: 'XundaInput',
      regList: [],
      tableAlign: 'left',
      span: 24,
    },
    readonly: false,
    prop: 'S_Name',
    xundaKey: 'input',
    __vModel__: 'S_Name',
    disabled: false,
    id: 'S_Name',
    placeholder: '请输入',
    addonBefore: '',
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    clearable: true,
    maxlength: null,
    fullName: '姓名',
    label: '姓名',
    sortable: false,
    addonAfter: '',
    maskConfig: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    width: null,
    useMask: false,
    showPassword: false,
    fixed: 'none',
    style: {
      width: '100%',
    },
    prefixIcon: '',
    labelI18nCode: '',
  },
  {
    useScan: false,
    suffixIcon: '',
    fullNameI18nCode: [''],
    align: 'left',
    showCount: false,
    __config__: {
      formId: 102,
      visibility: ['pc', 'app'],
      noShow: false,
      tipLabel: '',
      tableFixed: 'none',
      dragDisabled: false,
      className: [],
      label: '学号',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'demo_student_info',
      renderKey: 1727332952710,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      xundaKey: 'input',
      tag: 'XundaInput',
      regList: [],
      tableAlign: 'left',
      span: 24,
    },
    readonly: false,
    prop: 'S_No',
    xundaKey: 'input',
    __vModel__: 'S_No',
    disabled: false,
    id: 'S_No',
    placeholder: '请输入',
    addonBefore: '',
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    clearable: true,
    maxlength: null,
    fullName: '学号',
    label: '学号',
    sortable: false,
    addonAfter: '',
    maskConfig: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    width: null,
    useMask: false,
    showPassword: false,
    fixed: 'none',
    style: {
      width: '100%',
    },
    prefixIcon: '',
    labelI18nCode: '',
  },
  {
    controls: false,
    fullName: '设计子表-成绩',
    fullNameI18nCode: ['', ''],
    label: '设计子表-成绩',
    sortable: false,
    align: 'left',
    thousands: false,
    isAmountChinese: false,
    addonAfter: '',
    __config__: {
      formId: 106,
      relationTable: 'demo_student_score',
      visibility: ['pc', 'app'],
      noShow: false,
      parentVModel: 'tableField103',
      tipLabel: '',
      tableFixed: 'none',
      dragDisabled: false,
      className: [],
      label: '成绩',
      trigger: ['blur', 'change'],
      showLabel: true,
      required: false,
      tableName: 'demo_student_info',
      renderKey: 1727332976168,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-number',
      xundaKey: 'inputNumber',
      isSubTable: true,
      tag: 'XundaInputNumber',
      regList: [],
      tableAlign: 'left',
      span: 24,
    },
    prop: 'tableField103-SS_Score',
    xundaKey: 'inputNumber',
    width: null,
    __vModel__: 'SS_Score',
    fixed: 'none',
    style: {
      width: '100%',
    },
    step: 1,
    disabled: false,
    id: 'tableField103-SS_Score',
    placeholder: '请输入',
    addonBefore: '',
    labelI18nCode: '',
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    popupType: 'dialog',
    hasPage: false,
    modelId: '608970807279885125',
    pageSize: 20,
    columnOptions: [],
    fullNameI18nCode: ['', ''],
    align: 'left',
    __config__: {
      relationTable: 'demo_student_score',
      defaultValue: '',
      parentVModel: 'tableField103',
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'demo_student_info',
      renderKey: 1727333003171,
      transferList: [],
      tagIcon: 'icon-ym icon-ym-generator-menu',
      xundaKey: 'relationForm',
      isSubTable: true,
      tag: 'XundaRelationForm',
      formId: 108,
      visibility: ['pc', 'app'],
      noShow: false,
      tipLabel: '',
      tableFixed: 'none',
      label: '课程',
      trigger: 'change',
      layout: 'colFormItem',
      regList: [],
      tableAlign: 'left',
      span: 24,
    },
    prop: 'tableField103-SS_Course',
    xundaKey: 'relationForm',
    __vModel__: 'SS_Course',
    disabled: false,
    id: 'tableField103-SS_Course',
    placeholder: '请选择',
    popupWidth: '800px',
    on: {
      change: '({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
    filterable: false,
    clearable: true,
    fullName: '设计子表-课程',
    label: '设计子表-课程',
    sortable: false,
    relationField: 'C_Name',
    popupTitle: '选择数据',
    width: null,
    fixed: 'none',
    style: {
      width: '100%',
    },
    labelI18nCode: '',
  },
];
export default columnList;
