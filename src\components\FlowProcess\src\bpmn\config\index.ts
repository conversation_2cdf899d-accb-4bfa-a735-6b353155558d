import { xundaApproverConfig } from './element/approver';
import { xundaStartConfig } from './element/start';
import { xundaEndConfig } from './element/end';
import { xundaSubFlowConfig } from './element/subFlow';
import { xundaTimerConfig } from './element/timer';
import { xundaLabelConfig } from './element/label';
import { xundaExclusiveConfig } from './element/gateway/exclusive';
import { xundaInclusiveConfig } from './element/gateway/inclusive';
import { xundaParallelConfig } from './element/gateway/parallel';
import {
  bpmnTask,
  bpmnStart,
  bpmnEnd,
  bpmnTimer,
  bpmnSubFlow,
  bpmnLabel,
  bpmnInclusive,
  bpmnParallel,
  bpmnExclusive,
  typeStart,
  typeEnd,
  typeSubFlow,
  typeTimer,
  typeLabel,
  typeGateway,
  typeTask,
  bpmnSequenceFlow,
} from './variableName';
import { xundaSequenceFlow } from './element/sequenceFlow';

const hasLabelElements: any = ['bpmn:StartEvent', 'bpmn:EndEvent', 'bpmn:InclusiveGateway']; // 一开始就有label标签的元素类型
const BpmnBusinessObjectKey = {
  id: 'wnId',
};

const typeConfig: any = {
  [bpmnTask]: xundaApproverConfig,
  [bpmnStart]: xundaStartConfig,
  [bpmnEnd]: xundaEndConfig,
  [bpmnSubFlow]: xundaSubFlowConfig,
  [bpmnTimer]: xundaTimerConfig,
  [bpmnLabel]: xundaLabelConfig,
  [bpmnInclusive]: xundaInclusiveConfig,
  [bpmnParallel]: xundaParallelConfig,
  [bpmnExclusive]: xundaExclusiveConfig,
  [bpmnSequenceFlow]: xundaSequenceFlow,
};

// 默认wnType值
const conversionWnType: any = {
  [bpmnStart]: typeStart,
  [bpmnEnd]: typeEnd,
  [bpmnTask]: typeTask,
  [bpmnSubFlow]: typeSubFlow,
  [bpmnTimer]: typeTimer,
  [bpmnLabel]: typeLabel,
  [bpmnInclusive]: typeGateway,
  [bpmnParallel]: typeGateway,
  [bpmnExclusive]: typeGateway,
};

export { typeConfig, BpmnBusinessObjectKey, hasLabelElements, conversionWnType };
