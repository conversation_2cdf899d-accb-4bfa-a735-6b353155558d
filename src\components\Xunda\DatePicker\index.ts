import { withInstall } from '@/utils';
import DatePicker from './src/DatePicker.vue';
import DateRange from './src/DateRange.vue';
import TimePicker from './src/TimePicker.vue';
import TimeRange from './src/TimeRange.vue';

export const XundaDatePicker = withInstall(DatePicker);
export const XundaDateRange = withInstall(DateRange);
export const XundaTimePicker = withInstall(TimePicker);
export const XundaTimeRange = withInstall(TimeRange);
