import { BasicColumn } from '@/components/Table';
import { useI18n } from '@/hooks/web/useI18n';

const { t } = useI18n();


export const relationFormProps = {
    value: [String, Number, Array] as PropType<String | number | string[] | number[] | [string, number][]>,
    modelId: { type: String, default: '' },
    relationField: { type: String, default: '' },
    field: { type: String, default: '' },
    placeholder: { type: String, default: t('common.chooseText') },
    columnOptions: { type: Array, default: () => [] },
    hasPage: { type: Boolean, default: false },
    pageSize: { type: Number, default: 20 },
    allowClear: { type: Boolean, default: true },
    size: { type: String, default: 'default' },
    disabled: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    popupType: { type: String, default: 'dialog' },
    popupTitle: { type: String, default: t('component.xunda.popupSelect.modalTitle') },
    popupWidth: { type: String, default: '800px' },
    getDataChange: { type: Function, default: () => { } },
    getFieldDataSelect: { type: Function, default: () => { } },
    getConfigData: { type: Function, default: () => { } },
    title: { type: String, default: '' },
    dataSource: { type: Array<any>, default: () => [] },
    canSelected: { type: Boolean, default: false },
    showActionColumn: { type: Boolean, default: false },
    readonly: { type: Boolean, default: false },
    questions: { type: Array<any>, default: () => [] },
};

export const optionsColumn: BasicColumn[] = [
    {
        title: t('component.xunda.optionsColumn.title', "选项"),
        dataIndex: 'title',
        width: 50,
        align: 'left',
    },
    {
        title: t('component.xunda.optionsColumn.value', "内容"),
        dataIndex: 'answer',
        align: 'center',
        editComponent: "Input"
    }
];