<template>
  <a-table
    :data-source="dataSource"
    :columns="getColumns"
    size="small"
    :pagination="false"
    :scroll="{ x: 'max-content' }"
    :rowSelection="getRowSelection"
    rowKey="id">
    <template #title>{{ title }}</template>
    <template #headerCell="{ column }">
      <span class="required-sign" v-if="column.required">*</span>
      {{ column.title }}
      <BasicHelp :text="column.tipLabel" v-if="column.tipLabel && column.title" />
    </template>
    <template #bodyCell="{ column, index, record }">
      <template v-if="!readonly">
        <template v-if="column.editComponent === 'Input'">
          <XundaInput v-model:value="record[column.dataIndex]" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }" :showCount="false">
          </XundaInput>
        </template>
        <template v-if="column.editComponent === 'DatePicker'">
          <XundaDatePicker v-model:value="record[column.dataIndex]" placeholder="请选择" :allowClear="true" :style="{ width: '100%' }" format="yyyy-MM-dd">
          </XundaDatePicker>
        </template>
      </template>
      <template v-if="column.key === 'action' && !readonly">
        <a-space>
          <a-button class="action-btn" type="link" @click="copyRow(index)" size="small">
            {{ t('common.copyText', '复制') }}
          </a-button>
          <a-button class="action-btn" type="link" color="error" @click="removeRow(index, true)" size="small">
            {{ t('common.delText', '删除') }}
          </a-button>
        </a-space>
      </template>
    </template>
  </a-table>
  <a-space class="input-table-footer-btn" v-if="!readonly">
    <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addRow">
      {{ t('common.add1Text', '添加') }}
    </a-button>
    <a-button v-if="canSelected" type="danger" preIcon="icon-ym icon-ym-btn-clearn" @click="batchRemoveRow(true)">
      {{ t('common.batchDelText', '批量删除') }}
    </a-button>
  </a-space>
</template>

<script lang="ts" setup>
  import { computed, nextTick, reactive, unref } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { cloneDeep } from 'lodash-es';
  import { buildUUID } from '@/utils/uuid';
  import { useI18n } from '@/hooks/web/useI18n';
  import { relationFormProps } from '.';

  interface State {
    selectedRowKeys: any[];
    childIndex: any;
  }

  defineOptions({ name: 'FormItemTable', inheritAttrs: false });

  const { createMessage, createConfirm } = useMessage();

  const props = defineProps(relationFormProps);
  const emit = defineEmits(['update:value']);

  const state = reactive<State>({
    childIndex: -1,
    selectedRowKeys: [],
  });
  const { t } = useI18n();

  const getRowSelection = computed(() => {
    if (!props.canSelected) return undefined;
    const rowSelection = {
      selectedRowKeys: state.selectedRowKeys,
      onChange: (selectedRowKeys: string[]) => {
        state.selectedRowKeys = selectedRowKeys;
      },
    };
    return rowSelection;
  });

  const getColumns = computed(() => {
    let columns = props.columns || [];
    if (props.showActionColumn) {
      columns.push({
        title: t('common.actionText', '操作'),
        key: 'action',
        width: 120,
        align: 'center',
        fixed: 'right',
      });
    }
    return columns;
  });

  const addRow = () => {
    let item = {
      id: buildUUID(),
    };
    let dataSource = props.dataSource || [];
    dataSource.push(item);
    emit('update:value', dataSource);
    console.log('dataSource', dataSource);
    state.childIndex = dataSource.length - 1;
    state.childIndex = -1;
  };

  function copyRow(index) {
    let dataSource = props.dataSource || [];
    let item = cloneDeep(dataSource[index]);
    let copyData = {};
    for (let i = 0; i < unref(props.columns).length; i++) {
      const cur = unref(props.columns)[i];
      if (cur.key != 'index' && cur.key != 'action') {
        copyData[cur.dataIndex] = item[cur.dataIndex];
      }
    }
    const copyItem = { ...copyData, id: buildUUID() };
    dataSource.push(copyItem);
    emit('update:value', dataSource);
    state.childIndex = dataSource.length - 1;
    state.childIndex = -1;
  }

  const removeRow = (index, showConfirm = false) => {
    let dataSource = props.dataSource || [];
    if (showConfirm) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '此操作将永久删除该数据, 是否继续?',
        onOk: () => {
          dataSource.splice(index, 1);
          emit('update:value', dataSource);
        },
      });
    } else {
      dataSource.splice(index, 1);
      emit('update:value', dataSource);
    }
  };

  const batchRemoveRow = (showConfirm = false) => {
    if (!state.selectedRowKeys.length) return createMessage.error('请选择一条数据');
    const handleRemove = () => {
      let dataSource = props.dataSource || [];
      dataSource = dataSource.filter(o => !state.selectedRowKeys.includes(o.id));
      emit('update:value', dataSource);
      nextTick(() => {
        state.selectedRowKeys = [];
      });
    };
    if (showConfirm) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '此操作将永久删除该数据, 是否继续?',
        onOk: () => {
          handleRemove();
        },
      });
    } else {
      handleRemove();
    }
  };
</script>

<style lang="scss" scoped></style>
