import { defHttp } from '@/utils/http/axios';


const questionnaireApi = '/api/flowup/questionnaire';
// 获取列表
export function getList(data) {
  return defHttp.post({ url: questionnaire<PERSON><PERSON> + '/getList', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: questionnaireApi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: questionnaire<PERSON>pi + '/' + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: questionnaireApi + '/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: questionnaireApi + '/detail/' + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: questionnaireApi + '/' + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: questionnaire<PERSON>pi + '/batchRemove', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: questionnaire<PERSON>pi + '/export', data });
}


export function getPatientList(data) {
  return defHttp.post({ url: '/api/flowup/patient/getList', data });
}

export function distribute(data) {
  return defHttp.post({ url: questionnaireApi + 'Record/distribute', data });
}

export function getquestionnairePatientList(id) {
  return defHttp.get({ url: questionnaireApi + 'Record/getPatientList/' + id });
}