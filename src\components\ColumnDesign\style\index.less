@prefix-cls: ~'@{namespace}-basic-column-design';

.@{prefix-cls} {
  position: relative;
  width: 100%;
  height: 100%;
  .head-tabs {
    position: absolute;
    left: 0;
    top: 0;
    width: calc(100% - 350px);
    height: 42px;
    border-bottom: 1px solid @border-color-base1;
    background: @component-background;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 10px;
    z-index: 100;
    border-radius: 4px 4px 0 0;
    .ant-btn {
      padding: 0;
      margin-left: 15px;
    }
    .unActive-btn {
      color: @text-color !important;
      &:hover {
        color: @primary-color !important;
      }
    }
  }
  .column-empty-box {
    width: 100%;
    height: 100%;
    background-color: @component-background;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .empty-img {
      width: 180px;
      height: 120px;
    }
    p {
      padding: 15px 0;
    }
  }
  .column-design-container {
    height: 100%;
    width: 100%;
    position: relative;
    padding-top: 42px;
    .main-board {
      height: 100%;
      width: auto;
      margin: 0 350px 0 0;
      padding: 10px;
      overflow: auto;
      overflow-x: hidden;
      background-color: @component-background;
      border-radius: 0 0 4px 4px;
    }
    .right-board {
      width: 340px;
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      margin-left: 10px;
      background-color: @component-background;
      border-radius: 4px;
      overflow: hidden;
      .right-main {
        position: relative;
        height: calc(100% - 42px);
        overflow: hidden;
        box-sizing: border-box;
        .scrollbar__view {
          padding: 10px;
        }
        .right-board-form {
          .ant-form-item {
            margin-bottom: 18px;
          }
          .typeList {
            display: flex;
            flex-wrap: wrap;
            .item:nth-child(3n + 3) {
              margin-right: 0;
            }
            .item {
              width: 100px;
              margin-bottom: 15px;
              margin-right: 10px;
              border-bottom: unset !important;
              &.view-item {
                width: 150px;
                .item-img {
                  height: 100px;
                }
              }

              .item-img {
                width: 100%;
                height: 70px;
                border-radius: 4px;
                overflow: hidden;
                cursor: pointer;
                position: relative;
                border: 1px solid transparent;
                img {
                  width: 100%;
                  height: 100%;
                  z-index: -1;
                }
                &.checked {
                  border: 1px solid @primary-color;
                }
                .icon-checked {
                  display: block;
                  width: 12px;
                  height: 12px;
                  border: 12px solid @primary-color;
                  border-left: 12px solid transparent !important;
                  border-top: 12px solid transparent !important;
                  border-bottom-right-radius: 4px;
                  position: absolute;
                  right: -1px;
                  bottom: -1px;
                  .anticon-check {
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    font-size: 12px;
                    color: #fff;
                  }
                }
              }
              .item-name {
                font-size: 12px;
                color: @text-color-secondary;
                margin-top: 10px;
                text-align: center;
              }
            }
          }
          .right-radio {
            .ant-radio-button-wrapper {
              padding: 0 11px;
            }
          }
          .btnsList {
            width: 100%;
            .btnsList-cell {
              display: flex;
              align-items: flex-start;
              margin-bottom: 10px;
              width: 100%;
            }
            .ant-checkbox-wrapper {
              width: 90px;
              flex-shrink: 0;
              line-height: 32px;
            }
            .btn-upload {
              width: 100%;
              margin-top: 10px;
            }
          }
          .btn-cap {
            margin-bottom: 10px;
            color: @text-color-secondary;
          }
          .custom-btns-list {
            .custom-item {
              display: flex;
              align-items: center;
              border: 1px dashed @component-background;
              box-sizing: border-box;
              & + .custom-item {
                margin-top: 4px;
              }
              &.sortable-chosen {
                border: 1px dashed @primary-color;
              }
              .ant-input + .ant-input {
                margin-left: 4px;
              }
              .ant-input-group-addon {
                cursor: pointer;
                padding: 0;
                span {
                  display: inline-block;
                  line-height: 30px;
                  padding: 0 11px;
                }
              }
              .custom-line-icon {
                line-height: 32px;
                font-size: 22px;
                padding: 0 4px;
                color: #606266;
                .icon-ym-btn-clearn {
                  font-size: 18px;
                }
                .icon-ym-darg {
                  font-size: 20px;
                  line-height: 31px;
                  display: inline-block;
                  cursor: move;
                }
                &.option-drag {
                  padding-left: 0;
                }
                &.close-btn {
                  padding-right: 0;
                }
              }
              .custom-line-value {
                width: 90px;
                flex-shrink: 0;
                line-height: 32px;
                font-size: 14px;
              }
              .close-btn {
                cursor: pointer;
                color: @error-color;
              }
            }
            .add-btn .ant-btn {
              padding: 0;
            }
          }
        }
      }
    }
  }
}
