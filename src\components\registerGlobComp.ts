import type { App } from 'vue';
import { Button } from './Button';
import {
  Input,
  InputNumber,
  Layout,
  Form,
  Switch,
  Dropdown,
  Menu,
  Select,
  Table,
  Checkbox,
  Tabs,
  Collapse,
  Card,
  Tooltip,
  Row,
  Col,
  Popconfirm,
  Divider,
  Alert,
  AutoComplete,
  Cascader,
  Rate,
  Slider,
  Avatar,
  Tag,
  Space,
  Steps,
  Popover,
  Radio,
  Progress,
  Image,
  Upload,
} from 'ant-design-vue';

import { BasicHelp, BasicCaption } from '@/components/Basic';
import { XundaAlert } from '@/components/Xunda/Alert';
import { XundaAreaSelect } from '@/components/Xunda/AreaSelect';
import { XundaAutoComplete } from '@/components/Xunda/AutoComplete';
import { XundaButton } from '@/components/Xunda/Button';
import { XundaCron } from '@/components/Xunda/Cron';
import { XundaCascader } from '@/components/Xunda/Cascader';
import { XundaCheckbox, XundaCheckboxSingle } from '@/components/Xunda/Checkbox';
import { XundaColorPicker } from '@/components/Xunda/ColorPicker';
import { XundaDatePicker, XundaDateRange, XundaTimePicker, XundaTimeRange } from '@/components/Xunda/DatePicker';
import { XundaDivider } from '@/components/Xunda/Divider';
import { XundaEmpty } from '@/components/Xunda/Empty';
import { XundaIconPicker } from '@/components/Xunda/IconPicker';
import { XundaInput, XundaTextarea, XundaI18nInput } from '@/components/Xunda/Input';
import { XundaInputNumber } from '@/components/Xunda/InputNumber';
import { XundaLink } from '@/components/Xunda/Link';
import { XundaOpenData } from '@/components/Xunda/OpenData';
import { XundaOrganizeSelect, XundaDepSelect, XundaPosSelect, XundaGroupSelect, XundaRoleSelect, XundaUserSelect, XundaUsersSelect } from '@/components/Xunda/Organize';
import { XundaQrcode } from '@/components/Xunda/Qrcode';
import { XundaBarcode } from '@/components/Xunda/Barcode';
import { XundaRadio } from '@/components/Xunda/Radio';
import { XundaSelect } from '@/components/Xunda/Select';
import { XundaRate } from '@/components/Xunda/Rate';
import { XundaSlider } from '@/components/Xunda/Slider';
import { XundaSign } from '@/components/Xunda/Sign';
import { XundaSignature } from '@/components/Xunda/Signature';
import { XundaSwitch } from '@/components/Xunda/Switch';
import { XundaText } from '@/components/Xunda/Text';
import { XundaTreeSelect } from '@/components/Xunda/TreeSelect';
import { XundaUploadFile, XundaUploadImg, XundaUploadImgSingle, XundaUploadBtn } from '@/components/Xunda/Upload';
import { Tinymce } from '@/components/Tinymce/index';
import { XundaNumberRange } from '@/components/Xunda/NumberRange';
import { XundaRelationFormAttr } from '@/components/Xunda/RelationFormAttr';
import { XundaPopupSelect, XundaPopupTableSelect } from '@/components/Xunda/PopupSelect';
import { XundaPopupAttr } from '@/components/Xunda/PopupAttr';
import { XundaCalculate } from '@/components/Xunda/Calculate';
import { XundaLocation } from '@/components/Xunda/Location';
import { XundaIframe } from '@/components/Xunda/Iframe';
import { XundaTextTag } from '@/components/Xunda/TextTag';

const XundaEditor = Tinymce;
XundaEditor.name = 'XundaEditor';
const XundaGroupTitle = BasicCaption;
XundaGroupTitle.name = 'XundaGroupTitle';

export function registerGlobComp(app: App) {
  app
    .use(Input)
    .use(InputNumber)
    .use(Button)
    .use(Layout)
    .use(Form)
    .use(Switch)
    .use(Dropdown)
    .use(Menu)
    .use(Select)
    .use(Table)
    .use(Checkbox)
    .use(Tabs)
    .use(Card)
    .use(Collapse)
    .use(Tooltip)
    .use(Row)
    .use(Col)
    .use(Popconfirm)
    .use(Popover)
    .use(Divider)
    .use(Slider)
    .use(Rate)
    .use(Alert)
    .use(AutoComplete)
    .use(Cascader)
    .use(Avatar)
    .use(Tag)
    .use(Space)
    .use(Steps)
    .use(Radio)
    .use(Progress)
    .use(Image)
    .use(Upload)
    .use(BasicHelp)
    .use(XundaAlert)
    .use(XundaRate)
    .use(XundaSlider)
    .use(XundaAreaSelect)
    .use(XundaAutoComplete)
    .use(XundaButton)
    .use(XundaCron)
    .use(XundaCascader)
    .use(XundaCheckbox)
    .use(XundaCheckboxSingle)
    .use(XundaColorPicker)
    .use(XundaDatePicker)
    .use(XundaDateRange)
    .use(XundaTimePicker)
    .use(XundaTimeRange)
    .use(XundaDivider)
    .use(XundaEmpty)
    .use(XundaGroupTitle)
    .use(XundaIconPicker)
    .use(XundaInput)
    .use(XundaTextarea)
    .use(XundaI18nInput)
    .use(XundaInputNumber)
    .use(XundaLink)
    .use(XundaOrganizeSelect)
    .use(XundaDepSelect)
    .use(XundaPosSelect)
    .use(XundaGroupSelect)
    .use(XundaRoleSelect)
    .use(XundaUserSelect)
    .use(XundaUsersSelect)
    .use(XundaOpenData)
    .use(XundaQrcode)
    .use(XundaBarcode)
    .use(XundaRadio)
    .use(XundaSelect)
    .use(XundaSign)
    .use(XundaSignature)
    .use(XundaSwitch)
    .use(XundaText)
    .use(XundaTreeSelect)
    .use(XundaEditor)
    .use(XundaRelationFormAttr)
    .use(XundaPopupSelect)
    .use(XundaPopupTableSelect)
    .use(XundaPopupAttr)
    .use(XundaNumberRange)
    .use(XundaCalculate)
    .use(XundaUploadFile)
    .use(XundaUploadImg)
    .use(XundaUploadImgSingle)
    .use(XundaUploadBtn)
    .use(XundaLocation)
    .use(XundaIframe)
    .use(XundaTextTag);
}
