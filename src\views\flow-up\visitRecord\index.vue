<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm @register="registerSearchForm" :schemas="searchSchemas" @advanced-change="redoHeight"
          @submit="handleSearchSubmit" @reset="handleSearchReset" class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef" @columns-change="handleColumnChange">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addHandle()"> {{ t('common.add2Text',
              '新增') }}</a-button>
            <!--  <a-button type="link" preIcon="icon-ym icon-ym-btn-download"
              @click="openExportModal(true, { columnList: state.exportList, selectIds: getSelectRowKeys() })">
               {{ t('common.exportText', '导出') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-upload"
              @click="openImportModal(true, { url: 'example/basis/course', menuId: searchInfo.menuId })">
              {{ t('common.importText', '导入') }}</a-button> -->
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{
              t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="!(record.top || column.id?.includes('-'))">
              <template v-if="column.dataIndex === 'patientId'">
                <p class="link-text" @click="toPatientDetail(record.patientId)"> {{ record.patientName }}</p>
              </template>
              <template v-if="column.dataIndex === 'physicianId'">
                <p class="link-text" @click="toPhysicianDetail(record.physicianId)"> {{ record.physicianName }}</p>
              </template>
              <template v-if="column.xundaKey === 'relationForm'">
                <p class="link-text" @click="toDetail(column.modelId, record[column.dataIndex + `_id`])"> {{
                  record[column.dataIndex] }}</p>
              </template>
              <template v-if="column.xundaKey === 'inputNumber'">
                <xunda-input-number v-model:value="record[column.prop]" :precision="column.precision"
                  :thousands="column.thousands" disabled detailed />
              </template>
              <template v-if="column.xundaKey === 'calculate'">
                <xunda-calculate v-model:value="record[column.prop]" :isStorage="column.isStorage"
                  :precision="column.precision" :thousands="column.thousands" detailed />
              </template>
              <template v-if="column.xundaKey === 'sign'">
                <xunda-sign v-model:value="record[column.prop]" detailed />
              </template>
              <template v-if="column.xundaKey === 'signature'">
                <xunda-signature v-model:value="record[column.prop]" detailed />
              </template>
              <template v-if="column.xundaKey === 'rate'">
                <xunda-rate v-model:value="record[column.prop]" :count="column.count" :allowHalf="column.allowHalf"
                  disabled />
              </template>
              <template v-if="column.xundaKey === 'slider'">
                <xunda-slider v-model:value="record[column.prop]" :min="column.min" :max="column.max"
                  :step="column.step" disabled />
              </template>
              <template v-if="column.xundaKey === 'uploadImg'">
                <xunda-upload-img v-model:value="record[column.prop]" disabled detailed simple
                  v-if="record[column.prop]?.length" />
              </template>
              <template v-if="column.xundaKey === 'uploadFile'">
                <xunda-upload-file v-model:value="record[column.prop]" disabled detailed simple
                  v-if="record[column.prop]?.length" />
              </template>
              <template v-if="column.xundaKey === 'input'">
                <xunda-input v-model:value="record[column.prop]" :useMask="column.useMask"
                  :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
            </template>
            <template v-if="column.key === 'action' && !record.top">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" />
    <ExportModal @register="registerExportModal" @download="handleDownload" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <PatientDetail ref="patientDetailRef" />
    <PhysicianDetail ref="physicianDetailRef" />
  </div>
</template>

<script lang="ts" setup>
import { getConfigData } from '@/api/onlineDev/visualDev';
import { ref, reactive, computed, unref, nextTick, provide } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useModal } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { BasicTable, useTable, TableAction, ActionItem, TableActionType, SorterResult } from '@/components/Table';
import { useRoute } from 'vue-router';
import { cloneDeep } from 'lodash-es';
import { usePermission } from '@/hooks/web/usePermission';
import { getList, batchDelete, exportData } from '@/views/flow-up/visitRecord/api';
import { columns, searchSchemas } from '@/views/flow-up/visitRecord/index';
import Form from '@/views/flow-up/visitRecord/Form.vue';
import Detail from '@/views/flow-up/visitRecord/Detail.vue';
import PatientDetail from '@/views/flow-up/patient/Detail.vue';
import PhysicianDetail from '@/views/flow-up/physician/Detail.vue';
import { ExportModal } from '@/components/CommonModal';
import { downloadByUrl } from '@/utils/file/download';
import { ImportModal } from '@/components/CommonModal';

interface State {
  config: any;
  columnList: any[];
  printListOptions: any[];
  columnBtnsList: any[];
  customBtnsList: any[];
  treeFieldNames: any;
  leftTreeData: any[];
  leftTreeLoading: boolean;
  treeActiveId: string;
  treeActiveNodePath: any;
  columns: any[];
  complexColumns: any[];
  childColumnList: any[];
  exportList: any[];
  cacheList: any[];
  currFlow: any;
  isCustomCopy: boolean;
  candidateType: number;
  currRow: any;
  workFlowFormData: any;
  expandObj: any;
  columnSettingList: any[];
  searchSchemas: any[];
  treeRelationObj: any;
  treeQueryJson: any;
  leftTreeActiveInfo: any;
  keyword: string;
}

const route = useRoute();
const { hasBtnP } = usePermission();
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();

const [registerSuperQueryModal] = useModal();
const formRef = ref<any>(null);
const tableRef = ref<Nullable<TableActionType>>(null);
const detailRef = ref<any>(null);
const relationDetailRef = ref<any>(null);
const patientDetailRef = ref<any>(null);
const physicianDetailRef = ref<any>(null);
const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
const [registerImportModal, { openModal: openImportModal }] = useModal();
const state = reactive<State>({
  config: {},
  columnList: [],
  printListOptions: [],
  columnBtnsList: [],
  customBtnsList: [],
  treeFieldNames: {
    children: 'children',
    title: 'fullName',
    key: 'id',
    isLeaf: 'isLeaf',
  },
  leftTreeData: [],
  leftTreeLoading: false,
  treeActiveId: '',
  treeActiveNodePath: [],
  columns: [],
  complexColumns: [], // 复杂表头
  childColumnList: [],
  exportList: [],
  cacheList: [],
  currFlow: {},
  isCustomCopy: false,
  candidateType: 1,
  currRow: {},
  workFlowFormData: {},
  expandObj: {},
  columnSettingList: [],
  searchSchemas: [],
  treeRelationObj: null,
  treeQueryJson: {},
  leftTreeActiveInfo: {},
  keyword: '',
});
const defaultSearchInfo = {
  menuId: route.meta.modelId as string,
  moduleId: '661954965010384453',
  superQueryJson: '',
  dataType: 0,
};
const searchInfo = reactive({
  ...cloneDeep(defaultSearchInfo),
});
const [registerSearchForm, { submit: searchFormSubmit, setFieldsValue }] = useForm({
  baseColProps: { span: 6 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
});
const [registerTable, { reload, getFetchParams, setLoading, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  api: getList,
  columns: columns,
  pagination: { pageSize: 20 }, //有分页
  searchInfo: unref(searchInfo),
  clickToRowSelect: false,
  afterFetch: data => {
    const list = data.map(o => ({
      ...o,
      ...state.expandObj,
    }));
    state.cacheList = cloneDeep(list);
    return list;
  },
  sortFn: (sortInfo: SorterResult | SorterResult[]) => {
    if (Array.isArray(sortInfo)) {
      const sortList = sortInfo.map(o => (o.order === 'descend' ? '-' : '') + o.field);
      return { sidx: sortList.join(',') };
    } else {
      const { field, order } = sortInfo;
      if (field && order) {
        // 排序字段
        return { sidx: (order === 'descend' ? '-' : '') + field };
      } else {
        return {};
      }
    }
  },
  ellipsis: true,
  bordered: true,
  actionColumn: {
    width: 150,
    title: t('component.table.action'),
    dataIndex: 'action',
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.top,
    }),
  },
});

provide('getLeftTreeActiveInfo', () => state.leftTreeActiveInfo);

const getHasBatchBtn = computed(() => {
  let btnsList = [];
  return !!btnsList.length;
});

function init() {
  state.config = {};
  searchInfo.menuId = route.meta.modelId as string;
  setLoading(true);
  nextTick(() => {
    // 有搜索列表
    searchFormSubmit();
  });
}

// 关联表单查看详情
function toDetail(modelId, id) {
  if (!id) return;
  getConfigData(modelId).then(res => {
    if (!res.data || !res.data.formData) return;
    const formConf = JSON.parse(res.data.formData);
    formConf.popupType = 'general';
    const data = { id, formConf, modelId };
    relationDetailRef.value?.init(data);
  });
}
function handleColumnChange(data) {
  state.columnSettingList = data;
}
function getTableActions(record): ActionItem[] {
  return [
    {
      label: t('common.editText', '编辑'),
      onClick: updateHandle.bind(null, record),
    },
    {
      label: t('common.delText', '删除'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
    {
      label: t('common.detailText', '详情'),
      onClick: goDetail.bind(null, record),
    },
  ];
}
// 编辑
function updateHandle(record) {
  // 不带工作流
  const data = {
    id: record.id,
    menuId: searchInfo.menuId,
    allList: state.cacheList,
  };
  formRef.value?.init(data);
}
// 删除
function handleDelete(id) {
  const query = { ids: [id] };
  batchDelete(query).then(res => {
    createMessage.success(res.msg);
    clearSelectedRowKeys();
    reload();
  });
}
// 查看详情
function goDetail(record) {
  // 不带流程
  const data = {
    id: record.id,
  };
  detailRef.value?.init(data);
}
// 新增
function addHandle() {
  // 不带流程新增
  const data = {
    id: '',
    menuId: searchInfo.menuId,
    allList: state.cacheList,
  };
  formRef.value?.init(data);
}// 导出
function handleDownload(data) {
  let query = { ...getFetchParams(), ...data };
  exportData(query)
    .then(res => {
      setExportModalProps({ confirmLoading: false });
      if (!res.data.url) return;
      downloadByUrl({ url: res.data.url });
      closeExportModal();
    })
    .catch(() => {
      setExportModalProps({ confirmLoading: false });
    });
}

// 批量删除
function handelBatchRemove() {
  const ids = getSelectRowKeys();
  if (!ids.length) return createMessage.error('请选择一条数据');
  createConfirm({
    iconType: 'warning',
    title: t('common.tipTitle'),
    content: '您确定要删除这些数据吗, 是否继续?',
    onOk: () => {
      const query = { ids: ids };
      batchDelete(query).then(res => {
        createMessage.success(res.msg);
        clearSelectedRowKeys();
        reload();
      });
    },
  });
}
// 高级查询
function handleSuperQuery(superQueryJson) {
  searchInfo.superQueryJson = superQueryJson;
  reload({ page: 1 });
}

function handleSearchReset() {
  clearSelectedRowKeys();
}

function handleSearchSubmit(data) {
  clearSelectedRowKeys();
  let obj = {
    ...defaultSearchInfo,
    superQueryJson: searchInfo.superQueryJson,
    ...data,
  };
  Object.keys(searchInfo).map(key => {
    delete searchInfo[key];
  });
  for (let [key, value] of Object.entries(obj)) {
    searchInfo[key.replaceAll('-', '_')] = value;
  }
  console.log(searchInfo);
  reload({ page: 1 });
}

function updateSearchFormValue() {
  if (!state.treeActiveId) return searchFormSubmit();
  let queryJson: any = {};
  let leftTreeActiveInfo: any = {};
  const isMultiple = !state.treeRelationObj ? false : state.treeRelationObj.searchMultiple;
  //多级左侧树，需要拼父级->转为查询参数
  if (state.treeRelationObj && state.treeRelationObj.xundaKey && ['organizeSelect', 'cascader', 'areaSelect'].includes(state.treeRelationObj.xundaKey)) {
    let currValue = [];
    currValue = state.treeActiveNodePath.map(o => o[state.treeFieldNames.key]);
    queryJson = { '': isMultiple ? [currValue] : currValue };
    leftTreeActiveInfo = { '': state.treeRelationObj.multiple ? [currValue] : currValue };
  } else {
    queryJson = { '': isMultiple ? [state.treeActiveId] : state.treeActiveId };
    leftTreeActiveInfo = { '': state.treeRelationObj.multiple ? [state.treeActiveId] : state.treeActiveId };
  }
  state.leftTreeActiveInfo = leftTreeActiveInfo;
  // 有搜索列表
  setFieldsValue(queryJson);
  searchFormSubmit();
}

function toPatientDetail(id) {
  if (!id) return;
  patientDetailRef.value?.init({ id });
}

function toPhysicianDetail(id) {
  if (!id) return;
  physicianDetailRef.value?.init({ id });
}
</script>
