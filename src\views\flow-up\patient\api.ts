import { defHttp } from '@/utils/http/axios';

enum Api {
  patient = '/api/flowup/patient',
}

// 获取列表
export function getList(data) {
  return defHttp.post({ url: Api.patient + `/getList`, data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: Api.patient, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: Api.patient + `/` + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: Api.patient + `/` + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: Api.patient + `/detail/` + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: Api.patient + `/` + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: Api.patient + `/batchRemove`, data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: Api.patient + `/export`, data });
}

// 导入模板下载
export function templateDownload() {
  return defHttp.get({ url: Api.patient + `/TemplateDownload` });
}
// 导入
export function importData(data) {
  return defHttp.post({ url: Api.patient + `/importData`, data });
}
// 导入预览
export function importPreview(data) {
  return defHttp.get({ url: Api.patient + `/importPreview`, data });
}
// 导出错误数据
export function exportExceptionData(data) {
  return defHttp.post({ url: Api.patient + `/exportExceptionData`, data });
}
// 生成账号
export function createAccount(data) {
  console.log('data', data);
  return defHttp.post({ url: Api.patient + `/createAccount`, data });
}

export function createAllAccount() {
  return defHttp.post({ url: Api.patient + `/createAllAccount` });
}
