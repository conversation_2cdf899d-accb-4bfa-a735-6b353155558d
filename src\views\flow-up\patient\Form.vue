<template>
  <BasicModal v-bind="$attrs" @register="registerModal" width="600px" :minHeight="100"
    :cancelText="t('common.cancelText', '取消')" :okText="t('common.okText', '确定')" @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="loat-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left"
        :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="name">
              <template #label>姓名 </template>
              <XundaInput v-model:value="dataForm.name" @change="changeData('name', -1)" placeholder="请输入"
                :allowClear="true" :style="{ width: '100%' }" :maskConfig="maskConfig.name" :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="age">
              <template #label>年龄 </template>
              <XundaInputNumber v-model:value="dataForm.age" @change="changeData('age', -1)" placeholder="请输入"
                :style="{ width: '100%' }" :step="1" :controls="false">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="sex">
              <template #label>性别 </template>
              <XundaSelect v-model:value="dataForm.sex" @change="changeData('sex', -1)" placeholder="请选择"
                :templateJson="state.interfaceRes.sex" :allowClear="true" :style="{ width: '100%' }" :showSearch="false"
                :options="optionsObj.sexOptions" :fieldNames="optionsObj.sexProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="admissionNo">
              <template #label>住院号 </template>
              <XundaInput v-model:value="dataForm.admissionNo" @change="changeData('admissionNo', -1)" placeholder="请输入"
                :allowClear="true" :style="{ width: '100%' }" :maskConfig="maskConfig.admissionNo" :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="idCard">
              <template #label>身份证 </template>
              <XundaInput v-model:value="dataForm.idCard" @change="changeData('idCard', -1)" placeholder="请输入"
                :allowClear="true" :style="{ width: '100%' }" :maskConfig="maskConfig.idCard" :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="addressDetail">
              <template #label>详细地址 </template>
              <XundaLocation v-model:value="dataForm.addressDetail"
                @change="(record: any) => changeAddressDetail(record)" :allowClear="true" :adjustmentScope="500">
              </XundaLocation>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="admittingDiagnosis">
              <template #label>入院诊断 </template>
              <XundaInput v-model:value="dataForm.admittingDiagnosis" @change="changeData('admittingDiagnosis', -1)"
                placeholder="请输入" :allowClear="true" :style="{ width: '100%' }"
                :maskConfig="maskConfig.admittingDiagnosis" :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="dischargeDate">
              <template #label>出院日期 </template>
              <XundaDatePicker v-model:value="dataForm.dischargeDate" @change="changeData('dischargeDate', -1)"
                placeholder="请选择" :allowClear="true" :style="{ width: '100%' }" format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')" :endTime="getRelationDate(false, 1, 1, '', '')">
              </XundaDatePicker>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="admissionDate">
              <template #label>入院日期 </template>
              <XundaDatePicker v-model:value="dataForm.admissionDate" @change="changeData('admissionDate', -1)"
                placeholder="请选择" :allowClear="true" :style="{ width: '100%' }" format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')" :endTime="getRelationDate(false, 1, 1, '', '')">
              </XundaDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="pipeBedPhysician">
              <template #label>管床医师 </template>
              <MyRelationForm v-model:value="dataForm.pipeBedPhysician" @change="changeData('pipeBedPhysician', -1)"
                placeholder="请选择" :templateJson="state.interfaceRes.pipeBedPhysician" :allowClear="true"
                :style="{ width: '100%' }" :showSearch="false" :field="'pipeBedPhysician'" modelId="661876504619124229"
                :columnOptions="optionsObj.pipeBedPhysiciancolumnOptions" :get-field-data-select="getPhysicianList"
                :get-data-change="getPhysicianInfo" relationField="name" popupWidth="800px" hasPage :pageSize="20">
              </MyRelationForm>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="dnumber">
              <template #label>随访应完成人数 </template>
              <XundaInputNumber v-model:value="dataForm.dnumber" @change="changeData('dnumber', -1)" placeholder="请输入"
                :style="{ width: '100%' }" :step="1" :controls="false">
              </XundaInputNumber>
            </a-form-item> </a-col><a-col :span="24" class="ant-col-item">
            <a-form-item name="Type">
              <template #label>随访类型 </template>
              <XundaSelect v-model:value="dataForm.type" @change="changeData('Type', -1)" placeholder="请选择"
                :templateJson="state.interfaceRes.type" :allowClear="true" :style="{ width: '100%' }"
                :showSearch="false" :options="optionsObj.typeOptions" :fieldNames="optionsObj.typeProps">
              </XundaSelect>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
import { create, update, getInfo } from './api';
import { getList as getPhysicianList, getInfo as getPhysicianInfo } from '@/views/flow-up/physician/api';
import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
import MyRelationForm from '@/components/MyRelationForm/MyRelationForm.vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { FormInstance } from 'ant-design-vue';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
// 表单权限
import { usePermission } from '@/hooks/web/usePermission';
import { getRelationDate } from '@/utils/myUtil';
interface State {
  dataForm: any;
  tableRows: any;
  dataRule: any;
  optionsObj: any;
  childIndex: any;
  isEdit: any;
  interfaceRes: any;
  //可选范围默认值
  ableAll: any;
  //掩码配置
  maskConfig: any;
  //定位属性
  locationScope: any;

  title: string;
  continueText: string;
  allList: any[];
  currIndex: number;
  isContinue: boolean;
  submitType: number;
  showContinueBtn: boolean;
}

const emit = defineEmits(['reload']);
const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const [registerModal, { openModal, setModalProps }] = useModal();
const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    name: undefined,
    age: undefined,
    sex: '',
    idCard: undefined,
    addressDetail: '',
    admittingDiagnosis: undefined,
    dischargeDate: undefined,
    admissionDate: undefined,
    pipeBedPhysician: '',
  },

  tableRows: {},
  dataRule: {
    name: [
      {
        required: true,
        message: t('sys.validate.textRequiredSuffix', '不能为空'),
        trigger: 'blur',
      },
    ],
    age: [
      {
        required: true,
        message: t('sys.validate.textRequiredSuffix', '不能为空'),
        trigger: ['blur', 'change'],
      },
    ],
    sex: [
      {
        required: true,
        message: t('sys.validate.arrayRequiredPrefix	', '请至少选择一个'),
        trigger: 'change',
      },
    ],
    idCard: [
      {
        required: true,
        message: t('sys.validate.textRequiredSuffix	', '不能为空'),
        trigger: ['blur', 'change'],
      },
    ],
  },

  optionsObj: {
    sexOptions: [],
    sexProps: { label: 'fullName', value: 'enCode' },
    pipeBedPhysiciancolumnOptions: [
      { label: '姓名', value: 'name' },
      { label: '性别', value: 'sex' },
      { label: '手机号', value: 'phone' },
      { label: '身份证号', value: 'idCard' },
      { label: '民族', value: 'nation' },
      { label: '单位任职', value: 'position' },
    ],
    typeOptions: [],
    typeProps: { label: 'fullName', value: 'enCode' },
  },

  childIndex: -1,
  isEdit: false,
  interfaceRes: {
    age: [],
    pipeBedPhysician: [],
    dischargeDate: [],
    name: [],
    idCard: [],
    admittingDiagnosis: [],
    sex: [],
    addressDetail: [],
    admissionDate: [],
  },
  //可选范围默认值
  ableAll: {},

  //掩码配置
  maskConfig: {
    fname: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    fidCard: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    fadmittingDiagnosis: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
  },

  //定位属性
  locationScope: {
    faddressDetail: [],
  },

  title: '',
  continueText: '',
  allList: [],
  currIndex: 0,
  isContinue: false,
  submitType: 0,
  showContinueBtn: true,
});
const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

const getPrevDisabled = computed(() => state.currIndex === 0);
const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
// 表单权限
const { hasFormP } = usePermission();

defineExpose({ init });

function init(data) {
  state.submitType = 0;
  state.isContinue = false;
  state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
  state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
  setFormProps({ continueLoading: false });
  state.dataForm.id = data.id;
  openModal();
  state.allList = data.allList;
  state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
  nextTick(() => {
    getForm().resetFields();
    setTimeout(initData, 0);
  });
}
function initData() {
  changeLoading(true);
  if (state.dataForm.id) {
    getData(state.dataForm.id);
  } else {
    //初始化options
    getfsexOptions();
    getTypeOptions();
    // 设置默认值
    state.dataForm = {
      name: undefined,
      age: undefined,
      sex: '',
      idCard: undefined,
      addressDetail: '',
      admittingDiagnosis: undefined,
      dischargeDate: undefined,
      admissionDate: undefined,
      pipeBedPhysician: '',
    };
    if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
    state.childIndex = -1;
    changeLoading(false);
  }
}
function getForm() {
  const form = unref(formRef);
  if (!form) {
    throw new Error('form is null!');
  }
  return form;
}
function getData(id) {
  getInfo(id).then(res => {
    state.dataForm = res.data || {};
    getfsexOptions();
    getTypeOptions();
    state.childIndex = -1;
    changeLoading(false);
  });
}
async function handleSubmit(type) {
  try {
    const values = await getForm()?.validate();
    if (!values) return;
    setFormProps({ confirmLoading: true });
    const formMethod = state.dataForm.id ? update : create;
    console.log('values', state.dataForm);
    formMethod(state.dataForm)
      .then(res => {
        createMessage.success(res.msg);
        setFormProps({ confirmLoading: false });
        if (state.submitType == 1) {
          initData();
          state.isContinue = true;
        } else {
          setFormProps({ open: false });
          emit('reload');
        }
      })
      .catch(() => {
        setFormProps({ confirmLoading: false });
      });
  } catch (_) { }
}

// 跳转上一个
function handlePrev() {
  state.currIndex--;
  handleGetNewInfo();
}

// 跳转下一个
function handleNext() {
  state.currIndex++;
  handleGetNewInfo();
}

// 重新获取编辑信息
function handleGetNewInfo() {
  changeLoading(true);
  getForm().resetFields();
  const id = state.allList[state.currIndex].id;
  getData(id);
}

function setFormProps(data) {
  setModalProps(data);
}
function changeLoading(loading) {
  setModalProps({ loading });
}

// 关闭弹窗
async function onClose() {
  if (state.isContinue) emit('reload');
  return true;
}

function changeData(model, index) {
  state.isEdit = false;
  state.childIndex = index;
  for (let key in state.interfaceRes) {
    if (key != model) {
      let faceReList = state.interfaceRes[key];
      for (let i = 0; i < faceReList.length; i++) {
        let relationField = faceReList[i].relationField;
        if (relationField) {
          let modelAll = relationField.split('-');
          let faceMode = '';
          let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
          for (let i = 0; i < modelAll.length; i++) {
            faceMode += modelAll[i];
          }
          if (faceMode == model || faceMode2 == model) {
            let options = 'get' + key + 'Options';
            eval(options)(true);
            changeData(key, index);
          }
        }
      }
    }
  }
}

//数据选项--数据字典初始化方法
function getfsexOptions() {
  getDictionaryDataSelector('963255a34ea64a2584c5d1ba269c1fe6').then(res => {
    state.optionsObj.sexOptions = res.data.list;
  });
}
//数据选项--数据字典初始化方法
function getTypeOptions() {
  getDictionaryDataSelector('664466612501358213').then(res => {
    state.optionsObj.typeOptions = res.data.list;
  });
}

function changeAddressDetail(data) {
  if (data) {
    let address = JSON.parse(data);
    state.dataForm.longitude = Number.parseFloat(address.lng);
    state.dataForm.latitude = Number.parseFloat(address.lat);
    state.dataForm.address = address.fullAddress;
  } else {
    state.dataForm.address = null;
    state.dataForm.longitude = null;
    state.dataForm.latitude = null;
  }
  changeData('addressDetail', -1);
}
</script>
