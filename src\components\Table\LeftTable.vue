<template>
  <div class="xunda-content-wrapper-search-box" v-if="showSearch">
    <BasicForm
      @register="registerSearchForm"
      :schemas="searchSchemas"
      @advanced-change="redoHeight"
      @submit="handleSearchSubmit"
      @reset="handleSearchReset"
      class="search-form">
    </BasicForm>
  </div>
  <div class="xunda-content-wrapper-content bg-white">
    <BasicTable @register="registerTable" ref="tableRef">
      <template #tableTitle v-if="showToolbar">
        <slot name="tableTitle"></slot>
      </template>
      <template #bodyCell="{ column, record }">
        <slot name="bodyCell" :column="column" :record="record"></slot>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, PropType } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableActionType } from '@/components/Table';
  import { FormSchema } from '@/components/Form/src/types/form';
  import { BasicColumn } from '@/components/Table/src/types/table';
  import { cloneDeep } from 'lodash-es';

  const props = defineProps({
    columns: {
      type: Array as PropType<BasicColumn[]>,
      required: true,
    },
    api: {
      type: Function as PropType<(...args: any[]) => Promise<any>>,
      required: true,
    },
    searchSchemas: {
      type: Array as PropType<FormSchema[]>,
      default: () => [],
    },
    showSearch: {
      type: Boolean,
      default: false,
    },
    showToolbar: {
      type: Boolean,
      default: false,
    },
    searchInfo: {
      type: Object as PropType<Recordable>,
      default: () => ({}),
    },
    tableProps: {
      type: Object,
      default: () => ({}),
    },
  });

  const emit = defineEmits<{
    (e: 'register'): void;
    (e: 'search-submit', values: Recordable): void;
    (e: 'search-reset'): void;
    (e: 'selection-change', key: string[], row: Recordable[]): void;
  }>();

  const tableRef = ref<Nullable<TableActionType>>(null);

  // 注册搜索表单
  const [registerSearchForm, { resetFields, setFieldsValue }] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 24,
    },
    compact: true,
  });

  // 注册表格
  const [registerTable, { reload, setLoading, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable(getTableProps() as any);
  function getTableProps() {
    const defaultTableProps = {
      api: props.api,
      columns: props.columns,
      searchInfo: props.searchInfo,
      clickToRowSelect: false,
      showTableSetting: false,
      rowSelection: {
        type: 'checkbox',
        onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
          emit('selection-change', selectedRowKeys, selectedRows);
        },
      },
    };
    return {
      ...defaultTableProps,
      ...props.tableProps,
    };
  }

  // 搜索提交
  function handleSearchSubmit(data: Recordable) {
    clearSelectedRowKeys();
    emit('search-submit', data);
    clearSelectedRowKeys();
    let obj = {
      ...props.searchInfo,
      superQueryJson: props.searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(props.searchInfo).map(key => {
      delete props.searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      props.searchInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(props.searchInfo);
    reload({ page: 1 });
  }

  // 搜索重置
  function handleSearchReset() {
    clearSelectedRowKeys();
    emit('search-reset');
  }

  // 暴露方法给父组件
  defineExpose({
    reload,
    setLoading,
    getSelectRowKeys,
    clearSelectedRowKeys,
    setFieldsValue,
    resetFields,
  });
</script>

<style lang="less" scoped>
  .xunda-content-wrapper {
    height: 100%;

    &-center {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    &-search-box {
      padding: 16px;
      background-color: #fff;
      border-radius: 2px;
    }

    &-content {
      flex: 1;
      overflow: hidden;
      padding: 16px;
      background: #fff;
      border-radius: 2px;
    }
  }
</style>
