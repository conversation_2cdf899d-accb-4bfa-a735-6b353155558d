# 自定义上传组件 (CustomUpload)

这个组件封装了基于 Ant Design Vue 的 `a-upload` 组件，支持自定义上传和下载接口，可以用于各种文件上传场景。

## 特性

- 支持自定义上传接口
- 支持自定义下载接口
- 文件类型限制
- 文件大小限制
- 文件数量限制
- 上传进度显示
- 文件预览和下载
- 自定义上传参数

## 使用方法

### 基本用法

```vue
<template>
  <CustomUpload
    v-model:value="fileList"
    buttonText="点击上传"
    tipText="支持上传任意类型文件，单个文件大小不超过10MB"
    :fileSize="10"
    sizeUnit="MB"
    @change="handleChange" />
</template>

<script setup>
  import { ref } from 'vue';
  import CustomUpload from '@/views/study/registration/components/CustomUpload/index.vue';

  const fileList = ref([]);

  function handleChange(files) {
    console.log('文件变更:', files);
  }
</script>
```

### 使用自定义接口

```vue
<template>
  <CustomUpload
    v-model:value="fileList"
    buttonText="点击上传"
    tipText="使用自定义接口上传文件"
    customUploadUrl="/api/custom/upload"
    customDownloadUrl="/api/custom/download"
    :uploadParams="{ type: 'custom', module: 'registration' }"
    @success="handleSuccess"
    @error="handleError" />
</template>

<script setup>
  import { ref } from 'vue';
  import CustomUpload from '@/views/study/registration/components/CustomUpload/index.vue';
  import { useMessage } from '@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const fileList = ref([]);

  function handleSuccess(file) {
    createMessage.success(`文件 ${file.name} 上传成功`);
  }

  function handleError(error) {
    createMessage.error(`上传失败: ${error.message}`);
  }
</script>
```

### 限制文件类型和数量

```vue
<template>
  <CustomUpload
    v-model:value="pdfFiles"
    accept=".pdf"
    buttonText="上传PDF文件"
    tipText="只能上传PDF文件，单个文件大小不超过5MB"
    :fileSize="5"
    sizeUnit="MB"
    :limit="3" />
</template>

<script setup>
  import { ref } from 'vue';
  import CustomUpload from '@/views/study/registration/components/CustomUpload/index.vue';

  const pdfFiles = ref([]);
</script>
```

## API

### Props

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| value/v-model | Array | [] | 文件列表 |
| disabled | Boolean | false | 是否禁用上传 |
| fileSize | Number | 10 | 文件大小限制 |
| sizeUnit | String | 'MB' | 文件大小单位，可选值：'KB'、'MB'、'GB' |
| limit | Number | 0 | 文件数量限制，0表示不限制 |
| tipText | String | '' | 提示文本 |
| showFileList | Boolean | true | 是否显示文件列表 |
| accept | String | '*' | 接受的文件类型 |
| multiple | Boolean | false | 是否支持多选 |
| buttonText | String | '点击上传' | 上传按钮文本 |
| showIcon | Boolean | true | 是否显示文件图标 |
| showView | Boolean | true | 是否显示预览按钮 |
| showDownload | Boolean | true | 是否显示下载按钮 |
| customUploadUrl | String | '' | 自定义上传接口URL |
| customDownloadUrl | String | '' | 自定义下载接口URL |
| uploadParams | Object | {} | 额外的上传参数 |

### 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| change | 文件列表变更事件 | (fileList: Array) |
| success | 文件上传成功事件 | (file: Object) |
| error | 文件上传失败事件 | (error: Error) |

### 接口格式

#### 上传接口预期格式

```json
{
  "code": 200,
  "data": {
    "fileId": "file123",
    "name": "example.pdf",
    "url": "/api/download/file123"
  },
  "message": "上传成功"
}
```

#### 下载接口预期格式

```json
{
  "code": 200,
  "data": {
    "url": "https://example.com/download/file123"
  },
  "message": "获取下载链接成功"
}
```
