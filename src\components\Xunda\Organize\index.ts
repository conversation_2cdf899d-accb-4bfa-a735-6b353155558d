import { withInstall } from '@/utils';
import OrganizeSelect from './src/OrganizeSelect.vue';
import OrganizeSelectAsync from './src/OrganizeSelectAsync.vue';
import DepSelect from './src/DepSelect.vue';
import DepSelectAsync from './src/DepSelectAsync.vue';
import PosSelect from './src/PosSelect.vue';
import GroupSelect from './src/GroupSelect.vue';
import RoleSelect from './src/RoleSelect.vue';
import UserSelect from './src/UserSelect.vue';
import UsersSelect from './src/UsersSelect.vue';

const isAsync = true;

export const XundaOrganizeSelect = withInstall(isAsync ? OrganizeSelectAsync : OrganizeSelect);
export const XundaDepSelect = withInstall(isAsync ? DepSelectAsync : DepSelect);
export const XundaPosSelect = withInstall(PosSelect);
export const XundaGroupSelect = withInstall(GroupSelect);
export const XundaRoleSelect = withInstall(RoleSelect);
export const XundaUserSelect = withInstall(UserSelect);
export const XundaUsersSelect = withInstall(UsersSelect);
