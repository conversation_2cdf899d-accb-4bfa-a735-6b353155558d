import type { Component } from 'vue';
import type { ComponentType } from './types/index';

/**
 * Component list, register here to setting it in the form
 */
import { StrengthMeter } from '@/components/StrengthMeter';
import { CountdownInput } from '@/components/CountDown';
// xunda 组件
import {
  XundaAlert,
  XundaAreaSelect,
  XundaAutoComplete,
  XundaButton,
  XundaCron,
  XundaCascader,
  XundaColorPicker,
  XundaCheckbox,
  XundaCheckboxSingle,
  XundaDatePicker,
  XundaDateRange,
  XundaTimePicker,
  XundaTimeRange,
  XundaMonthPicker,
  XundaWeekPicker,
  XundaDivider,
  XundaEditor,
  XundaGroupTitle,
  XundaIconPicker,
  XundaInput,
  XundaInputPassword,
  XundaInputGroup,
  XundaInputSearch,
  XundaTextarea,
  XundaInputNumber,
  XundaLink,
  XundaOpenData,
  XundaOrganizeSelect,
  XundaDepSelect,
  XundaPosSelect,
  XundaGroupSelect,
  XundaRoleSelect,
  XundaUserSelect,
  XundaUsersSelect,
  XundaQrcode,
  XundaBarcode,
  XundaRadio,
  XundaRate,
  XundaSelect,
  XundaSlider,
  XundaSign,
  XundaSignature,
  XundaSwitch,
  XundaText,
  XundaTreeSelect,
  XundaUploadFile,
  XundaUploadImg,
  XundaUploadImgSingle,
  XundaRelationForm,
  XundaRelationFormAttr,
  XundaPopupSelect,
  XundaPopupTableSelect,
  XundaPopupAttr,
  XundaNumberRange,
  XundaCalculate,
  XundaInputTable,
  XundaLocation,
  XundaIframe,
} from '@/components/Xunda';

const componentMap = new Map<ComponentType, Component>();

componentMap.set('StrengthMeter', StrengthMeter);
componentMap.set('InputCountDown', CountdownInput);

componentMap.set('InputGroup', XundaInputGroup);
componentMap.set('InputSearch', XundaInputSearch);
componentMap.set('MonthPicker', XundaMonthPicker);
componentMap.set('WeekPicker', XundaWeekPicker);

componentMap.set('Alert', XundaAlert);
componentMap.set('AreaSelect', XundaAreaSelect);
componentMap.set('AutoComplete', XundaAutoComplete);
componentMap.set('Button', XundaButton);
componentMap.set('Cron', XundaCron);
componentMap.set('Cascader', XundaCascader);
componentMap.set('ColorPicker', XundaColorPicker);
componentMap.set('Checkbox', XundaCheckbox);
componentMap.set('XundaCheckboxSingle', XundaCheckboxSingle);
componentMap.set('DatePicker', XundaDatePicker);
componentMap.set('DateRange', XundaDateRange);
componentMap.set('TimePicker', XundaTimePicker);
componentMap.set('TimeRange', XundaTimeRange);
componentMap.set('Divider', XundaDivider);
componentMap.set('Editor', XundaEditor);
componentMap.set('GroupTitle', XundaGroupTitle);
componentMap.set('Input', XundaInput);
componentMap.set('InputPassword', XundaInputPassword);
componentMap.set('Textarea', XundaTextarea);
componentMap.set('InputNumber', XundaInputNumber);
componentMap.set('IconPicker', XundaIconPicker);
componentMap.set('Link', XundaLink);
componentMap.set('OrganizeSelect', XundaOrganizeSelect);
componentMap.set('DepSelect', XundaDepSelect);
componentMap.set('PosSelect', XundaPosSelect);
componentMap.set('GroupSelect', XundaGroupSelect);
componentMap.set('RoleSelect', XundaRoleSelect);
componentMap.set('UserSelect', XundaUserSelect);
componentMap.set('UsersSelect', XundaUsersSelect);
componentMap.set('Qrcode', XundaQrcode);
componentMap.set('Barcode', XundaBarcode);
componentMap.set('Radio', XundaRadio);
componentMap.set('Rate', XundaRate);
componentMap.set('Select', XundaSelect);
componentMap.set('Slider', XundaSlider);
componentMap.set('Sign', XundaSign);
componentMap.set('Signature', XundaSignature);
componentMap.set('Switch', XundaSwitch);
componentMap.set('Text', XundaText);
componentMap.set('TreeSelect', XundaTreeSelect);
componentMap.set('UploadFile', XundaUploadFile);
componentMap.set('UploadImg', XundaUploadImg);
componentMap.set('UploadImgSingle', XundaUploadImgSingle);
componentMap.set('BillRule', XundaInput);
componentMap.set('ModifyUser', XundaInput);
componentMap.set('ModifyTime', XundaInput);
componentMap.set('CreateUser', XundaOpenData);
componentMap.set('CreateTime', XundaOpenData);
componentMap.set('CurrOrganize', XundaOpenData);
componentMap.set('CurrPosition', XundaOpenData);
componentMap.set('RelationForm', XundaRelationForm);
componentMap.set('RelationFormAttr', XundaRelationFormAttr);
componentMap.set('PopupSelect', XundaPopupSelect);
componentMap.set('PopupTableSelect', XundaPopupTableSelect);
componentMap.set('PopupAttr', XundaPopupAttr);
componentMap.set('NumberRange', XundaNumberRange);
componentMap.set('Calculate', XundaCalculate);
componentMap.set('InputTable', XundaInputTable);
componentMap.set('Location', XundaLocation);
componentMap.set('Iframe', XundaIframe);

export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export { componentMap };
