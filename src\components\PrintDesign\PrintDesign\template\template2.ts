export default [
  {
    options: {
      left: 235.5,
      top: 31.5,
      height: 28.5,
      width: 120,
      title: '培训记录',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 21.75,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 352.5000228881836,
      bottom: 54,
      vCenter: 292.5000228881836,
      hCenter: 39.75,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 444,
      top: 81,
      height: 12,
      width: 120,
      title: '编号：',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 11.25,
      right: 563.2500228881836,
      bottom: 92.25,
      vCenter: 503.2500228881836,
      hCenter: 86.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 100.5,
      height: 32,
      width: 435,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 296.25,
      bottom: 111.5,
      vCenter: 212.25,
      hCenter: 95.5,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 100.5,
      height: 32,
      width: 99,
      title: '培训主题',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 121.5,
      bottom: 105,
      vCenter: 75.75,
      hCenter: 93,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 132,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 565.74609375,
      bottom: 177.74609375,
      vCenter: 348.24609375,
      hCenter: 161.74609375,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 297,
      top: 132,
      height: 32,
      width: 99,
      title: '主讲师',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 395.99219512939453,
      bottom: 163.99219512939453,
      vCenter: 346.49219512939453,
      hCenter: 147.99219512939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 396,
      top: 132,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 566.49609375,
      bottom: 173.99609375,
      vCenter: 482.49609375,
      hCenter: 157.99609375,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 132,
      height: 32,
      width: 99,
      title: '培训时间',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 163.99219512939453,
      vCenter: 79.5,
      hCenter: 147.99219512939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 162,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 296.9974136352539,
      bottom: 195.48958587646484,
      vCenter: 212.9974136352539,
      hCenter: 179.48958587646484,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 162,
      height: 32,
      width: 99,
      title: '培训地点',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 195.48958587646484,
      vCenter: 79.5,
      hCenter: 179.48958587646484,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 127.5,
      top: 223.5,
      height: 81,
      width: 436.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 560.9921951293945,
      bottom: 319.48959732055664,
      vCenter: 343.49219512939453,
      hCenter: 278.98959732055664,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 225,
      height: 79.5,
      width: 99,
      title: '培训目的',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 127.48958587646484,
      bottom: 319.5000114440918,
      vCenter: 77.98958587646484,
      hCenter: 279.7500114440918,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 127.5,
      top: 304.5,
      height: 289.5,
      width: 436.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 562.5000228881836,
      bottom: 608.9922180175781,
      vCenter: 345.0000228881836,
      hCenter: 464.2422180175781,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 304.5,
      height: 289.5,
      width: 99,
      title: '培训内容摘要',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129,
      bottom: 563.9921951293945,
      vCenter: 79.5,
      hCenter: 419.24219512939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 127.5,
      top: 592.5,
      height: 111,
      width: 435,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 562.5000228881836,
      bottom: 718.5000457763672,
      vCenter: 345.0000228881836,
      hCenter: 663.0000457763672,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 594,
      height: 109.5,
      width: 99,
      title: '考评情况',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 127.48958587646484,
      bottom: 718.4974365234375,
      vCenter: 77.98958587646484,
      hCenter: 663.7474365234375,
      fontSize: 11.25,
      zIndex: 99,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 127.5,
      top: 703.5,
      height: 80,
      width: 435,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.9974136352539,
      bottom: 753.0000457763672,
      vCenter: 346.4974136352539,
      hCenter: 714.0000457763672,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 703.5,
      height: 80,
      width: 99,
      title: '备注',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 127.48958587646484,
      bottom: 798.4896087646484,
      vCenter: 77.98958587646484,
      hCenter: 758.4896087646484,
      fontSize: 11.25,
      zIndex: 99,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 297,
      top: 162,
      height: 32,
      width: 99,
      title: '记录人',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 394.49219512939453,
      bottom: 192.49219512939453,
      vCenter: 344.99219512939453,
      hCenter: 176.49219512939453,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 396,
      top: 162,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 566.49609375,
      bottom: 173.99609375,
      vCenter: 482.49609375,
      hCenter: 157.99609375,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 129,
      top: 193.5,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 563.9974136352539,
      bottom: 195.48958587646484,
      vCenter: 346.4974136352539,
      hCenter: 179.48958587646484,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 30,
      top: 193.5,
      height: 32,
      width: 99,
      title: '参加人数',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 127.4974136352539,
      bottom: 225.48958587646484,
      vCenter: 77.9974136352539,
      hCenter: 209.48958587646484,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 297,
      top: 193.5,
      height: 32,
      width: 99,
      title: '实到人数',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 395.99219512939453,
      bottom: 225.48958587646484,
      vCenter: 346.49219512939453,
      hCenter: 209.48958587646484,
      fontSize: 11.25,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 396,
      top: 193.5,
      height: 32,
      width: 168,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 566.49609375,
      bottom: 173.99609375,
      vCenter: 482.49609375,
      hCenter: 157.99609375,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 442.5,
      top: 790.5,
      height: 12,
      width: 120,
      title: '记录人签字：',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 11.25,
      right: 563.9974136352539,
      bottom: 802.4922180175781,
      vCenter: 503.9974136352539,
      hCenter: 796.4922180175781,
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
