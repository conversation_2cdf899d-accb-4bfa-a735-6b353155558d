import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';

const { t } = useI18n();

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'title',
    label: '资讯标题',
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
      placeholder: '请输入资讯标题',
    },
  },
  {
    field: 'category',
    label: '新闻分类',
    component: 'Select',
    componentProps: {
      placeholder: '请选择新闻分类',
      options: [
        { label: '文章', value: 'news' },
        { label: '视频', value: 'video' },
      ],
    },
  },
  {
    field: 'status',
    label: '发布状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择发布状态',
      options: [
        { label: '全部', value: null },
        { label: '已发布', value: true },
        { label: '草稿', value: false },
      ],
    },
  },
  {
    field: 'author',
    label: '作者',
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
      placeholder: '请输入作者姓名',
    },
  },
  {
    field: 'publishTimeRange',
    label: '发布时间',
    component: 'DatePicker',
    componentProps: {
      showTime: false,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: '封面图片',
    dataIndex: 'coverImage',
    width: 120,
    customRender({ record }) {
      record.coverImage = record.coverImage || '/api/file/Image/news/default.png';
    },
  },
  {
    title: '资讯标题',
    dataIndex: 'title',
    width: 200,
    ellipsis: true,
  },
  {
    title: '资讯分类',
    dataIndex: 'category',
    width: 120,
    customRender({ record }) {
      const categoryMap = {
        news: '文章',
        video: '视频',
      };
      return categoryMap[record.category] || record.category;
    },
  },
  {
    title: '作者',
    dataIndex: 'author',
    width: 120,
  },
  {
    title: '发布状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '置顶状态',
    dataIndex: 'isTop',
    width: 100,
  },
  {
    title: '阅读量',
    dataIndex: 'viewCount',
    width: 100,
    sorter: true,
  },
  {
    title: '点赞数',
    dataIndex: 'likeCount',
    width: 100,
    sorter: true,
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    width: 150,
    sorter: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    sorter: true,
  },
];

/**
 * 新增/编辑表单配置
 */
export const createFormSchemas: FormSchema[] = [
  {
    field: 'title',
    label: '资讯标题',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资讯标题',
      maxlength: 100,
      showCount: true,
    },
    rules: [
      { required: true, message: '请输入资讯标题' },
      { max: 100, message: '标题长度不能超过100个字符' },
    ],
  },
  {
    field: 'subtitle',
    label: '副标题',
    component: 'Input',
    componentProps: {
      placeholder: '请输入副标题（可选）',
      maxlength: 200,
      showCount: true,
    },
    rules: [{ max: 200, message: '副标题长度不能超过200个字符' }],
  },
  {
    field: 'category',
    label: '资讯分类',
    component: 'Select',
    componentProps: {
      placeholder: '请选择资讯分类',
      options: [
        { fullName: '文章', id: 'news' },
        { fullName: '视频', id: 'video' },
      ],
    },
    rules: [{ required: true, message: '请选择新闻分类' }],
  },
  {
    field: 'tags',
    label: '标签',
    component: 'Select',
    componentProps: {
      mode: 'tags',
      placeholder: '请输入标签，按回车添加',
      maxTagCount: 5,
    },
  },
  {
    field: 'coverImage',
    label: '封面图片',
    component: 'UploadImgSingle',
    componentProps: {
      type: 'newscover',
      tipText: '上传封面图片',
      accept: 'image/*',
    },
    rules: [{ required: true, message: '请上传封面图片' }],
  },
  {
    field: 'summary',
    label: '资讯摘要',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入资讯摘要',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
    rules: [
      { required: true, message: '请输入新闻摘要' },
      { max: 500, message: '摘要长度不能超过500个字符' },
    ],
  },
  {
    field: 'content',
    label: '资讯内容',
    component: 'Editor',
    componentProps: {
      placeholder: '请输入资讯内容',
      height: 400,
    },
    rules: [{ required: true, message: '请输入资讯内容' }],
    colProps: { span: 24 },
    ifShow: ({ values }) => values.category !== 'video',
  },
  {
    field: 'videoContent',
    label: '视频上传',
    component: 'UploadFile',
    componentProps: {
      type: 'video',
      accept: ['video/*'],
      fileSize: 500 * 1024 * 1024, // 500MB
      tipText: '点击上传视频文件',
    },
    rules: [{ required: true, message: '请上传视频文件' }],
    colProps: { span: 24 },
    ifShow: ({ values }) => values.category === 'video',
  },
  {
    field: 'author',
    label: '作者',
    component: 'Input',
    componentProps: {
      placeholder: '请输入作者姓名',
    },
    rules: [{ required: true, message: '请输入作者姓名' }],
  },
  {
    field: 'isTop',
    label: '是否置顶',
    component: 'Switch',
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
  },
  {
    field: 'status',
    label: '发布状态',
    component: 'Switch',
    componentProps: {
      checkedChildren: '发布',
      unCheckedChildren: '草稿',
    },
    defaultValue: false,
  },
  {
    field: 'publishTime',
    label: '发布时间',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择发布时间',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    ifShow: ({ values }) => values.status,
  },
  {
    field: 'allowComment',
    label: '允许评论',
    component: 'Switch',
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
    defaultValue: true,
  },
];
