import { defHttp } from '@/utils/http/axios';

const physician<PERSON><PERSON> = '/api/flowup/physician';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: physician<PERSON><PERSON> + '/getList', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url: physician<PERSON>pi, data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: physician<PERSON>pi + '/' + data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: physician<PERSON><PERSON> + '/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: physician<PERSON><PERSON> + '/detail/' + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: physician<PERSON><PERSON> + '/' + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: physician<PERSON><PERSON> + '/batchRemove', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: physician<PERSON><PERSON> + '/export', data });
}

export function createAllAccount() {
  return defHttp.post({ url: physicianApi + `/createAllAccount` });
}
