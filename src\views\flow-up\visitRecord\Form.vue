<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="800px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="left"
        :labelCol="{ style: { width: '120px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="patientId">
              <template #label>随访患者 </template>
              <MyRelationForm
                v-model:value="dataForm.patientId"
                @change="changeData('patientId', -1)"
                placeholder="请选择"
                :templateJson="state.interfaceRes.patientId"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :field="'patientId'"
                modelId="661952363199727173"
                :columnOptions="optionsObj.patientIdcolumnOptions"
                :get-field-data-select="getPatientList"
                :get-data-change="getPatientInfo"
                relationField="name"
                popupWidth="100%"
                hasPage
                :pageSize="20">
              </MyRelationForm>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="physicianId">
              <template #label>随访医师 </template>
              <MyRelationForm
                v-model:value="dataForm.physicianId"
                @change="changeData('physicianId', -1)"
                placeholder="请选择"
                :templateJson="state.interfaceRes.physicianId"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :field="'physicianId'"
                modelId="661876504619124229"
                :columnOptions="optionsObj.physicianIdcolumnOptions"
                :get-field-data-select="getPhysicianList"
                :get-data-change="getPhysicianInfo"
                relationField="name"
                popupWidth="100%"
                hasPage
                :pageSize="20">
              </MyRelationForm>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="visiteDate">
              <template #label>随访日期 </template>
              <XundaDatePicker
                v-model:value="dataForm.visiteDate"
                @change="changeData('visiteDate', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </XundaDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="situation">
              <template #label>随访情况 </template>
              <XundaTextarea v-model:value="dataForm.situation" @change="changeData('situation', -1)" :allowClear="true" :style="{ width: '100%' }">
              </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="attention">
              <template #label>注意事项 </template>
              <XundaTextarea v-model:value="dataForm.attention" @change="changeData('attention', -1)" :allowClear="true" :style="{ width: '100%' }">
              </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="advice">
              <template #label>随访建议 </template>
              <XundaTextarea v-model:value="dataForm.advice" @change="changeData('advice', -1)" :allowClear="true" :style="{ width: '100%' }"> </XundaTextarea>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="advice">
              <template #label>附件 </template>
              <XundaUploadFile v-model:value="dataForm.attachments" @change="attachmentsUpload" multiple> </XundaUploadFile>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="incisionHealing">
              <template #label>切口愈合情况 </template>
              <XundaRadio
                v-model:value="dataForm.incisionHealing"
                @change="changeData('incisionHealing', -1)"
                :templateJson="state.interfaceRes.incisionHealing"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.incisionHealingOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="selfCare">
              <template #label>生活自理能力情况 </template>
              <XundaRadio
                v-model:value="dataForm.selfCare"
                @change="changeData('selfCare', -1)"
                :templateJson="state.interfaceRes.selfCare"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.incisionHealingOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="medication">
              <template #label>规律服药情况 </template>
              <XundaRadio
                v-model:value="dataForm.medication"
                @change="changeData('medication', -1)"
                :templateJson="state.interfaceRes.medication"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="pain">
              <template #label>疼痛情况 </template>
              <XundaRadio
                v-model:value="dataForm.pain"
                @change="changeData('pain', -1)"
                :templateJson="state.interfaceRes.pain"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="dysfunction">
              <template #label>功能障碍情况 </template>
              <XundaRadio
                v-model:value="dataForm.dysfunction"
                @change="changeData('dysfunction', -1)"
                :templateJson="state.interfaceRes.dysfunction"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="regularReview">
              <template #label>定期复查 </template>
              <XundaRadio
                v-model:value="dataForm.regularReview"
                @change="changeData('regularReview', -1)"
                :templateJson="state.interfaceRes.regularReview"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="facialPalsy">
              <template #label>面瘫 </template>
              <XundaRadio
                v-model:value="dataForm.facialPalsy"
                @change="changeData('facialPalsy', -1)"
                :templateJson="state.interfaceRes.facialPalsy"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="epilepsy">
              <template #label>癫痫 </template>
              <XundaRadio
                v-model:value="dataForm.epilepsy"
                @change="changeData('epilepsy', -1)"
                :templateJson="state.interfaceRes.epilepsy"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="diplopia">
              <template #label>复视 </template>
              <XundaRadio
                v-model:value="dataForm.diplopia"
                @change="changeData('diplopia', -1)"
                :templateJson="state.interfaceRes.diplopia"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.isOptions"
                :fieldNames="optionsObj.defaultOptionProps"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="prosthesis">
              <template #label>肢具佩戴情况 </template>
              <XundaRadio
                v-model:value="dataForm.prosthesis"
                @change="changeData('prosthesis', -1)"
                :templateJson="state.interfaceRes.prosthesis"
                :style="{ width: '100%' }"
                size="default"
                :options="optionsObj.prosthesisOptions"
                :fieldNames="optionsObj.prosthesisOptions"
                direction="horizontal"
                optionType="default">
              </XundaRadio>
            </a-form-item>
          </a-col>

          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from './api';
  import { getList as getPhysicianList, getInfo as getPhysicianInfo } from '@/views/flow-up/physician/api';
  import { getList as getPatientList, getInfo as getPatientInfo } from '@/views/flow-up/patient/api';
  import { reactive, toRefs, nextTick, ref, unref, computed, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import MyRelationForm from '@/components/MyRelationForm/MyRelationForm.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { getRelationDate } from '@/utils/myUtil';
  import { XundaTextarea, XundaUploadFile } from '@/components/Xunda';
  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;

    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      patientId: '',
      physicianId: '',
      visiteDate: undefined,
      situation: undefined,
    },

    tableRows: {},

    dataRule: {
      patientId: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'change',
        },
      ],
      physicianId: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'change',
        },
      ],
      visiteDate: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'change',
        },
      ],
      situation: [
        {
          required: true,
          message: t('sys.validate.textRequiredSuffix', '不能为空'),
          trigger: 'blur',
        },
      ],
    },

    optionsObj: {
      patientIdcolumnOptions: [
        { label: '姓名', value: 'name' },
        { label: '年龄', value: 'age' },
        { label: '性别', value: 'sex' },
        { label: '身份证', value: 'idCard' },
        { label: '详细地址', value: 'address' },
        { label: '入院诊断', value: 'admittingDiagnosis' },
        { label: '入院日期', value: 'admissionDate' },
        { label: '出院日期', value: 'dischargeDate' },
      ],
      physicianIdcolumnOptions: [
        { label: '照片', value: 'picture', type: 'image' },
        { label: '姓名', value: 'name' },
        { label: '性别', value: 'sex' },
        { label: '手机号', value: 'phone' },
        { label: '身份证号', value: 'idCard' },
        { label: '家庭住址', value: 'address' },
        { label: '单位任职', value: 'position' },
      ],
      incisionHealingOptions: [
        { fullName: '好', id: '好' },
        { fullName: '一般', id: '一般' },
        { fullName: '差', id: '差' },
      ],

      isOptions: [
        { fullName: '是', id: '是' },
        { fullName: '否', id: '否' },
      ],
      prosthesisOptions: [
        { fullName: '一般', id: '一般' },
        { fullName: '良好', id: '良好' },
      ],
      defaultOptionProps: { label: 'fullName', value: 'id' },
    },

    childIndex: -1,
    isEdit: false,
    interfaceRes: { patientId: [], visiteDate: [], situation: [], physicianId: [] },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {},

    //定位属性
    locationScope: {},

    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  const patientId = ref('');

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    patientId.value = data.patientId || '';
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      //初始化options

      // 设置默认值
      state.dataForm = {
        patientId: patientId.value,
        physicianId: '',
        visiteDate: new Date(),
        situation: undefined,
      };
      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;

      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;

      var query = { ...state.dataForm };

      formMethod(query)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setModalProps({ loading });
  }
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function attachmentsUpload(info) {
    let fileList = [...info.fileList];
    // 只显示最近的上传5个文件
    fileList = fileList.slice(-5);
    // 清空失败的状态
    fileList = fileList.map(file => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });
    console.log(fileList);
  }
</script>
