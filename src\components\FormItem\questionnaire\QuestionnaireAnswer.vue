<template>
  <div>
    <div>
      <div v-if="radioQuestions.length > 0">
        <p>单选题</p>
        <div v-for="(item, index) in radioQuestions" :key="index">
          <div :id="item.id">
            <a-form-item :name="item.id">
              <div>
                <p>{{ numberToChinese(index + 1) }}. {{ item.question }}(单选题)</p>
                <XundaRadio v-model:value="item.answer" :disabled="readonly" class="radio" :style="{ width: '100%' }"
                  size="default" :options="item.options" :direction="direction" :fieldNames="defaultProps"
                  optionType="default">
                </XundaRadio>
              </div>
            </a-form-item>
          </div>
        </div>
      </div>
      <div v-if="checkboxQuestions.length > 0">
        <p>多选题</p>
        <div v-for="(item, index) in checkboxQuestions" :key="index">
          <div :id="item.id">
            <a-form-item :name="item.id">
              <div>
                <p>{{ numberToChinese(index + 1) }}. {{ item.question }}(多选题)</p>
                <XundaCheckbox v-model:value="item.selectAnswer" :style="{ width: '100%' }" size="default"
                  :options="item.options" :direction="direction" :fieldNames="defaultProps" optionType="default">
                </XundaCheckbox>
              </div>
            </a-form-item>
          </div>
        </div>
      </div>
      <div v-if="textAreaQuestions.length > 0">
        <p>简答题</p>
        <div v-for="(item, index) in textAreaQuestions" :key="index">
          <div :id="item.id">
            <a-form-item :name="item.id">
              <p>{{ numberToChinese(index + 1) }}. {{ item.question }}(简答题)</p>
              <XundaTextarea v-model:value="item.answer" :placeholder="t('common.inputText')" />
            </a-form-item>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, nextTick, reactive, ref, toRefs } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { relationFormProps, optionsColumn } from '.';
import { buildUUID } from '@/utils/uuid';
import { useI18n } from '@/hooks/web/useI18n';
import { numberToChinese, getDictionaryFullName } from '@/utils/myUtil';
interface State {
  selectedRowKeys: any[];
  defaultProps: any;
  childIndex: any;
}
defineOptions({ name: 'QuestionnaireAnswer', inheritAttrs: false });



const { t } = useI18n();
const { createMessage, createConfirm } = useMessage();
const props = defineProps(relationFormProps);
const emit = defineEmits(['update:value']);
const state = reactive<State>({
  defaultProps: { label: 'option', value: 'title' },
  childIndex: -1,
  selectedRowKeys: [],
});
const direction = 'vertical';//水平或者垂直 horizontal vertical

const { defaultProps } = toRefs(state);
const radioQuestions = computed(() => {
  let questions = props.questions || [];
  return questions.filter(item => item.type === 'radio');
});

const checkboxQuestions = computed(() => {
  let questions = props.questions || [];
  return questions.filter(item => item.type === 'checkbox');
});

const textAreaQuestions = computed(() => {
  let questions = props.questions || [];
  return questions.filter(item => item.type === 'textarea');
});

const activeKey = ref(['1']);

const betchDeleteQuestion = () => { };

const getRowSelection = computed(() => {
  if (!props.canSelected) return undefined;
  const rowSelection = {
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys: string[]) => {
      state.selectedRowKeys = selectedRowKeys;
    },
  };
  return rowSelection;
});

const addQuestion = (type: string) => {
  let item = {
    id: buildUUID(),
    type,
    options: [],
  };
  if (type === 'radio' || type === 'checkbox') {
    addOption(item);
  }
  let questions = props.questions || [];
  questions.push(item);
  nextTick(() => {
    scrollToElement(item.id);
  });
};

const addOption = item => {
  let options = item.options || [];
  let optionTitle = 'A';
  if (options.length >= 1) {
    let lastOption = options[options.length - 1];
    let lastOptionTitle = lastOption.title;
    optionTitle = String.fromCharCode(lastOptionTitle.charCodeAt(0) + 1);
  }
  options.push({
    id: buildUUID(),
    answer: '',
    title: optionTitle,
  });
  item.options = options;
};

const removeQuestion = (item, showConfirm = false) => {
  let questions = props.questions || [];
  if (showConfirm) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除该数据, 是否继续?',
      onOk: () => {
        questions = questions.filter(o => o.id !== item.id);
        emit('update:value', questions);
      },
    });
  } else {
    questions = questions.filter(o => o.id !== item.id);
    emit('update:value', questions);
  }
};

const batchRemoveRow = (showConfirm = false) => {
  if (!state.selectedRowKeys.length) return createMessage.error('请选择一条数据');
  const handleRemove = () => {
    let questions = props.questions || [];
    questions = questions.filter(o => !state.selectedRowKeys.includes(o.id));
    emit('update:value', questions);
    nextTick(() => {
      state.selectedRowKeys = [];
    });
  };
  if (showConfirm) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除该数据, 是否继续?',
      onOk: () => {
        handleRemove();
      },
    });
  } else {
    handleRemove();
  }
};

const getOptionColumns = computed(() => {
  let columns = optionsColumn || [];
  if (!props.readonly) {
    columns.push({
      title: '操作',
      key: 'action',
      width: 50,
      align: 'center',
      fixed: 'right',
    });
  }
  return columns;
});

const removeRowOptions = (item, index, showConfirm = false) => {
  let options = item.options || [];
  options.splice(index, 1);
  item.options = options;
};
const scrollToElement = id => {
  const element = document.getElementById(id);
  if (element) element.scrollIntoView({ behavior: 'smooth' });
};

</script>

<style lang="less" scoped>
:deep(.ant-radio-wrapper-disabled) {
  color: #000;
}

:deep(.ant-checkbox-inner) {}

:deep(.ant-checkbox-disabled+span) {
  color: #000;
}

:deep(.ant-input-disabled) {
  color: #000;
}
</style>