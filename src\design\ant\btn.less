// button reset
.ant-btn {
  &-link:hover,
  &-link:focus,
  &-link:active {
    border-color: transparent !important;
  }
  &-text:not([disabled]):hover,
  &-text:not([disabled]):focus,
  &-text:not([disabled]):active {
    border-color: transparent !important;
    color: inherit !important;
  }

  [data-theme='light'] &.ant-btn-link.is-disabled {
    color: rgb(0 0 0 / 25%);
    text-shadow: none;
    cursor: not-allowed !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none;
    &:hover {
      color: rgb(0 0 0 / 25%);
    }
  }

  [data-theme='dark'] &.ant-btn-link.is-disabled {
    color: rgb(255 255 255 / 25%) !important;
    text-shadow: none;
    cursor: not-allowed !important;
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none;
    &:hover {
      color: rgb(255 255 255 / 25%) !important;
    }
  }
  [data-theme='dark'] &.ant-btn-default {
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    color: #c9d1d9;
    border-color: #303030;
    background: transparent;
    &:hover,
    &:focus,
    &:active {
      background: transparent;
    }
  }

  [data-theme='dark'] & {
    &[disabled]:not(.ant-btn-text) {
      color: rgba(255, 255, 255, 0.3);
      background-color: rgba(255, 255, 255, 0.08);
      border-color: #424242;
      &:hover,
      &:focus,
      &:active {
        color: rgba(255, 255, 255, 0.3);
        background-color: rgba(255, 255, 255, 0.08);
        border-color: #424242;
      }
    }
    &.ant-btn-background-ghost:not(.ant-btn-primary, .ant-btn-warning, .ant-btn-success, .ant-btn-error) {
      color: #c9d1d9;
      border-color: rgba(255, 255, 255, 0.25);
      &:hover,
      &:focus,
      &:active {
        color: @primary-color;
        border-color: @primary-color;
      }
    }
  }

  // color: @white;

  &-success.ant-btn-link:not([disabled='disabled']) {
    color: @button-success-color;

    &:hover,
    &:focus {
      color: @button-success-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-success-active-color;
    }
  }

  &-success.ant-btn-link.ant-btn-loading,
  &-warning.ant-btn-link.ant-btn-loading,
  &-danger.ant-btn-link.ant-btn-loading,
  &-error.ant-btn-link.ant-btn-loading,
  &-info.ant-btn-link.ant-btn-loading,
  &-background-ghost.ant-btn-link.ant-btn-loading,
  &.ant-btn-link.ant-btn-loading {
    &::before {
      background: transparent;
    }
  }

  &-success:not(.ant-btn-link, .is-disabled),
  &-default&-success:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-success-color;
    border-color: @button-success-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-success-hover-color;
      border-color: @button-success-hover-color;
    }

    &:active {
      background-color: @button-success-active-color;
      border-color: @button-success-active-color;
    }
  }

  &-warning.ant-btn-link:not([disabled='disabled']) {
    color: @button-warn-color;

    &:hover,
    &:focus {
      color: @button-warn-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-warn-active-color;
    }
  }

  &-warning:not(.ant-btn-link, .is-disabled, .ant-btn-dashed),
  &-default&-warning:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-warn-color;
    border-color: @button-warn-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-warn-hover-color;
      border-color: @button-warn-hover-color;
    }

    &:active {
      background-color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }

    //&[disabled],
    //&[disabled]:hover {
    //  color: @white;
    //  background-color: fade(@button-warn-color, 40%);
    //  border-color: fade(@button-warn-color, 40%);
    //}
  }

  &-danger.ant-btn-link:not([disabled='disabled']),
  &-error.ant-btn-link:not([disabled='disabled']) {
    color: @button-error-color;

    &:hover,
    &:focus {
      color: @button-error-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-error-active-color;
    }
  }

  &-danger:not(.ant-btn-link, .is-disabled),
  &-error:not(.ant-btn-link, .is-disabled),
  &-default&-danger:not(.ant-btn-link, .is-disabled),
  &-default&-error:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-error-color;
    border-color: @button-error-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-error-hover-color;
      border-color: @button-error-hover-color;
    }

    &:active {
      background-color: @button-error-active-color;
      border-color: @button-error-active-color;
    }

    // &[disabled],
    // &[disabled]:hover {
    //   color: @white;
    //   background-color: fade(@button-error-color, 40%);
    //   border-color: fade(@button-error-color, 40%);
    // }
  }
  &-default&-dangerous:not(.ant-btn-link, .is-disabled) {
    border-color: @button-error-color;
    color: @button-error-color;
  }

  &-info.ant-btn-link:not([disabled='disabled']) {
    color: @button-info-color;

    &:hover,
    &:focus {
      color: @button-info-hover-color;
      border-color: transparent;
    }

    &:active {
      color: @button-info-active-color;
    }
  }

  &-info:not(.ant-btn-link, .is-disabled) {
    color: @white;
    background-color: @button-info-color;
    border-color: @button-info-color;
    //border-width: 0;

    &:hover,
    &:focus {
      color: @white;
      background-color: @button-info-hover-color;
      border-color: @button-info-hover-color;
    }

    &:active {
      background-color: @button-info-active-color;
      border-color: @button-info-active-color;
    }

    //&[disabled],
    //&[disabled]:hover {
    //  color: @white;
    //  background-color: fade(@button-info-color, 40%);
    //  border-color: fade(@button-info-color, 40%);
    //}
  }

  &-background-ghost,
  &-default&-background-ghost {
    border-width: 1px;
    background-color: transparent !important;

    &[disabled],
    &[disabled]:hover {
      color: fade(@white, 40%) !important;
      background-color: transparent !important;
      border-color: fade(@white, 40%) !important;
    }
  }

  &-dashed&-background-ghost,
  &-default&-background-ghost {
    color: @button-ghost-color;
    border-color: @button-ghost-color;

    &:hover,
    &:focus {
      color: @button-ghost-hover-color;
      border-color: @button-ghost-hover-color;
    }

    &:active {
      color: @button-ghost-active-color;
      border-color: @button-ghost-active-color;
    }

    &[disabled],
    &[disabled]:hover {
      color: fade(@white, 40%) !important;
      border-color: fade(@white, 40%) !important;
    }
  }
  &-dashed&-background-ghost&-warning {
    color: @button-warn-color;
    border-color: @button-warn-color;

    &:hover,
    &:focus {
      color: @button-warn-hover-color !important;
      border-color: @button-warn-hover-color !important;
    }

    &:active {
      color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }
  }

  &-default&-background-ghost&-success:not(.ant-btn-link) {
    color: @button-success-color;
    background-color: transparent;
    border-color: @button-success-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-success-hover-color !important;
      border-color: @button-success-hover-color;
    }

    &:active {
      color: @button-success-active-color;
      border-color: @button-success-active-color;
    }
  }

  &-default&-background-ghost&-warning:not(.ant-btn-link) {
    color: @button-warn-color;
    background-color: transparent;
    border-color: @button-warn-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-warn-hover-color !important;
      border-color: @button-warn-hover-color;
    }

    &:active {
      color: @button-warn-active-color;
      border-color: @button-warn-active-color;
    }
  }

  &-default&-background-ghost&-danger:not(.ant-btn-link),
  &-default&-background-ghost&-dangerous:not(.ant-btn-link),
  &-default&-background-ghost&-error:not(.ant-btn-link) {
    color: @button-error-color;
    background-color: transparent;
    border-color: @button-error-color;
    border-width: 1px;

    &:hover,
    &:focus {
      color: @button-error-hover-color !important;
      border-color: @button-error-hover-color;
    }

    &:active {
      color: @button-error-active-color;
      border-color: @button-error-active-color;
    }
  }

  &-ghost.ant-btn-link:not([disabled='disabled']) {
    color: @button-ghost-color;

    &:hover,
    &:focus {
      color: @button-ghost-hover-color;
      border-color: transparent;
    }
  }
  &:not(.ant-btn-link) {
    .anticon {
      vertical-align: middle;
    }
  }
}
