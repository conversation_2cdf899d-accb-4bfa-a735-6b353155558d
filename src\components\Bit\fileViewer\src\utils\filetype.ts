import { getDownloadUrl } from '@/api/basic/common';

// 文件类型定义
export enum FileType {
  PDF = 'pdf',
  IMAGE = 'image',
  TEXT = 'text',
  VIDEO = 'video',
  AUDIO = 'audio',
  OFFICE = 'office',
  ZIP = 'zip',
  UNKNOWN = 'unknown',
}

// 文件扩展名映射
const extensionMap: Record<string, FileType> = {
  // PDF文件
  pdf: FileType.PDF,

  // 图片文件
  jpg: FileType.IMAGE,
  jpeg: FileType.IMAGE,
  png: FileType.IMAGE,
  gif: FileType.IMAGE,
  bmp: FileType.IMAGE,
  webp: FileType.IMAGE,

  // 文本文件
  txt: FileType.TEXT,
  json: FileType.TEXT,
  xml: FileType.TEXT,
  md: FileType.TEXT,
  log: FileType.TEXT,
  js: FileType.TEXT,
  ts: FileType.TEXT,
  css: FileType.TEXT,
  html: FileType.TEXT,
  htm: FileType.TEXT,

  // 视频文件
  mp4: FileType.VIDEO,
  webm: FileType.VIDEO,
  ogg: FileType.VIDEO,

  // 音频文件
  mp3: FileType.AUDIO,
  wav: FileType.AUDIO,

  // Office文件
  doc: FileType.OFFICE,
  docx: FileType.OFFICE,
  xls: FileType.OFFICE,
  xlsx: FileType.OFFICE,
  ppt: FileType.OFFICE,
  pptx: FileType.OFFICE,

  // 压缩文件
  zip: FileType.ZIP,
  rar: FileType.ZIP,
  tar: FileType.ZIP,
  gz: FileType.ZIP,
  _7z: FileType.ZIP,
};

// MIME类型映射
const mimeMap: Record<string, FileType> = {
  // PDF
  'application/pdf': FileType.PDF,

  // 图片
  'image/jpeg': FileType.IMAGE,
  'image/png': FileType.IMAGE,
  'image/gif': FileType.IMAGE,
  'image/bmp': FileType.IMAGE,
  'image/webp': FileType.IMAGE,

  // 文本
  'text/plain': FileType.TEXT,
  'application/json': FileType.TEXT,
  'text/xml': FileType.TEXT,
  'text/markdown': FileType.TEXT,
  'text/javascript': FileType.TEXT,
  'text/typescript': FileType.TEXT,
  'text/css': FileType.TEXT,
  'text/html': FileType.TEXT,

  // 视频
  'video/mp4': FileType.VIDEO,
  'video/webm': FileType.VIDEO,
  'video/ogg': FileType.VIDEO,

  // 音频
  'audio/mpeg': FileType.AUDIO,
  'audio/wav': FileType.AUDIO,
  'audio/ogg': FileType.AUDIO,

  // Office
  'application/msword': FileType.OFFICE,
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': FileType.OFFICE,
  'application/vnd.ms-excel': FileType.OFFICE,
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': FileType.OFFICE,
  'application/vnd.ms-powerpoint': FileType.OFFICE,
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': FileType.OFFICE,
};

/**
 * 从文件扩展名获取文件类型
 */
export function getFileTypeFromExtension(filename: string): FileType {
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  return extensionMap[extension] || FileType.UNKNOWN;
}

/**
 * 从MIME类型获取文件类型
 */
export function getFileTypeFromMime(mimeType: string): FileType {
  return mimeMap[mimeType] || FileType.UNKNOWN;
}

/**
 * 从文件对象获取文件类型
 */
export async function getFileType(file: File | Blob): Promise<FileType> {
  // 如果是File对象，先尝试从文件名获取
  if (file instanceof File && file.name) {
    const typeFromExt = getFileTypeFromExtension(file.name);
    if (typeFromExt !== FileType.UNKNOWN) {
      return typeFromExt;
    }
  }

  // 从MIME类型获取
  const typeFromMime = getFileTypeFromMime(file.type);
  if (typeFromMime !== FileType.UNKNOWN) {
    return typeFromMime;
  }

  // 如果是PDF，尝试通过魔数识别
  if (file.size >= 4) {
    const buffer = await file.slice(0, 4).arrayBuffer();
    const header = new Uint8Array(buffer);
    if (
      header[0] === 0x25 && // %
      header[1] === 0x50 && // P
      header[2] === 0x44 && // D
      header[3] === 0x46
    ) {
      // F
      return FileType.PDF;
    }
  }

  return FileType.UNKNOWN;
}

/**
 * 从URL获取文件类型
 */
export async function getFileTypeFromUrl(url: string): Promise<FileType> {
  // 先尝试从URL扩展名获取
  const typeFromExt = getFileTypeFromExtension(url);
  if (typeFromExt !== FileType.UNKNOWN) {
    return typeFromExt;
  }

  try {
    // 发送HEAD请求获取Content-Type
    const response = await fetch(url, { method: 'HEAD' });
    const contentType = response.headers.get('content-type');
    if (contentType) {
      const typeFromMime = getFileTypeFromMime(contentType.split(';')[0]);
      if (typeFromMime !== FileType.UNKNOWN) {
        return typeFromMime;
      }
    }
  } catch (error) {
    console.warn('Failed to fetch content type:', error);
  }

  return FileType.UNKNOWN;
}

/**
 * 获取文件类型的显示名称
 */
export function getFileTypeDisplayName(type: FileType): string {
  const displayNames: Record<FileType, string> = {
    [FileType.PDF]: 'PDF文档',
    [FileType.IMAGE]: '图片',
    [FileType.TEXT]: '文本文件',
    [FileType.VIDEO]: '视频',
    [FileType.AUDIO]: '音频',
    [FileType.OFFICE]: 'Office文档',
    [FileType.ZIP]: '压缩文件',
    [FileType.UNKNOWN]: '未知文件类型',
  };
  return displayNames[type];
}
