import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { toDateString } from '@/utils/myUtil';
const { t } = useI18n();

export const createFormSchenmas: FormSchema[] = [
  {
    field: 'picture',
    label: '照片',
    component: 'UploadImgSingle',
    componentProps: { type: 'annexpic', tipText: '上传照片' },
    rules: [{ required: true, trigger: 'change', message: '必填' }],
  },
  {
    field: 'name',
    label: '照片',
    component: 'Input',
    colProps: { sm: 24, span: 24 },
  },
];

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
  {
    field: 'patientAdmissionNo',
    label: t('随访患者住院号'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'patientName',
    label: t('随访患者姓名'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
  {
    field: 'physicianName',
    label: t('随访医师'),
    component: 'Input',
    componentProps: {
      submitOnPressEnter: true,
    },
  },

  {
    field: 'visiteDate',
    label: t('随访日期'),
    component: 'DatePicker',
    componentProps: {
      submitOnPressEnter: true,
    },
  },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: t('随访患者住院号'),
    dataIndex: 'patientAdmissionNo',
    width: 120,
    fixed: 'left',
  },
  {
    title: '随访患者',
    dataIndex: 'patientId',
    width: 120,
    fixed: 'left',
  },
  {
    title: '随访医师',
    dataIndex: 'physicianId',
    width: 120,
  },
  {
    title: '随访日期',
    dataIndex: 'visiteDate',
    width: 120,
    customRender({ record }) {
      return toDateString(record.visiteDate, 'YYYY年MM月DD日');
    },
  },
  {
    title: '随访情况',
    dataIndex: 'situation',
    width: 120,
  },
  {
    title: '注意事项',
    dataIndex: 'attention',
    width: 120,
  },
  {
    title: '随访建议',
    dataIndex: 'advice',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'note',
    width: 120,
  },
];
