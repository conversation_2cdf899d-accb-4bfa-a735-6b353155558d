import { FormSchema } from "@/components/Form";
import { useI18n } from "@/hooks/web/useI18n";
import { BasicColumn } from "@/components/Table/src/types/table";
import { toDateString, toFixedPercent } from "@/utils/myUtil";
const { t } = useI18n();


/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
    {
        field: 'name',
        label: t('姓名'),
        component: 'Input',
        componentProps: {
            submitOnPressEnter: true,
        },
    },
    {
        field: 'admissionNo',
        label: t('住院号'),
        component: 'Input',
        componentProps: {
            submitOnPressEnter: true,
        },
    },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
    {
        title: '问卷标题',
        dataIndex: 'questionnaireTitle',
        width: 120,
    },
    {
        title: '问卷患者',
        dataIndex: 'patientName',
        width: 120,
    },

    {
        title: '是否填写',
        dataIndex: 'isAnswered',
        width: 120,
        customRender({ record }) {
            return record.isAnswered ? '是' : '否';
        }
    },
];



/**
 * 表格列配置
 */
export const patientColumns: BasicColumn[] = [

    {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
    },
    {
        title: '年龄',
        dataIndex: 'age',
        width: 120,
    },
    {
        title: '性别',
        dataIndex: 'sex',
        width: 120,
    },
    {
        title: '住院号',
        dataIndex: 'admissionNo',
        width: 120,
    },
    {
        title: '身份证号',
        dataIndex: 'idCard',
        width: 120,
    },
    {
        title: '地址',
        dataIndex: 'address',
        width: 200,
    },
    {
        title: '出院日期',
        dataIndex: 'dischargeDate',
        width: 120,
        customRender({ record }) {
            return toDateString(record.dischargeDate, 'YYYY年MM月DD日')
        }
    },
    {
        title: '入院日期',
        dataIndex: 'admissionDate',
        width: 120,
        customRender({ record }) {
            return toDateString(record.admissionDate, 'YYYY年MM月DD日')
        }
    },
    {
        title: '管床医师',
        dataIndex: 'pipeBedPhysician',
        width: 120,
    },
    // {
    //     title: '随访总人数',
    //     dataIndex: 'tnumber',
    //     width: 120,
    // },
    {
        title: '随访应完成人数',
        dataIndex: 'dnumber',
        width: 120,
    },
    {
        title: '随访次数',
        dataIndex: 'fcount',
        width: 120,
    },
    {
        title: '随访率',
        dataIndex: 'rate',
        width: 120,
        customRender({ record }) {
            return toFixedPercent(record.rate);
        }
    },
    {
        title: '随访次数类型',
        dataIndex: 'type',
        width: 120,
    }
];
