export default [
  {
    options: {
      left: 237,
      top: 21,
      height: 34.5,
      width: 120,
      title: '入职申请表',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 21,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 372,
      top: 88.5,
      height: 13.5,
      width: 177,
      title: '填表时间：',
      right: 491.25,
      bottom: 84,
      vCenter: 431.25,
      hCenter: 77.25,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'Microsoft YaHei',
      fontSize: 12,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 88.5,
      height: 13.5,
      width: 177,
      title: '编号：',
      right: 219.24609375,
      bottom: 83.49609375,
      vCenter: 130.74609375,
      hCenter: 76.74609375,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'Microsoft YaHei',
      fontSize: 12,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 211.5,
      top: 108,
      height: 32,
      width: 49.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 275.2499771118164,
      bottom: 85.24147415161133,
      vCenter: 238.4999771118164,
      hCenter: 69.24147415161133,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 309,
      top: 108,
      height: 32,
      width: 49.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 275.2499771118164,
      bottom: 85.24147415161133,
      vCenter: 238.4999771118164,
      hCenter: 69.24147415161133,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 261,
      top: 108,
      height: 32,
      width: 49.5,
      title: '年龄',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 310.5,
      bottom: 122,
      vCenter: 285.75,
      hCenter: 106,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 358.5,
      top: 108,
      height: 32,
      width: 49.5,
      title: '民族',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 408,
      bottom: 122,
      vCenter: 383.25,
      hCenter: 106,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 108,
      height: 32,
      width: 49.5,
      title: '性别',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 201.75,
      bottom: 85.99147415161133,
      vCenter: 177,
      hCenter: 69.99147415161133,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 406.5,
      top: 108,
      height: 32,
      width: 49.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 275.2499771118164,
      bottom: 85.24147415161133,
      vCenter: 238.4999771118164,
      hCenter: 69.24147415161133,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 90,
      top: 108,
      height: 32,
      width: 73.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 129.99288940429688,
      bottom: 84.74502944946289,
      vCenter: 105.24288940429688,
      hCenter: 68.74502944946289,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 456,
      top: 108,
      height: 126,
      width: 95.5,
      title: '照片',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 216.75,
      vCenter: 503.75,
      hCenter: 153.75,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 108,
      height: 32,
      width: 51,
      title: '姓名',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 79.5,
      bottom: 85.99361419677734,
      vCenter: 54.75,
      hCenter: 69.99361419677734,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 139.5,
      height: 32,
      width: 97.5,
      title: '申请职位',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 261,
      bottom: 153.5,
      vCenter: 212.25,
      hCenter: 137.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 90,
      top: 139.5,
      height: 32,
      width: 73.5,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 129.99288940429688,
      bottom: 84.74502944946289,
      vCenter: 105.24288940429688,
      hCenter: 68.74502944946289,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 261,
      top: 139.5,
      height: 32,
      width: 195,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 494.2478713989258,
      bottom: 116.74360275268555,
      vCenter: 371.9978713989258,
      hCenter: 100.74360275268555,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 139.5,
      height: 32,
      width: 51,
      title: '学历',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 91.5,
      bottom: 153.5,
      vCenter: 66,
      hCenter: 137.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 171,
      height: 32,
      width: 292.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 227.24571990966797,
      bottom: 149.74573135375977,
      vCenter: 190.49571990966797,
      hCenter: 133.74573135375977,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 171,
      height: 32,
      width: 123,
      title: '身份证号码',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 163.5,
      bottom: 185,
      vCenter: 102,
      hCenter: 169,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 202.5,
      height: 32,
      width: 292.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 227.24571990966797,
      bottom: 149.74573135375977,
      vCenter: 190.49571990966797,
      hCenter: 133.74573135375977,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 202.5,
      height: 32,
      width: 123,
      title: '现居地址',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 163.5,
      bottom: 216.5,
      vCenter: 102,
      hCenter: 200.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 234,
      height: 32,
      width: 388,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 227.24571990966797,
      bottom: 149.74573135375977,
      vCenter: 190.49571990966797,
      hCenter: 133.74573135375977,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 234,
      height: 32,
      width: 123,
      title: '婚姻状况',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 163.5,
      bottom: 248,
      vCenter: 102,
      hCenter: 232,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 180,
      top: 241.5,
      height: 17,
      width: 17,
      coordinateSync: false,
      widthHeightSync: true,
      right: 196.25,
      bottom: 242,
      vCenter: 187.75,
      hCenter: 233.5,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },
  {
    options: {
      left: 282,
      top: 241.5,
      height: 17,
      width: 17,
      coordinateSync: false,
      widthHeightSync: true,
      right: 196.25,
      bottom: 242,
      vCenter: 187.75,
      hCenter: 233.5,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },
  {
    options: { left: 210, top: 246, height: 13.5, width: 34.5, title: '未婚', coordinateSync: false, widthHeightSync: false, fontSize: 11.25 },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: { left: 312, top: 246, height: 13.5, width: 34.5, title: '已婚', coordinateSync: false, widthHeightSync: false, fontSize: 11.25 },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 265.5,
      height: 32,
      width: 130,
      title: '联系方式',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 279.5,
      vCenter: 357.5,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 265.5,
      height: 32,
      width: 132,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 292,
      bottom: 279.5,
      vCenter: 227,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 265.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 279.5,
      vCenter: 486.5,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 265.5,
      height: 32,
      width: 123,
      title: '健康状况',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 163.5,
      bottom: 279.5,
      vCenter: 102,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 297,
      height: 32,
      width: 130,
      title: '院校名称',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 311,
      vCenter: 357.5,
      hCenter: 295,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 297,
      height: 32,
      width: 130,
      title: '起止日期',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 293.5,
      bottom: 311,
      vCenter: 228.5,
      hCenter: 295,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 297,
      height: 32,
      width: 130,
      title: '专业',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 311,
      vCenter: 486.5,
      hCenter: 295,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 297,
      height: 126,
      width: 123,
      title: '教育经历',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 164.24146270751953,
      bottom: 392.24573135375977,
      vCenter: 102.74146270751953,
      hCenter: 329.24573135375977,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 328.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 342.5,
      vCenter: 357.5,
      hCenter: 326.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 328.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 342.5,
      vCenter: 486.5,
      hCenter: 326.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 328.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 293.4978485107422,
      bottom: 330.5000114440918,
      vCenter: 228.4978485107422,
      hCenter: 314.5000114440918,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 360,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 374,
      vCenter: 357.5,
      hCenter: 358,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 360,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 374,
      vCenter: 486.5,
      hCenter: 358,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 360,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 293.5,
      bottom: 374,
      vCenter: 228.5,
      hCenter: 358,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 391.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 405.5,
      vCenter: 357.5,
      hCenter: 389.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 391.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 405.5,
      vCenter: 486.5,
      hCenter: 389.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 391.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 294.25,
      bottom: 406.25,
      vCenter: 229.25,
      hCenter: 390.25,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 423,
      height: 32,
      width: 130,
      title: '工作单位',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 437,
      vCenter: 357.5,
      hCenter: 421,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 423,
      height: 32,
      width: 130,
      title: '起止日期',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 293.7471466064453,
      bottom: 422.99500370025635,
      vCenter: 228.7471466064453,
      hCenter: 406.99500370025635,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 423,
      height: 32,
      width: 130,
      title: '职位',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 437,
      vCenter: 486.5,
      hCenter: 421,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 423,
      height: 126,
      width: 123,
      title: '工作经历',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 152.49288940429688,
      bottom: 275.9950294494629,
      vCenter: 90.99288940429688,
      hCenter: 259.9950294494629,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 454.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 468.5,
      vCenter: 357.5,
      hCenter: 452.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 454.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 468.5,
      vCenter: 486.5,
      hCenter: 452.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 454.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 293.5,
      bottom: 468.5,
      vCenter: 228.5,
      hCenter: 452.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 486,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 500,
      vCenter: 357.5,
      hCenter: 484,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 486,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 500,
      vCenter: 486.5,
      hCenter: 484,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 486,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 293.5,
      bottom: 500,
      vCenter: 228.5,
      hCenter: 484,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 292.5,
      top: 517.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 422.5,
      bottom: 531.5,
      vCenter: 357.5,
      hCenter: 515.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 517.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 531.5,
      vCenter: 486.5,
      hCenter: 515.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 517.5,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 293.5,
      bottom: 531.5,
      vCenter: 228.5,
      hCenter: 515.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 163.5,
      top: 549,
      height: 32,
      width: 388,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 563,
      vCenter: 357.5,
      hCenter: 547,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 549,
      height: 32,
      width: 123,
      title: '奖励或专业证书',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 163.5,
      bottom: 563,
      vCenter: 102,
      hCenter: 547,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 210,
      top: 580.5,
      height: 32,
      width: 85,
      title: '能否出差',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 295,
      bottom: 594.5,
      vCenter: 252.5,
      hCenter: 578.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 580.5,
      height: 32,
      width: 85,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 379,
      bottom: 594.5,
      vCenter: 336.5,
      hCenter: 578.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 378,
      top: 580.5,
      height: 32,
      width: 85,
      title: '能否接受调动',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 463,
      bottom: 594.5,
      vCenter: 420.5,
      hCenter: 578.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 126,
      top: 580.5,
      height: 32,
      width: 85,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 211,
      bottom: 594.5,
      vCenter: 168.5,
      hCenter: 578.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 462,
      top: 580.5,
      height: 32,
      width: 89,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 551,
      bottom: 594.5,
      vCenter: 506.5,
      hCenter: 578.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 580.5,
      height: 32,
      width: 86,
      title: '能否加班',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 126.5,
      bottom: 594.5,
      vCenter: 83.5,
      hCenter: 578.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 294,
      top: 612,
      height: 32,
      width: 127.5,
      title: '联系方式',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 424,
      bottom: 626,
      vCenter: 359,
      hCenter: 610,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      zIndex: 99,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 162,
      top: 612,
      height: 32,
      width: 132,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      qrCodeLevel: 0,
      right: 292,
      bottom: 279.5,
      vCenter: 227,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 421.5,
      top: 612,
      height: 32,
      width: 130,
      title: '',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 551.5,
      bottom: 279.5,
      vCenter: 486.5,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 612,
      height: 32,
      width: 123,
      title: '健康状况',
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderLeft: 'solid',
      borderTop: 'solid',
      borderRight: 'solid',
      qrCodeLevel: 0,
      right: 163.5,
      bottom: 279.5,
      vCenter: 102,
      hCenter: 263.5,
      hideTitle: true,
      fontFamily: 'SimSun',
      fontSize: 11.25,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 643.5,
      height: 127.5,
      width: 511,
      right: 129,
      bottom: 739.5,
      vCenter: 84,
      hCenter: 694.5,
      coordinateSync: false,
      widthHeightSync: false,
    },
    printElementType: { title: '矩形', type: 'rect' },
  },
  {
    options: {
      left: 52.5,
      top: 660,
      height: 66,
      width: 486,
      title:
        '   本人承诺:以上所填信息全部属实，并已经与前用人单位解除劳动合同，如与事实不符，个人愿无条件接受公司处罚甚至辞退，并愿负全部责任。入职后服从公司工作安排，遵守公司各项规章制度。',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 11.25,
      lineHeight: 21,
      longTextIndent: 24,
      right: 538.5,
      bottom: 709.5,
      vCenter: 295.5,
      hCenter: 685.5,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 301.5,
      top: 750,
      height: 13.5,
      width: 63,
      title: '签名确认：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 366,
      bottom: 763.5,
      vCenter: 334.5,
      hCenter: 756.75,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 438,
      top: 750,
      height: 13.5,
      width: 63,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      right: 486,
      bottom: 762.75,
      vCenter: 454.5,
      hCenter: 756,
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
