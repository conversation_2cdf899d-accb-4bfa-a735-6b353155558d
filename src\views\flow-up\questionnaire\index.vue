<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm @register="registerSearchForm" :schemas="searchSchemas" @advanced-change="redoHeight"
          @submit="handleSearchSubmit" @reset="handleSearchReset" class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addHandle()"> {{ t('common.add2Text',
              '新增') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{
              t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="!(record.top || column.id?.includes('-'))">
              <template v-if="column.dataIndex === 'pipeBedPhysician'">
                <p class="link-text" @click="toPhysicianDetail(record.pipeBedPhysician)"> {{ record.physicianName }}</p>
              </template>
              <template v-if="column.xundaKey === 'relationForm'">
                <p class="link-text" @click="toDetail(column.modelId, record[column.dataIndex + `_id`])"> {{
                  record[column.dataIndex] }}</p>
              </template>
              <template v-if="column.xundaKey === 'inputNumber'">
                <xunda-input-number v-model:value="record[column.prop]" :precision="column.precision"
                  :thousands="column.thousands" disabled detailed />
              </template>
              <template v-if="column.xundaKey === 'calculate'">
                <xunda-calculate v-model:value="record[column.prop]" :isStorage="column.isStorage"
                  :precision="column.precision" :thousands="column.thousands" detailed />
              </template>
              <template v-if="column.xundaKey === 'sign'">
                <xunda-sign v-model:value="record[column.prop]" detailed />
              </template>
              <template v-if="column.xundaKey === 'signature'">
                <xunda-signature v-model:value="record[column.prop]" detailed />
              </template>
              <template v-if="column.xundaKey === 'rate'">
                <xunda-rate v-model:value="record[column.prop]" :count="column.count" :allowHalf="column.allowHalf"
                  disabled />
              </template>
              <template v-if="column.xundaKey === 'slider'">
                <xunda-slider v-model:value="record[column.prop]" :min="column.min" :max="column.max"
                  :step="column.step" disabled />
              </template>
              <template v-if="column.xundaKey === 'uploadImg'">
                <xunda-upload-img v-model:value="record[column.prop]" disabled detailed simple
                  v-if="record[column.prop]?.length" />
              </template>
              <template v-if="column.xundaKey === 'uploadFile'">
                <xunda-upload-file v-model:value="record[column.prop]" disabled detailed simple
                  v-if="record[column.prop]?.length" />
              </template>
              <template v-if="column.xundaKey === 'input'">
                <xunda-input v-model:value="record[column.prop]" :useMask="column.useMask"
                  :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="[
                {
                  label: t('common.editText', '编辑'),
                  onClick: updateHandle.bind(null, record),
                },
                {
                  label: t('common.delText', '删除'),
                  color: 'error',
                  modelConfirm: {
                    onOk: handleDelete.bind(null, record.id),
                  },
                },
                {
                  label: t('common.detailText', '详情'),
                  onClick: goDetail.bind(null, record),
                },

                {
                  label: t('common.recordText', '发放问卷'),
                  onClick: distributeHandle.bind(null, record),
                },
                {
                  label: t('common.recordText', '问卷记录'),
                  onClick: recordHandle.bind(null, record),
                  ifShow: false
                },
              ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" @reload="reload" />
    <PhysicianDetail ref="physicianDetailRef" />
    <VisitRecordCreateForm ref="visitRecordCreateFormRef" @reload="reload" />
    <PatientSelect @register="registerLiBoTableSelect" :getList="getPatientList" :title="t('common.selectText', '选择患者')"
      @submit="HandleDistribute" :getSelectedValue="getSelectedValue" />
  </div>
</template>

<script lang="ts" setup>
import { getList, batchDelete, getPatientList, distribute, getquestionnairePatientList } from '@/views/flow-up/questionnaire/api';
import { getConfigData } from '@/api/onlineDev/visualDev';
import { ref, reactive } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { useUserStore } from '@/store/modules/user';
import { BasicForm, useForm } from '@/components/Form';
import { BasicTable, useTable, TableAction, BasicColumn, ActionItem, TableActionType } from '@/components/Table';
import Form from '@/views/flow-up/questionnaire/Form.vue';
import Detail from '@/views/flow-up/questionnaire/Detail.vue';
import { useRoute } from 'vue-router';
import { cloneDeep } from 'lodash-es';
import { usePermission } from '@/hooks/web/usePermission';
import { columns, searchSchemas, patientColumns } from '@/views/flow-up/questionnaire/index';
import PhysicianDetail from '@/views/flow-up/physician/Detail.vue';
import VisitRecordCreateForm from '@/views/flow-up/visitRecord/Form.vue';
import { useModal } from '@/components/Modal';
import PatientSelect from '@/views/flow-up/questionnaire/PatientSelect.vue';


const route = useRoute();
const { hasBtnP } = usePermission();
const { createMessage, createConfirm } = useMessage();
const { t } = useI18n();
const userStore = useUserStore();
const [registerLiBoTableSelect, { openModal: openLiBoTableSelectModel }] = useModal();

const formRef = ref<any>(null);
const tableRef = ref<Nullable<TableActionType>>(null);
const detailRef = ref<any>(null);
const physicianDetailRef = ref<any>(null);
const relationDetailRef = ref<any>(null);
const cacheList = ref<any>([]);
const visitRecordCreateFormRef = ref<any>(null);

const defaultSearchInfo = {
  menuId: route.meta.modelId as string,
  moduleId: '661952363199727173',
  superQueryJson: '',
  dataType: 0,
};
const searchInfo = reactive({
  ...cloneDeep(defaultSearchInfo),
});

const [registerSearchForm] = useForm({
  baseColProps: { span: 6 },
  showActionButtonGroup: true,
  showAdvancedButton: true,
  compact: true,
});

const [registerTable, { reload, setLoading, getFetchParams, getSelectRows, getSelectRowKeys, redoHeight, clearSelectedRowKeys }] = useTable({
  api: getList,
  columns: columns,
  searchInfo: searchInfo,
  clickToRowSelect: false,
  afterFetch: data => {
    const list = data.map(o => ({
      ...o,
      // ...state.expandObj,
    }));
    cacheList.value = cloneDeep(list);
    return list;
  },
  actionColumn: {
    width: 200,
    title: t('common.actionText', '操作'),
    dataIndex: 'action',
    fixed: 'right',
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.top,
    }),
  },
});

// 关联表单查看详情
function toDetail(modelId, id) {
  if (!id) return;
  getConfigData(modelId).then(res => {
    if (!res.data || !res.data.formData) return;
    const formConf = JSON.parse(res.data.formData);
    formConf.popupType = 'general';
    const data = { id, formConf, modelId };
    relationDetailRef.value?.init(data);
  });
}

function handleSearchReset() {
  clearSelectedRowKeys();
}

function handleSearchSubmit(data) {
  clearSelectedRowKeys();
  let obj = {
    ...defaultSearchInfo,
    superQueryJson: searchInfo.superQueryJson,
    ...data,
  };
  Object.keys(searchInfo).map(key => {
    delete searchInfo[key];
  });
  for (let [key, value] of Object.entries(obj)) {
    searchInfo[key.replaceAll('-', '_')] = value;
  }
  console.log(searchInfo);
  reload({ page: 1 });
}

// 编辑
function updateHandle(record) {
  // 不带工作流
  const data = {
    id: record.id,
    menuId: searchInfo.menuId,
    allList: cacheList.value,
  };
  formRef.value?.init(data);
}
// 删除
function handleDelete(id) {
  const query = { ids: [id] };
  batchDelete(query).then(res => {
    createMessage.success(res.msg);
    clearSelectedRowKeys();
    reload();
  });
}
// 查看详情
function goDetail(record) {
  // 不带流程
  const data = {
    id: record.id,
  };
  detailRef.value?.init(data);
}
// 新增
function addHandle() {
  // 不带流程新增
  const data = {
    id: '',
    menuId: searchInfo.menuId,
    allList: cacheList.value,
  };
  formRef.value?.init(data);
}


// 批量删除
function handelBatchRemove() {
  const ids = getSelectRowKeys();
  if (!ids.length) return createMessage.error('请选择一条数据');
  createConfirm({
    iconType: 'warning',
    title: t('common.tipTitle'),
    content: '您确定要删除这些数据吗, 是否继续?',
    onOk: () => {
      const query = { ids: ids };
      batchDelete(query).then(res => {
        createMessage.success(res.msg);
        clearSelectedRowKeys();
        reload();
      });
    },
  });
}
function toPhysicianDetail(id) {
  if (!id) return;
  physicianDetailRef.value?.init({ id });
}
function recordHandle(record) {
  // 不带工作流
  const data = {
    id: '',
    questionnaireId: record.id,
    menuId: '661955255939892805',
    allList: [],
  };
  visitRecordCreateFormRef.value?.init(data);
}

const patientSearchInfo = {
  menuId: route.meta.modelId as string,
  moduleId: '661952363199727173',
  superQueryJson: '',
  dataType: 0,
};


function distributeHandle(record) {
  openLiBoTableSelectModel(true, record);
}

function HandleDistribute(id, ids, callBack) {
  const query = { questionnaireId: id, patientIds: ids };
  console.log(id, ids);
  distribute(query).then(res => {
    createMessage.success("发放问卷成功");
    if (callBack) callBack();
    clearSelectedRowKeys();
    reload();
  });
}

function getSelectedValue(record) {
  return getquestionnairePatientList(record.id);
}
</script>
