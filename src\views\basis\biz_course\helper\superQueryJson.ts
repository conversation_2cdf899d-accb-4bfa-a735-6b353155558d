const superQuery<PERSON>son = [
	{
		"clearable":true,
		"maxlength":null,
		"useScan":false,
		"suffixIcon":"",
		"fullName":"编号",
		"fullNameI18nCode":[
			""
		],
		"addonAfter":"",
		"showCount":false,
		"__config__":{
			"formId":101,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"编号",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"bit_example_course",
			"renderKey":1735183086794,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"__vModel__":"no",
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"no",
		"placeholder":"请输入",
		"prefixIcon":"",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	},
	{
		"clearable":true,
		"maxlength":null,
		"useScan":false,
		"suffixIcon":"",
		"fullName":"名称",
		"fullNameI18nCode":[
			""
		],
		"addonAfter":"",
		"showCount":false,
		"__config__":{
			"formId":102,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"名称",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"bit_example_course",
			"renderKey":1735183088075,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"__vModel__":"name",
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"name",
		"placeholder":"请输入",
		"prefixIcon":"",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	}
]
export default superQueryJson