const superQuery<PERSON>son = [
	{
		"clearable":true,
		"maxlength":null,
		"useScan":false,
		"suffixIcon":"",
		"fullName":"姓名",
		"fullNameI18nCode":[
			""
		],
		"addonAfter":"",
		"showCount":false,
		"__config__":{
			"formId":101,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"姓名",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727332852322,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"__vModel__":"S_Name",
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"S_Name",
		"placeholder":"请输入",
		"prefixIcon":"",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	},
	{
		"clearable":true,
		"maxlength":null,
		"useScan":false,
		"suffixIcon":"",
		"fullName":"学号",
		"fullNameI18nCode":[
			""
		],
		"addonAfter":"",
		"showCount":false,
		"__config__":{
			"formId":102,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"学号",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727332952710,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"__vModel__":"S_No",
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"S_No",
		"placeholder":"请输入",
		"prefixIcon":"",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	},
	{
		"controls":false,
		"fullName":"设计子表-成绩",
		"fullNameI18nCode":[
			"",
			""
		],
		"thousands":false,
		"isAmountChinese":false,
		"addonAfter":"",
		"__config__":{
			"formId":106,
			"relationTable":"demo_student_score",
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"parentVModel":"tableField103",
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"成绩",
			"trigger":[
				"blur",
				"change"
			],
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727332976168,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-number",
			"xundaKey":"inputNumber",
			"isSubTable":true,
			"tag":"XundaInputNumber",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"__vModel__":"SS_Score",
		"style":{
			"width":"100%"
		},
		"step":1,
		"disabled":false,
		"id":"tableField103-SS_Score",
		"placeholder":"请输入",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	},
	{
		"popupType":"dialog",
		"hasPage":false,
		"filterable":false,
		"clearable":true,
		"modelId":"608970807279885125",
		"fullName":"设计子表-课程",
		"pageSize":20,
		"columnOptions":[],
		"fullNameI18nCode":[
			"",
			""
		],
		"relationField":"C_Name",
		"__config__":{
			"relationTable":"demo_student_score",
			"defaultValue":"",
			"parentVModel":"tableField103",
			"dragDisabled":false,
			"className":[],
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727333003171,
			"transferList":[],
			"tagIcon":"icon-ym icon-ym-generator-menu",
			"xundaKey":"relationForm",
			"isSubTable":true,
			"tag":"XundaRelationForm",
			"formId":108,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"label":"课程",
			"trigger":"change",
			"layout":"colFormItem",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"popupTitle":"选择数据",
		"__vModel__":"SS_Course",
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"tableField103-SS_Course",
		"placeholder":"请选择",
		"popupWidth":"800px",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	}
]
export default superQueryJson
