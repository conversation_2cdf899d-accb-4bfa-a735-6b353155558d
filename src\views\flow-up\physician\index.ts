import { FormSchema } from '@/components/Form';
import { useI18n } from '@/hooks/web/useI18n';
import { BasicColumn } from '@/components/Table/src/types/table';
import { toDateString, toLocalDateString } from '@/utils/myUtil';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
const { t } = useI18n();

export const createFormSchenmas: FormSchema[] = [
    {
        field: 'picture',
        label: '照片',
        component: 'UploadImgSingle',
        componentProps: { type: 'annexpic', tipText: '上传照片' },
        rules: [{ required: true, trigger: 'change', message: '必填' }],
    },
    {
        field: 'name',
        label: '照片',
        component: 'Input',
        colProps: { sm: 24, span: 24 },
    },
];

/**
 * 搜索表单配置
 */
export const searchSchemas: FormSchema[] = [
    {
        field: 'name',
        label: t('姓名'),
        component: 'Input',
        componentProps: {
            submitOnPressEnter: true,
        },
    },
    {
        field: 'idCard',
        label: t('身份证号'),
        component: 'Input',
        componentProps: {
            submitOnPressEnter: true,
        },
    },
];

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
    {
        title: '照片',
        dataIndex: 'picture',
        width: 120,
        customRender({ record }) {
            record.picture = record.picture || '/api/file/Image/userAvatar/001.png';
        },
    },
    {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
    },
    {
        title: '性别',
        dataIndex: 'sex',
        width: 120,
    },
    {
        title: '籍贯',
        dataIndex: 'nativePlace',
        width: 120,
    },
    {
        title: '出生日期',
        dataIndex: 'birthdate',
        width: 120,
        // customRender({ record }) {
        //     return toDateString(record.birthdate, 'YYYY年MM月DD日');
        // },
    },
    {
        title: '年龄',
        dataIndex: 'age',
        width: 120,
    },
    {
        title: '手机号',
        dataIndex: 'phone',
        width: 120,
    },
    {
        title: '身份证号',
        dataIndex: 'idCard',
        width: 120,
    },
    {
        title: '民族',
        dataIndex: 'nation',
        width: 120,
    },
    {
        title: '单位任职',
        dataIndex: 'position',
        width: 120,
    },
];


// 获取所有字典数据
export function getPhysicianAllDictionaryData(record) {
    // 性别
    getDictionaryDataSelector('963255a34ea64a2584c5d1ba269c1fe6').then(res => {
        record.sexOptions = res.data.list;
    });

    // 民族
    getDictionaryDataSelector('b6cd65a763fa45eb9fe98e5057693e40').then(res => {
        record.nationOptions = res.data.list;
    });

    // 党派
    getDictionaryDataSelector('664828006606318341').then(res => {
        record.politicalGroupOptions = res.data.list;
    });

    // 单位任职
    getDictionaryDataSelector('664799411850717381').then(res => {
        record.positionOptions = res.data.list;
    });

    // 医师级别
    getDictionaryDataSelector('664802197581605061').then(res => {
        record.medicalLevelOptions = res.data.list;
    });

    // 职业状态
    getDictionaryDataSelector('664805371981867269').then(res => {
        record.vocationalStatusOptions = res.data.list;
    });

    // 从业类型
    getDictionaryDataSelector('664832037768670021').then(res => {
        record.workTypeOptions = res.data.list;
    });

    // 专业方向
    getDictionaryDataSelector('668788087303714885').then(res => {
        record.majorOptions = res.data.list;
    });

    // 学历
    getDictionaryDataSelector('668788854949427269').then(res => {
        record.educationLevelOptions = res.data.list;
    });

    // 学位
    getDictionaryDataSelector('668790135625627717').then(res => {
        record.degreeOptions = res.data.list;
    });
}


export const academicPositionColumns: BasicColumn[] = [
    {
        title: t('component.table.index'),
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        fixed: 'left',
        width: 50,
        customRender: ({ index }) => {
            let i = index + 1;
            return i;
        },
    },
    {
        title: '任职类别',
        editComponent: 'Input',
        dataIndex: 'jobCategory',
        key: 'jobCategory',
        align: 'left',
        fixed: false,
    },
    {
        title: '学会名称',
        editComponent: 'Input',
        dataIndex: 'societyName',
        key: 'societyName',
        align: 'left',
        fixed: false,
    },
    {
        title: '类别',
        editComponent: 'Input',
        dataIndex: 'category',
        key: 'category',
        align: 'left',
        fixed: false,
    },
    {
        title: '届数',
        editComponent: 'Input',
        dataIndex: 'session',
        key: 'session',

        align: 'left',
        fixed: false,
    },
    {
        title: '职务',
        editComponent: 'Input',
        dataIndex: 'duty',
        key: 'duty',
        align: 'left',
        fixed: false,
    },
    {
        title: '开始时间',
        editComponent: 'DatePicker',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'left',
        fixed: false,
        customRender: ({ value }) => {
            return toLocalDateString(value);
        }
    },
    {
        title: '结束时间',
        editComponent: 'DatePicker',
        dataIndex: 'endTime',
        key: 'endTime',
        align: 'left',
        fixed: false,
        customRender: ({ value }) => {
            return toLocalDateString(value);
        }
    },
];


export const workHistoryColumns: BasicColumn[] = [
    {
        title: t('component.table.index'),
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        fixed: 'left',
        width: 50,
        customRender: ({ index }) => {
            let i = index + 1;
            return i;
        },
    },
    {
        title: '所在单位',
        editComponent: 'Input',
        dataIndex: 'unit',
        key: 'unit',
        align: 'left',
        fixed: false,
    },
    {
        title: '开始时间',
        editComponent: 'DatePicker',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'left',
        fixed: false,
        customRender: ({ value }) => {
            return toLocalDateString(value);
        }
    },
    {
        title: '结束时间',
        editComponent: 'DatePicker',
        dataIndex: 'endTime',
        key: 'endTime',
        align: 'left',
        fixed: false,
        customRender: ({ value }) => {
            return toLocalDateString(value);
        }
    },
    {
        title: '任职情况',
        editComponent: 'Input',
        dataIndex: 'position',
        key: 'position',
        align: 'left',
        fixed: false,
    },
];


