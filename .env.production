# 资源公共路径,需要以 / 开头和结尾
VITE_PUBLIC_PATH = /

# 是否删除Console.log
VITE_DROP_CONSOLE = true

# 打包是否输出gz｜br文件
# 可选: gzip | brotli | none
# 也可以有多个, 例如 'gzip'|'brotli',这样会同时生成.gz和.br文件
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 是否在打包时使用cdn替换本地库，内网环境请设置为false
VITE_CDN = false

# 接口地址 可以由nginx做转发或者直接写实际地址
VITE_GLOB_API_URL=

# 报表接口地址 可以由nginx做转发或者直接写实际地址
VITE_GLOB_REPORT_API_URL=

# WebSocket基础地址 (为空时默认取当前url路径，若需要自定义，请输入)
VITE_GLOB_WEBSOCKET_URL=

# 接口地址前缀
VITE_GLOB_API_URL_PREFIX=

# 打包是否开启pwa功能
VITE_USE_PWA = false
