<template>
  <div class="pdf-preview-demo">
    <h3>使用BasicModal实现的PDF预览组件示例</h3>
    <div class="button-group">
      <a-button type="primary" @click="showPdfPreview">
        <template #icon><EyeOutlined /></template>
        预览PDF文件
      </a-button>

      <a-button type="primary" @click="showAnotherPdf" style="margin-left: 16px">
        <template #icon><FileOutlined /></template>
        预览另一个文件
      </a-button>

      <a-button @click="showWithTimeout" style="margin-left: 16px" type="default">
        <template #icon><ClockCircleOutlined /></template>
        自动关闭示例
      </a-button>
    </div>

    <!-- PDF预览组件 - 基于BasicModal实现 -->
    <PdfPreview ref="pdfPreviewRef" @close="handlePreviewClose" @download="handleDownload" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { EyeOutlined, FileOutlined, ClockCircleOutlined } from '@ant-design/icons-vue';
  import PdfPreview from './index.vue';
  import { useMessage } from '@/hooks/web/useMessage';

  // 示例文件信息
  const pdfFile1 = {
    url: '/api/file/sample.pdf', // 这里替换为实际的PDF文件URL
    name: '示例文档.pdf'
  };

  // 另一个文件示例
  const pdfFile2 = {
    url: '/api/file/another-sample.pdf',
    name: '第二个示例文档.pdf'
  };

  const pdfPreviewRef = ref();
  const { createMessage, createConfirm } = useMessage();

  // 预览第一个PDF文件
  const showPdfPreview = () => {
    if (pdfPreviewRef.value) {
      const { close } = pdfPreviewRef.value.init(pdfFile1);
      createMessage.success(`正在预览: ${pdfFile1.name}`);
    }
  };

  // 预览第二个PDF文件
  const showAnotherPdf = () => {
    if (pdfPreviewRef.value) {
      const { close } = pdfPreviewRef.value.init(pdfFile2);
      createMessage.success(`正在预览: ${pdfFile2.name}`);
    }
  };

  // 自动关闭示例
  const showWithTimeout = () => {
    createConfirm({
      iconType: 'info',
      title: '自动关闭演示',
      content: '将在5秒后自动关闭预览窗口',
      onOk: () => {
        if (pdfPreviewRef.value) {
          const { close } = pdfPreviewRef.value.init(pdfFile1);
          createMessage.info('预览窗口将在5秒后自动关闭');

          setTimeout(() => {
            close();
            createMessage.success('预览窗口已自动关闭');
          }, 5000);
        }
      }
    });
  };

  // 处理预览关闭事件
  const handlePreviewClose = () => {
    createMessage.info('预览已关闭');
  };

  // 处理下载事件
  const handleDownload = (file) => {
    createMessage.success(`文件 ${file.name} 开始下载`);
  };
</script>

<style lang="less" scoped>
  .pdf-preview-demo {
    padding: 24px;

    h3 {
      margin-bottom: 20px;
      font-weight: 500;
      color: #1a1a1a;
    }

    .button-group {
      margin: 16px 0 24px;
      display: flex;
    }
  }
</style>
