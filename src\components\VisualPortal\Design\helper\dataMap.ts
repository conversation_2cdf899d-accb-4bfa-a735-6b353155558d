export const chartData = {
  baseBarData: [
    {
      name: '苹果',
      type: '手机品牌',
      value: 1000879,
    },
    {
      name: '三星',
      type: '手机品牌',
      value: 3400879,
    },
    {
      name: '小米',
      type: '手机品牌',
      value: 2300879,
    },
    {
      name: 'oppo',
      type: '手机品牌',
      value: 5400879,
    },
    {
      name: 'vivo',
      type: '手机品牌',
      value: 3400879,
    },
  ],
  mulBarData: [
    {
      name: '1991',
      value: 3,
      type: 'Lon',
    },
    {
      name: '1992',
      value: 4,
      type: 'Lon',
    },
    {
      name: '1993',
      value: 3.5,
      type: 'Lon',
    },
    {
      name: '1994',
      value: 5,
      type: 'Lon',
    },
    {
      name: '1995',
      value: 4.9,
      type: 'Lon',
    },
    {
      name: '1996',
      value: 6,
      type: 'Lon',
    },
    {
      name: '1997',
      value: 7,
      type: 'Lon',
    },
    {
      name: '1998',
      value: 9,
      type: 'Lon',
    },
    {
      name: '1999',
      value: 13,
      type: 'Lon',
    },
    {
      name: '1991',
      value: 3,
      type: 'Bor',
    },
    {
      name: '1992',
      value: 4,
      type: 'Bor',
    },
    {
      name: '1993',
      value: 3.5,
      type: 'Bor',
    },
    {
      name: '1994',
      value: 5,
      type: 'Bor',
    },
    {
      name: '1995',
      value: 4.9,
      type: 'Bor',
    },
    {
      name: '1996',
      value: 6,
      type: 'Bor',
    },
    {
      name: '1997',
      value: 7,
      type: 'Bor',
    },
    {
      name: '1998',
      value: 9,
      type: 'Bor',
    },
    {
      name: '1999',
      value: 13,
      type: 'Bor',
    },
  ],
  plusMinusBarData: [
    {
      name: '周一',
      value: 200,
      type: '利润',
    },
    {
      name: '周二',
      value: 170,
      type: '利润',
    },
    {
      name: '周三',
      value: 240,
      type: '利润',
    },
    {
      name: '周四',
      value: 244,
      type: '利润',
    },
    {
      name: '周五',
      value: 200,
      type: '利润',
    },
    {
      name: '周六',
      value: 220,
      type: '利润',
    },
    {
      name: '周日',
      value: 210,
      type: '利润',
    },
    {
      name: '周一',
      value: 320,
      type: '收入',
    },
    {
      name: '周二',
      value: 302,
      type: '收入',
    },
    {
      name: '周三',
      value: 341,
      type: '收入',
    },
    {
      name: '周四',
      value: 374,
      type: '收入',
    },
    {
      name: '周五',
      value: 390,
      type: '收入',
    },
    {
      name: '周六',
      value: 450,
      type: '收入',
    },
    {
      name: '周日',
      value: 420,
      type: '收入',
    },
    {
      name: '周一',
      value: -120,
      type: '支出',
    },
    {
      name: '周二',
      value: -132,
      type: '支出',
    },
    {
      name: '周三',
      value: -101,
      type: '支出',
    },
    {
      name: '周四',
      value: -134,
      type: '支出',
    },
    {
      name: '周五',
      value: -190,
      type: '支出',
    },
    {
      name: '周六',
      value: -230,
      type: '支出',
    },
    {
      name: '周日',
      value: -210,
      type: '支出',
    },
  ],
  brokenColumnBarData: [
    {
      name: '1991',
      value: 110,
      type: '降水量',
    },
    {
      name: '1992',
      value: 130,
      type: '降水量',
    },
    {
      name: '1993',
      value: 113.5,
      type: '降水量',
    },
    {
      name: '1994',
      value: 150,
      type: '降水量',
    },
    {
      name: '1995',
      value: 240.9,
      type: '降水量',
    },
    {
      name: '1996',
      value: 160,
      type: '降水量',
    },
    {
      name: '1997',
      value: 97,
      type: '降水量',
    },
    {
      name: '1998',
      value: 290,
      type: '降水量',
    },
    {
      name: '1999',
      value: 230,
      type: '降水量',
    },
    {
      name: '1991',
      value: 33,
      type: '温度',
    },
    {
      name: '1992',
      value: 35,
      type: '温度',
    },
    {
      name: '1993',
      value: 37,
      type: '温度',
    },
    {
      name: '1994',
      value: 35,
      type: '温度',
    },
    {
      name: '1995',
      value: 34.9,
      type: '温度',
    },
    {
      name: '1996',
      value: 36,
      type: '温度',
    },
    {
      name: '1997',
      value: 37,
      type: '温度',
    },
    {
      name: '1998',
      value: 39,
      type: '温度',
    },
    {
      name: '1999',
      value: 33,
      type: '温度',
    },
  ],
  radarData: [
    {
      value: 75,
      name: '得分',
      type: 'NBA',
      max: 100,
    },
    {
      value: 65,
      name: '篮板',
      type: 'NBA',
      max: 100,
    },
    {
      value: 55,
      name: '防守',
      type: 'NBA',
      max: 100,
    },
    {
      value: 74,
      name: '失误',
      type: 'NBA',
      max: 100,
    },
    {
      value: 38,
      name: '盖帽',
      type: 'NBA',
      max: 100,
    },
    {
      value: 88,
      name: '三分',
      type: 'NBA',
      max: 100,
    },
    {
      value: 35,
      name: '得分',
      type: 'CBA',
      max: 100,
    },
    {
      value: 62,
      name: '篮板',
      type: 'CBA',
      max: 100,
    },
    {
      value: 44,
      name: '防守',
      type: 'CBA',
      max: 100,
    },
    {
      value: 66,
      name: '失误',
      type: 'CBA',
      max: 100,
    },
    {
      value: 28,
      name: '盖帽',
      type: 'CBA',
      max: 100,
    },
    {
      value: 58,
      name: '三分',
      type: 'CBA',
      max: 100,
    },
  ],
  customEchartsData: {
    color: ['#5470C6', '#91CC75', '#EE6666'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    grid: {
      right: '20%',
    },
    legend: {
      data: ['Evaporation', 'Precipitation', 'Temperature'],
    },
    xAxis: [
      {
        type: 'category',
        axisTick: {
          alignWithLabel: true,
        },
        // prettier-ignore
        data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: 'Evaporation',
        position: 'right',
        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5470C6',
          },
        },
        axisLabel: {
          formatter: '{value} ml',
        },
      },
      {
        type: 'value',
        name: 'Precipitation',
        position: 'right',
        alignTicks: true,
        offset: 80,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#91CC75',
          },
        },
        axisLabel: {
          formatter: '{value} ml',
        },
      },
      {
        type: 'value',
        name: '温度',
        position: 'left',
        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#EE6666',
          },
        },
        axisLabel: {
          formatter: '{value} °C',
        },
      },
    ],
    series: [
      {
        name: 'Evaporation',
        type: 'bar',
        data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3],
      },
      {
        name: 'Precipitation',
        type: 'bar',
        yAxisIndex: 1,
        data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3],
      },
      {
        name: 'Temperature',
        type: 'line',
        yAxisIndex: 2,
        data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2],
      },
    ],
  },
  customAppEchartsData: {
    type: 'column',
    opts: {
      color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
      padding: [15, 15, 0, 5],
      enableScroll: false,
      legend: {},
      xAxis: {
        disableGrid: true,
      },
      yAxis: {
        data: [
          {
            min: 0,
          },
        ],
      },
      extra: {
        column: {
          type: 'group',
          width: 30,
          activeBgColor: '#000000',
          activeBgOpacity: 0.08,
        },
      },
    },
    chartData: {
      categories: ['2018', '2019', '2020', '2021', '2022', '2023'],
      series: [
        {
          name: '目标值',
          data: [35, 36, 31, 33, 13, 34],
        },
        {
          name: '完成量',
          data: [18, 27, 21, 24, 6, 28],
        },
      ],
    },
  },
};

export const rankList = [
  {
    label: '北京-销售部',
    value: '¥ 895560880',
  },
  {
    label: '上海-销售部',
    value: '¥ 829546160',
  },
  {
    label: '深圳-销售部',
    value: '¥ 46564535',
  },
  {
    label: '厦门-销售部',
    value: '¥ 146564535',
  },
  {
    label: '广州-销售部',
    value: '¥ 125264330',
  },
];

export const timeAxisList = [
  {
    timestamp: '2012-04-12 08:46',
    content: '节点发起',
  },
  {
    timestamp: '2012-04-12 10:10',
    content: '节点加签',
  },
  {
    timestamp: '2012-04-12 11:46',
    content: '节点审批',
  },
  {
    timestamp: '2012-04-12 13:20',
    content: '结束',
  },
];
export const tableList = [
  {
    inputField101: '01',
    inputField102: 'xx门店',
    inputField103: '2598元',
    inputField104: '85%',
    inputField105: '2012-04-12 08:46',
  },
  {
    inputField101: '02',
    inputField102: 'xx门店',
    inputField103: '2598元',
    inputField104: '85%',
    inputField105: '2012-04-12 08:46',
  },
  {
    inputField101: '03',
    inputField102: 'xx门店',
    inputField103: '2598元',
    inputField104: '85%',
    inputField105: '2012-04-12 08:46',
  },
  {
    inputField101: '04',
    inputField102: 'xx门店',
    inputField103: '2598元',
    inputField104: '85%',
    inputField105: '2012-04-12 08:46',
  },
  {
    inputField101: '05',
    inputField102: 'xx门店',
    inputField103: '2598元',
    inputField104: '85%',
    inputField105: '2012-04-12 08:46',
  },
];

export const mapChartData = [
  { name: '北京市', value: 190, long: 116.46, lat: 39.92 },
  { name: '上海市', value: 180, long: 121.48, lat: 31.22 },
  { name: '成都市', value: 90, long: 104.06, lat: 30.65 },
  { name: '深圳市', value: 120, long: 114.07, lat: 22.62 },
  { name: '厦门市', value: 80, long: 118.11, lat: 24.49 },
];
//四个基础图表控件
export const chartList = ['barChart', 'lineChart', 'pieChart', 'radarChart'];
//系统控件
export const systemList = ['notice', 'email', 'todoList'];
//布局控件
export const layoutList = ['card', 'tab', 'collapse'];
//是否需要数据设置控件
export const needDataSetList = ['rankList', 'timeAxis', 'mapChart', ...chartList];
//是否需要跳转设置控件
export const needJumpSetList = ['tableList', 'rankList', 'timeAxis', 'mapChart', ...chartList];

//是否需要分格类型
export const noStyleTypeList = ['text', 'image', 'carousel', 'video', 'iframe', 'todo', 'commonFunc', 'dataBoard', 'tableList', 'customEcharts', ...layoutList];

//是否需要mask层控件
export const noNeedMaskList = [
  'text',
  'image',
  'carousel',
  'video',
  'iframe',
  'rankList',
  'timeAxis',
  'tableList',
  'mapChart',
  'customEcharts',
  ...layoutList,
  ...chartList,
];

export const needDefaultList = ['text', 'image', 'video', 'rankList', 'timeAxis', 'tableList', 'mapChart', ...chartList];

export const alignList = [
  { fullName: '左对齐', id: 'left' },
  { fullName: '居中对齐', id: 'center' },
  { fullName: '右对齐', id: 'right' },
];
export const underLineList = [
  { fullName: '无', id: 'none' },
  { fullName: '下划线', id: 'underline' },
  { fullName: '删除线', id: 'line-through' },
];
export const linkTypeList = [
  { fullName: '菜单', id: '1' },
  { fullName: '外链', id: '2' },
];
export const directionList = [
  { fullName: '横向', id: 'horizontal' },
  { fullName: '纵向', id: 'vertical' },
];
export const indicatorPositionList = [
  { label: '无', title: '无', id: 'none' },
  { label: '底部', title: '右侧', id: 'bottomRight' },
  { label: '顶部', title: '左侧', id: 'topLeft' },
];
export const arrowList = [
  { fullName: '无', id: 'never' },
  { fullName: '悬停', id: 'hover' },
  { fullName: '始终', id: 'always' },
];
export const fillStyleList = [
  { fullName: '适应', id: 'contain' },
  { fullName: '填充', id: 'cover' },
  { fullName: '拉伸', id: 'fill' },
];
export const playNumberList = [
  { fullName: '播放一次', id: 1 },
  { fullName: '循环播放', id: 2 },
];
export const barStyleList = [
  { fullName: '基础', id: 1 },
  { fullName: '堆叠', id: 2 },
  { fullName: '背景', id: 4 },
  { fullName: '对比', id: 5 },
  { fullName: '正负条', id: 6 },
  { fullName: '折柱图', id: 7 },
];
export const lineStyleList = [
  { fullName: '基础', id: 1 },
  { fullName: '平滑', id: 2 },
  { fullName: '阶梯', id: 3 },
  { fullName: '堆叠', id: 4 },
];
export const pieStyleList = [
  { fullName: '饼图', id: 1 },
  { fullName: '环形', id: 2 },
];
export const radarStyleList = [
  { fullName: '基础', id: 1 },
  { fullName: '圆形', id: 2 },
];
export const rankStyleList = [
  { fullName: '基础', id: 1 },
  { fullName: '勋章', id: 2 },
  { fullName: '奖杯', id: 3 },
  { fullName: '奖牌', id: 4 },
];
export const timeStyleList = [
  { fullName: '基础', id: 1 },
  { fullName: '卡片', id: 2 },
];
export const layoutStyleList = [
  { fullName: '纵向-左右交错', id: 1 },
  { fullName: '纵向-右左交错', id: 2 },
  { fullName: '纵向-轴左侧', id: 3 },
  { fullName: '纵向-轴右侧', id: 4 },
  { fullName: '横向-上下交错', id: 5 },
  { fullName: '横向-下上交错', id: 6 },
  { fullName: '横向-轴上侧', id: 7 },
  { fullName: '横向-轴下侧', id: 8 },
];
export const appLayoutStyleList = [
  { fullName: '纵向-轴左侧', id: 1 },
  { fullName: '纵向-轴右侧', id: 2 },
  { fullName: '横向-轴上侧', id: 3 },
  { fullName: '横向-轴下侧', id: 4 },
];
export const sortList = [
  { fullName: '升序', id: 1 },
  { fullName: '降序', id: 2 },
];
export const textStyleList = [
  { fullName: '文本', id: 1 },
  { fullName: 'HTML', id: 2 },
];
export const imageStyleList = [
  { fullName: '本地上传', id: 1 },
  { fullName: 'URL路径', id: 2 },
  { fullName: '远端数据', id: 3 },
];
export const videoStyleList = [
  { fullName: '本地上传', id: 1 },
  { fullName: 'URL路径', id: 2 },
  { fullName: '远端数据', id: 3 },
];
export const mapStyleList = [
  { fullName: '散点', id: 1 },
  { fullName: '热力', id: 2 },
  { fullName: '柱形', id: 3 },
  { fullName: '柱形排名', id: 4 },
];
export const appTodoStyleList = [
  { fullName: '横向滚动', id: 1 },
  { fullName: '全部展示', id: 2 },
];
export const commonFuncStyleList = [
  { fullName: '顶部', id: 1 },
  { fullName: '靠左', id: 2 },
];
export const dataTypeList = [
  { fullName: '静态数据', id: 'static' },
  { fullName: '远端数据', id: 'dynamic' },
];
export const typeList: any[] = [
  { fullName: '_self', id: '_self' },
  { fullName: '_blank', id: '_blank' },
];
