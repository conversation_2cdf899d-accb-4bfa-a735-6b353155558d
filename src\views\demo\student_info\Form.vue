<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    width="600px"
    :minHeight="100"
    :cancelText="t('common.cancelText', '取消')"
    :okText="t('common.okText', '确定')"
    @ok="handleSubmit"
    :closeFunc="onClose">
    <template #title>
      <a-space :size="10">
        <div class="text-16px font-medium">{{ title }}</div>
        <a-space-compact size="small" block v-if="dataForm.id">
          <a-tooltip :title="t('common.prevRecord')">
            <a-button size="small" :disabled="getPrevDisabled" @click="handlePrev">
              <i class="icon-ym icon-ym-caret-left text-10px"></i>
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('common.nextRecord')">
            <a-button size="small" :disabled="getNextDisabled" @click="handleNext">
              <i class="icon-ym icon-ym-caret-right text-10px"></i>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </template>
    <template #insertFooter>
      <div class="float-left mt-5px">
        <XundaCheckboxSingle v-model:value="submitType" :label="continueText" />
      </div>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="middle"
        layout="horizontal"
        labelAlign="right"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item" v-if="hasFormP('S_Name')">
            <a-form-item name="S_Name">
              <template #label>姓名 </template>
              <XundaInput
                v-model:value="dataForm.S_Name"
                @change="changeData('S_Name', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.S_Name"
                :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item" v-if="hasFormP('S_No')">
            <a-form-item name="S_No">
              <template #label>学号 </template>
              <XundaInput
                v-model:value="dataForm.S_No"
                @change="changeData('S_No', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.S_No"
                :showCount="false">
              </XundaInput>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item mb-20px" v-if="hasFormP('tableField103')">
            <a-form-item>
              <XundaGroupTitle content="设计子表" :bordered="false" helpMessage="" />
              <a-table
                :data-source="dataForm.demo_student_scoreList"
                :columns="demo_student_scoreColumns"
                size="small"
                :pagination="false"
                :scroll="{ x: 'max-content' }"
                :rowSelection="getdemo_student_scoreRowSelection"
                rowKey="xundaId">
                <template #headerCell="{ column }">
                  <span class="required-sign" v-if="column.required">*</span>
                  {{ column.title }}
                  <BasicHelp :text="column.tipLabel" v-if="column.tipLabel && column.title" />
                </template>
                <template #bodyCell="{ column, index, record }">
                  <template v-if="column.key === 'index'">{{ index + 1 }}</template>
                  <template v-if="column.key === 'SS_Score'">
                    <XundaInputNumber
                      v-model:value="record.SS_Score"
                      @change="changeData('demo_student_scoreSS_Score', index)"
                      placeholder="请输入"
                      :style="{ width: '100%' }"
                      :step="1"
                      :controls="false">
                    </XundaInputNumber>
                  </template>
                  <template v-if="column.key === 'SS_Course'">
                    <XundaRelationForm
                      v-model:value="record.SS_Course"
                      @change="changeData('demo_student_scoreSS_Course', index)"
                      placeholder="请选择"
                      :templateJson="state.interfaceRes.demo_student_scoreSS_Course"
                      :allowClear="true"
                      :style="{ width: '100%' }"
                      :showSearch="false"
                      :field="'SS_Course' + index"
                      modelId="608970807279885125"
                      :columnOptions="optionsObj.demo_student_scoreSS_CoursecolumnOptions"
                      relationField="C_Name"
                      popupWidth="800px">
                    </XundaRelationForm>
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-space>
                      <a-button class="action-btn" type="link" @click="copyDemo_student_scoreRow(index)" size="small">
                        {{ t('common.copyText', '复制') }}
                      </a-button>
                      <a-button class="action-btn" type="link" color="error" @click="removeDemo_student_scoreRow(index, true)" size="small">
                        {{ t('common.delText', '删除') }}
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
              <a-space class="input-table-footer-btn">
                <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addDemo_student_scoreRow"> {{ t('common.add1Text', '添加') }} </a-button>
                <a-button type="danger" preIcon="icon-ym icon-ym-btn-clearn" @click="batchRemoveDemo_student_scoreRow(true)">
                  {{ t('common.batchDelText', '批量删除') }}
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from './helper/api';
  import { reactive, toRefs, nextTick, ref, unref, computed, toRaw, inject } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { XundaRelationForm } from '@/components/Xunda';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { thousandsFormat, getDateTimeUnit, getTimeUnit } from '@/utils/xunda';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import { getDataInterfaceRes } from '@/api/systemData/dataInterface';
  import dayjs from 'dayjs';
  // 表单权限
  import { usePermission } from '@/hooks/web/usePermission';
  import { cloneDeep } from 'lodash-es';
  import { buildUUID } from '@/utils/uuid';
  import { CaretRightOutlined } from '@ant-design/icons-vue';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    //掩码配置
    maskConfig: any;
    //定位属性
    locationScope: any;
    selecteddemo_student_scoreRowKeys: any;

    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const getLeftTreeActiveInfo: (() => any) | null = inject('getLeftTreeActiveInfo', null);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const demo_student_scoreColumns: any[] = computed(() => {
    let list = [
      {
        title: '成绩',
        dataIndex: 'SS_Score',
        key: 'SS_Score',
        labelWidth: '',
        span: '24',
        tipLabel: '',
        required: false,
        style: { width: '100%' },
        align: 'left',
        fixed: false,
      },
      {
        title: '课程',
        dataIndex: 'SS_Course',
        key: 'SS_Course',
        labelWidth: '',
        span: '24',
        tipLabel: '',
        required: false,
        style: { width: '100%' },
        align: 'left',
        fixed: false,
      },
    ];
    list = list.filter(o => hasFormP('tableField103-' + o.dataIndex));
    list.push({ title: t('component.table.action'), showLabel: true, dataIndex: 'action', key: 'action', align: 'center', fixed: 'right', width: 100 });
    const indexColumn = { title: t('component.table.index'), showLabel: true, dataIndex: 'index', key: 'index', align: 'center', fixed: 'left', width: 50 };
    //子表复杂表头-处理
    let columnList = list;
    let complexHeaderList: any[] = [];
    if (complexHeaderList.length) {
      let childColumns: any[] = [];
      let firstChildColumns: string[] = [];
      for (let i = 0; i < complexHeaderList.length; i++) {
        const e = complexHeaderList[i];
        e.title = e.fullNameI18nCode ? t(e.fullNameI18nCode, e.fullName) : e.fullName;
        e.align = e.align;
        e.children = [];
        e.xundaKey = 'complexHeader';
        if (e.childColumns?.length) {
          childColumns.push(...e.childColumns);
          for (let k = 0; k < e.childColumns.length; k++) {
            const item = e.childColumns[k];
            for (let j = 0; j < list.length; j++) {
              const o = list[j];
              if (o.key == item && o.fixed !== 'left' && o.fixed !== 'right') e.children.push({ ...o });
            }
          }
        }
        if (e.children.length) firstChildColumns.push(e.children[0].key);
      }
      complexHeaderList = complexHeaderList.filter(o => o.children.length);
      let newList: any[] = [];
      for (let i = 0; i < list.length; i++) {
        const e = list[i];
        if (!childColumns.includes(e.key) || e.fixed === 'left' || e.fixed === 'right') {
          newList.push(e);
        } else {
          if (firstChildColumns.includes(e.key)) {
            const item = complexHeaderList.find(o => o.childColumns.includes(e.key));
            newList.push(item);
          }
        }
      }
      columnList = newList;
    }
    let columns = [indexColumn, ...columnList];
    const leftFixedList = columns.filter(o => o.fixed === 'left');
    const rightFixedList = columns.filter(o => o.fixed === 'right');
    const noFixedList = columns.filter(o => o.fixed !== 'left' && o.fixed !== 'right');
    return [...leftFixedList, ...noFixedList, ...rightFixedList];
  });

  const getdemo_student_scoreHasBatchBtn = computed(() => {
    let flist: any[] = [
      { btnIcon: 'icon-ym icon-ym-btn-add', show: true, label: '添加', btnType: 'primary', value: 'add', labelI18nCode: 'common.add1Text' },
      {
        btnIcon: 'icon-ym icon-ym-btn-clearn',
        showConfirm: 'true',
        show: true,
        label: '批量删除',
        btnType: 'danger',
        value: 'batchRemove',
        labelI18nCode: 'common.batchDelText',
      },
    ];
    return flist?.length && flist.some(o => o.value == 'batchRemove' && !!o.show);
  });

  const getdemo_student_scoreRowSelection = computed(() => {
    if (!unref(getdemo_student_scoreHasBatchBtn)) return undefined;
    const rowSelection = {
      selectedRowKeys: state.selecteddemo_student_scoreRowKeys,
      onChange: (selectedRowKeys: string[]) => {
        state.selecteddemo_student_scoreRowKeys = selectedRowKeys;
      },
    };
    return rowSelection;
  });

  const state = reactive<State>({
    dataForm: {
      S_Name: undefined,
      S_No: undefined,
      demo_student_scoreList: [],
    },

    tableRows: {
      demo_student_scoreList: {
        SS_Score: undefined,
        SS_Course: '',
        enabledmark: undefined,
      },
    },

    dataRule: {},

    optionsObj: {
      demo_student_scoreSS_CoursecolumnOptions: [],
    },

    childIndex: -1,
    isEdit: false,
    interfaceRes: { demo_student_scoreSS_Score: [], S_Name: [], S_No: [], demo_student_scoreSS_Course: [] },
    //可选范围默认值
    ableAll: {},

    //掩码配置
    maskConfig: {
      S_Name: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      S_No: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
    },

    //定位属性
    locationScope: {},

    selecteddemo_student_scoreRowKeys: [],

    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll, maskConfig, submitType } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.submitType = 0;
    state.isContinue = false;
    state.title = !data.id ? t('common.add2Text', '新增') : t('common.editText', '编辑');
    state.continueText = !data.id ? t('common.continueAndAddText', '确定并新增') : t('common.continueText', '确定并继续');
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    nextTick(() => {
      getForm().resetFields();
      state.dataForm.demo_student_scoreList = [];
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      //初始化options

      if (state.dataForm.demo_student_scoreList) {
        for (let i = 0; i < state.dataForm.demo_student_scoreList.length; i++) {
          state.childIndex = i;
          state.dataForm.demo_student_scoreList[i].xundaId = buildUUID();
        }
      }
      // 设置默认值
      state.dataForm = {
        S_Name: undefined,
        S_No: undefined,
        demo_student_scoreList: [],
      };
      if (getLeftTreeActiveInfo) state.dataForm = { ...state.dataForm, ...(getLeftTreeActiveInfo() || {}) };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};

      if (state.dataForm.demo_student_scoreList) {
        for (let i = 0; i < state.dataForm.demo_student_scoreList.length; i++) {
          state.childIndex = i;
          state.dataForm.demo_student_scoreList[i].xundaId = buildUUID();
        }
      }
      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      if (!demo_student_scoreExist()) return;

      setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ open: false });
            emit('reload');
          }
        })
        .catch(() => {
          setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setModalProps({ loading });
  }
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            let faceMode2 = modelAll.length == 2 ? modelAll[0].substring(0, modelAll[0].length - 4) + modelAll[1] : '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model || faceMode2 == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function changeDataFormData(type, data, model, index, defaultValue) {
    if (!state.isEdit) {
      if (type == 2) {
        for (let i = 0; i < state.dataForm[data].length; i++) {
          if (index == -1) {
            state.dataForm[data][i][model] = defaultValue;
          } else if (index == i) {
            state.dataForm[data][i][model] = defaultValue;
          }
        }
      } else {
        state.dataForm[data] = defaultValue;
      }
    }
  }
  function addDemo_student_scoreRow() {
    let item = {
      SS_Score: undefined,
      SS_Course: '',
      xundaId: buildUUID(),
    };
    state.dataForm.demo_student_scoreList.push(item);
    state.childIndex = state.dataForm.demo_student_scoreList.length - 1;
    state.childIndex = -1;
  }

  function removeDemo_student_scoreRow(index, showConfirm = false) {
    if (showConfirm) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '此操作将永久删除该数据, 是否继续?',
        onOk: () => {
          state.dataForm.demo_student_scoreList.splice(index, 1);
        },
      });
    } else {
      state.dataForm.demo_student_scoreList.splice(index, 1);
    }
  }

  function copyDemo_student_scoreRow(index) {
    let item = cloneDeep(state.dataForm.demo_student_scoreList[index]);
    let copyData = {};
    for (let i = 0; i < unref(demo_student_scoreColumns).length; i++) {
      const cur = unref(demo_student_scoreColumns)[i];
      if (cur.key != 'index' && cur.key != 'action') {
        if (cur.children?.length && cur.xundaKey == 'complexHeader') {
          for (let j = 0; j < cur.children.length; j++) {
            copyData[cur.children[j].key] = item[cur.children[j].key];
          }
        } else {
          copyData[cur.key] = item[cur.key];
        }
      }
    }
    const copyItem = { ...copyData, xundaId: buildUUID() };
    state.dataForm.demo_student_scoreList.push(copyItem);
    state.childIndex = state.dataForm.demo_student_scoreList.length - 1;
    state.childIndex = -1;
  }

  function batchRemoveDemo_student_scoreRow(showConfirm = false) {
    if (!state.selecteddemo_student_scoreRowKeys.length) return createMessage.error('请选择一条数据');
    const handleRemove = () => {
      state.dataForm.demo_student_scoreList = state.dataForm.demo_student_scoreList.filter(o => !state.selecteddemo_student_scoreRowKeys.includes(o.xundaId));
      nextTick(() => {
        state.selecteddemo_student_scoreRowKeys = [];
      });
    };
    if (showConfirm) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '此操作将永久删除该数据, 是否继续?',
        onOk: () => {
          handleRemove();
        },
      });
    } else {
      handleRemove();
    }
  }

  function demo_student_scoreExist() {
    let isOk = true;
    for (let i = 0; i < state.dataForm.demo_student_scoreList.length; i++) {
      const e = state.dataForm.demo_student_scoreList[i];
    }
    return isOk;
  }

  function getRelationDate(timeRule, timeType, timeTarget, timeValueData, dataValue) {
    let timeDataValue: any = null;
    let timeValue = Number(timeValueData);
    if (timeRule) {
      if (timeType == 1) {
        timeDataValue = timeValue;
      } else if (timeType == 2) {
        timeDataValue = dataValue;
      } else if (timeType == 3) {
        timeDataValue = new Date().getTime();
      } else if (timeType == 4 || timeType == 5) {
        const type = getTimeUnit(timeTarget);
        const method = timeType == 4 ? 'subtract' : 'add';
        timeDataValue = dayjs()[method](timeValue, type).valueOf();
      }
    }
    return timeDataValue;
  }
  function getRelationTime(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
    let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType;
    let timeDataValue: any = null;
    if (timeRule) {
      if (timeType == 1) {
        timeDataValue = timeValue || '00:00:00';
        if (timeDataValue.split(':').length == 3) {
          timeDataValue = timeDataValue;
        } else {
          timeDataValue = timeDataValue + ':00';
        }
      } else if (timeType == 2) {
        timeDataValue = dataValue;
      } else if (timeType == 3) {
        timeDataValue = dayjs().format(format);
      } else if (timeType == 4 || timeType == 5) {
        const type = getTimeUnit(timeTarget + 3);
        const method = timeType == 4 ? 'subtract' : 'add';
        timeDataValue = dayjs()[method](timeValue, type).format(format);
      }
    }
    return timeDataValue;
  }
</script>
