import Modeler from 'bpmn-js/lib/Modeler';
import xundaPaletteProvider from '../palette';
import xundaRenderer from '../renderer';
import xundaElementFactory from '../factory';
import xundaOutline from '../outline';
import xundaBusinessData from '../business';
import xundaGridSnappingAutoPlaceBehavior from '../gridSnapping';
import xundaAlignElementsContextPadProvider from '../alignElements';
import xundaContextPad from '../contextPad';
import xundaContextPadProvider from '../contextPad/provider';
import xundaCustomBpmnRules from '../rule';
import xundaCommandStack from '../commandStack';
import xundaCustomBpmnCopyPaste from '../copyPaste';
import GridSnappingLayoutConnectionBehavior from '../gridSnapping/connect';
let flowInfo: any;
const modeler: any = options => [
  {
    __init__: [
      'paletteProvider',
      'bpmnRenderer',
      'contextPadProvider',
      'replaceMenuProvider',
      'elementFactory',
      'xundaData',
      'gridSnappingAutoPlaceBehavior',
      'alignElementsContextPadProvider',
      'alignElementsMenuProvider',
      'bpmnAlignElements',
      'outlineProvider',
      'contextPad',
      'bpmnRules',
      'bpmnCopyPaste',
    ],
    paletteProvider: ['type', xundaPaletteProvider], // 左侧的元素 目前不用该方法
    bpmnRenderer: ['type', xundaRenderer, { options }], // 画布渲染
    elementFactory: ['type', xundaElementFactory], // 元素工厂
    xundaData: ['type', xundaBusinessData], // 用于放置业务数据
    gridSnappingAutoPlaceBehavior: ['type', xundaGridSnappingAutoPlaceBehavior], // 自动生成元素位置 在点击coontext-pad时计算元素生成位置
    alignElementsContextPadProvider: ['type', xundaAlignElementsContextPadProvider], // 元素的排序等
    outlineProvider: ['type', xundaOutline], // 元素的外边框(用于修改边框颜色，注：线条颜色有svg获取标签再去修改颜色及箭头）
    contextPad: ['type', xundaContextPad], // 点击元素后的元素右侧弹窗框（显示开始节点 结束节点等）
    contextPadProvider: ['type', xundaContextPadProvider], // context-pad 属性
    bpmnRules: ['type', xundaCustomBpmnRules], // 自定义规则
    commandStack: ['type', xundaCommandStack], // 自定义CommandStack
    gridSnappingLayoutConnectionBehavior: ['type', GridSnappingLayoutConnectionBehavior], // 修改连线的排序
    bpmnCopyPaste: ['type', xundaCustomBpmnCopyPaste], // 复制元素
  },
];

class bpmnModeler extends Modeler {
  constructor(options: any) {
    flowInfo = options.flowInfo;
    super(options);
  }
}

bpmnModeler.prototype['_modules'] = [].concat(bpmnModeler.prototype['_modules'], modeler(flowInfo));

export default bpmnModeler;
