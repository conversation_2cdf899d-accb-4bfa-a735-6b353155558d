{"name": "xunda-web-vue3", "version": "5.0.0", "description": "XUNDA快速开发平台", "author": {"name": "贵州比特软件有限公司", "email": "<EMAIL>", "url": "https://www.gzbtrj.com"}, "homepage": "https://www.gzbtrj.com", "scripts": {"commit": "czg", "bootstrap": "pnpm install --registry=https://registry.npmmirror.com", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "gen:icon": "esno ./build/generate/icon/index.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^7.1.0", "@ant-design/icons-vue": "^7.0.1", "@fullcalendar/core": "^6.1.14", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/timegrid": "^6.1.14", "@fullcalendar/vue3": "^6.1.14", "@iconify/iconify": "^3.1.0", "@logicflow/core": "^1.2.1", "@logicflow/extension": "^1.2.1", "@univerjs-pro/engine-pivot": "0.5.5", "@univerjs-pro/sheets-pivot": "0.5.5", "@univerjs-pro/sheets-pivot-ui": "0.5.5", "@univerjs/core": "0.5.5", "@univerjs/data-validation": "0.5.5", "@univerjs/design": "0.5.5", "@univerjs/docs": "0.5.5", "@univerjs/docs-drawing": "0.5.5", "@univerjs/docs-drawing-ui": "0.5.5", "@univerjs/docs-ui": "0.5.5", "@univerjs/drawing": "0.5.5", "@univerjs/drawing-ui": "0.5.5", "@univerjs/engine-formula": "0.5.5", "@univerjs/engine-render": "0.5.5", "@univerjs/facade": "0.5.5", "@univerjs/find-replace": "0.5.5", "@univerjs/icons": "0.2.14", "@univerjs/protocol": "0.1.43", "@univerjs/sheets": "0.5.5", "@univerjs/sheets-conditional-formatting": "0.5.5", "@univerjs/sheets-conditional-formatting-ui": "0.5.5", "@univerjs/sheets-crosshair-highlight": "0.5.5", "@univerjs/sheets-data-validation": "0.5.5", "@univerjs/sheets-data-validation-ui": "0.5.5", "@univerjs/sheets-drawing": "0.5.5", "@univerjs/sheets-drawing-ui": "0.5.5", "@univerjs/sheets-filter": "0.5.5", "@univerjs/sheets-filter-ui": "0.5.5", "@univerjs/sheets-find-replace": "0.5.5", "@univerjs/sheets-formula": "0.5.5", "@univerjs/sheets-formula-ui": "0.5.5", "@univerjs/sheets-hyper-link": "0.5.5", "@univerjs/sheets-hyper-link-ui": "0.5.5", "@univerjs/sheets-numfmt": "0.5.5", "@univerjs/sheets-numfmt-ui": "0.5.5", "@univerjs/sheets-sort": "0.5.5", "@univerjs/sheets-sort-ui": "0.5.5", "@univerjs/sheets-thread-comment": "0.5.5", "@univerjs/sheets-thread-comment-ui": "0.5.5", "@univerjs/sheets-ui": "0.5.5", "@univerjs/sheets-zen-editor": "0.5.5", "@univerjs/thread-comment": "0.5.5", "@univerjs/thread-comment-ui": "0.5.5", "@univerjs/ui": "0.5.5", "@univerjs/uniscript": "0.5.5", "@univerjs/watermark": "0.5.5", "@vue/runtime-core": "^3.4.27", "@vue/shared": "^3.4.27", "@vueuse/core": "^10.1.2", "@vueuse/shared": "^10.1.2", "@wendellhu/redi": "^0.15.2", "@zxcvbn-ts/core": "^2.2.1", "ant-design-vue": "^4.2.3", "axios": "^1.4.0", "bpmn-js": "16.3.2", "bpmn-js-properties-panel": "5.7.0", "camunda-bpmn-moddle": "6.1.2", "codemirror": "^5.65.12", "cron-parser": "^4.8.1", "cropperjs": "^1.5.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "diagram-js": "11.9.1", "diagram-js-minimap": "4.1.0", "echarts": "^5.4.2", "echarts-stat": "^1.2.0", "inherits-browser": "^0.1.0", "intro.js": "^7.0.1", "jsbarcode": "^3.11.5", "lodash-es": "^4.17.21", "min-dash": "^4.2.1", "min-dom": "^4.1.0", "monaco-editor": "^0.38.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "^2.1.3", "print-js": "^1.6.0", "qrcode": "^1.5.1", "qs": "^6.11.1", "reconnecting-websocket": "^4.4.0", "resize-observer-polyfill": "^1.5.1", "rxjs": "^7.8.1", "showdown": "^2.1.0", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "terser": "^5.14.2", "tiny-svg": "^3.0.1", "tinymce": "^5.10.7", "v-code-diff": "^1.13.1", "vditor": "^3.9.1", "vue": "^3.4.27", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.13.1", "vue-json-pretty": "^2.2.4", "vue-plugin-hiprint": "0.0.57-beta24", "vue-router": "^4.3.2", "vue-simple-uploader": "1.0.0", "vue-types": "^5.1.2", "vue3-draggable-resizable": "^1.6.5", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@iconify/json": "^2.2.43", "@purge-icons/generated": "^0.9.0", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/codemirror": "^5.60.7", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.1", "@types/inquirer": "^8.2.6", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@types/showdown": "^2.0.0", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "@univerjs/vite-plugin": "^0.5.1", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "^3.4.27", "@vue/test-utils": "^2.4.5", "autoprefixer": "^10.4.14", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "cz-git": "^1.6.1", "czg": "^1.6.1", "dotenv": "^16.0.3", "eslint": "^8.37.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.10.0", "esno": "^0.16.3", "fs-extra": "^11.1.1", "inquirer": "^9.1.5", "jquery": "^3.7.1", "less": "^4.1.3", "lint-staged": "13.2.0", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.8", "rimraf": "^4.4.1", "rollup": "^3.7.4", "rollup-plugin-visualizer": "^5.9.0", "stylelint": "^15.4.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^11.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^32.0.0", "stylelint-order": "^6.0.3", "ts-node": "^10.9.1", "typescript": "^5.4.5", "vite": "^4.5.3", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mkcert": "^1.10.1", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-pwa": "^0.14.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-windicss": "^1.8.10", "vue-eslint-parser": "^9.1.1", "vue-tsc": "^2.0.19"}, "engines": {"node": ">=16.15.0", "pnpm": ">=8.1.0"}, "pnpm": {"ignoredBuiltDependencies": ["core-js", "esbuild", "protobufjs", "v-code-diff", "vue-demi"], "onlyBuiltDependencies": ["core-js", "esbuild", "protobufjs", "v-code-diff", "vue-demi"]}}