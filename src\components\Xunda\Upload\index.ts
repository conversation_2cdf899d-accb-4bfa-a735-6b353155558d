import { withInstall } from '@/utils';
import UploadBtn from './src/UploadBtn.vue';
import UploadFile from './src/UploadFile.vue';
import UploadImg from './src/UploadImg.vue';
import UploadImgSingle from './src/UploadImgSingle.vue';

export const XundaUploadBtn = withInstall(UploadBtn);
export const XundaUploadFile = withInstall(UploadFile);
export const XundaUploadImg = withInstall(UploadImg);
export const XundaUploadImgSingle = withInstall(UploadImgSingle);
