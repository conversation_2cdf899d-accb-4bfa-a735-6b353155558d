import { defHttp } from '@/utils/http/axios';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: '/api/example/Demo_allcomponents/getList', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url:'/api/example/Demo_allcomponents', data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: '/api/example/Demo_allcomponents/'+ data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: '/api/example/Demo_allcomponents/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: '/api/example/Demo_allcomponents/detail/' + id });
}
// 删除
export function del(id) {
  return defHttp.delete({ url: '/api/example/Demo_allcomponents/' + id });
}
// 批量删除数据
export function batchDelete(data) {
  return defHttp.delete({ url: '/api/example/Demo_allcomponents/batchRemove', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: '/api/example/Demo_allcomponents/Actions/Export', data });
}
