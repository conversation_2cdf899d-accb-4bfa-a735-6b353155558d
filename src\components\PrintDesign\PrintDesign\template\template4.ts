export default [
  {
    options: {
      left: 232.5,
      top: 31.5,
      height: 28.5,
      width: 120,
      title: '工作证明',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 21,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 355.5,
      bottom: 48.75,
      vCenter: 295.5,
      hCenter: 34.5,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 411,
      top: 108,
      height: 24,
      width: 42,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 453.24609375,
      bottom: 130.74609375,
      vCenter: 432.24609375,
      hCenter: 118.74609375,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 312,
      top: 108,
      height: 24,
      width: 42,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 446.99219512939453,
      bottom: 132.74219512939453,
      vCenter: 391.49219512939453,
      hCenter: 120.74219512939453,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 177,
      top: 108,
      height: 24,
      width: 85.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 288,
      bottom: 131.25,
      vCenter: 232.5,
      hCenter: 119.25,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 451.5,
      top: 112.5,
      height: 16.5,
      width: 93,
      title: '，有效身份证',
      right: 503.49609375,
      bottom: 129.24609375,
      vCenter: 477.24609375,
      hCenter: 120.99609375,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 264,
      top: 114,
      height: 16.5,
      width: 46.5,
      title: '，性别',
      right: 308.25,
      bottom: 130.5,
      vCenter: 290.25,
      hCenter: 122.25,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
      zIndex: 55,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 358.5,
      top: 114,
      height: 16.5,
      width: 52.5,
      title: '，年龄',
      right: 373.5,
      bottom: 127.5,
      vCenter: 366,
      hCenter: 119.25,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 70.5,
      top: 115.5,
      height: 16.5,
      width: 106.5,
      title: '兹有我单位员工',
      right: 99,
      bottom: 93,
      vCenter: 73.5,
      hCenter: 84.75,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 79.5,
      top: 142.5,
      height: 24,
      width: 192,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 208.5,
      bottom: 165,
      vCenter: 144,
      hCenter: 153,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 40.5,
      top: 148.5,
      height: 16.5,
      width: 40.5,
      title: '号码',
      right: 86.25,
      bottom: 141.75,
      vCenter: 60.75,
      hCenter: 133.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 439.5,
      top: 172.5,
      height: 24,
      width: 106.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 526.74609375,
      bottom: 193.74609375,
      vCenter: 468.24609375,
      hCenter: 181.74609375,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 286.5,
      top: 172.5,
      height: 24,
      width: 117,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 450,
      bottom: 154.5,
      vCenter: 391.5,
      hCenter: 142.5,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 90,
      top: 174,
      height: 24,
      width: 100.5,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 205.5,
      bottom: 189,
      vCenter: 157.5,
      hCenter: 177,
      hideTitle: true,
      fontSize: 15,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 405,
      top: 180,
      height: 16.5,
      width: 33,
      title: '部门',
      right: 436.5,
      bottom: 195.75,
      vCenter: 420,
      hCenter: 187.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 192,
      top: 180,
      height: 16.5,
      width: 94.5,
      title: '起，在本单位',
      right: 402.75,
      bottom: 195,
      vCenter: 296.25,
      hCenter: 186.75,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 70.5,
      top: 180,
      height: 16.5,
      width: 19.5,
      title: '自',
      right: 89.25,
      bottom: 194.25,
      vCenter: 79.5,
      hCenter: 186,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 42,
      top: 219,
      height: 16.5,
      width: 114,
      title: '岗位任职至今。',
      right: 107.25,
      bottom: 234.75,
      vCenter: 74.25,
      hCenter: 226.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 70.5,
      top: 255,
      height: 16.5,
      width: 94.5,
      title: '特此证明！',
      right: 168,
      bottom: 369.75,
      vCenter: 120.75,
      hCenter: 361.5,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 42,
      top: 282,
      height: 61.5,
      width: 504,
      title: '声明:此证明仅用于证明该员工是我公司员工，不作为我公司对该员工任何形势的担保文件。\n\n\n',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textAlign: 'left',
      lineHeight: 32.25,
      right: 545.25,
      bottom: 348.75,
      vCenter: 293.25,
      hCenter: 318,
      longTextIndent: 27,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 408,
      top: 429,
      height: 16.5,
      width: 135,
      title: '公司名称（盖章）',
      right: 538.4978713989258,
      bottom: 414.7499713897705,
      vCenter: 470.9978713989258,
      hCenter: 406.4999713897705,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 408,
      top: 465,
      height: 16.5,
      width: 135,
      title: '年           月           日',
      right: 540.9907608032227,
      bottom: 449.49290657043457,
      vCenter: 473.49076080322266,
      hCenter: 441.24290657043457,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
      textAlign: 'right',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 271.5,
      top: 148.5,
      height: 16.5,
      width: 40.5,
      title: '。',
      right: 312,
      bottom: 165,
      vCenter: 291.75,
      hCenter: 156.75,
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 15,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
