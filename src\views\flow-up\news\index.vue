<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addHandle()"> {{ t('common.add2Text', '新增') }}</a-button>

            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'coverImage'">
              <xunda-upload-img-single v-model:value="record['coverImage']" disabled detailed simple />
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="record.status ? 'green' : 'red'">
                {{ record.status ? '已发布' : '草稿' }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'isTop'">
              <a-tag :color="record.isTop ? 'orange' : 'default'">
                {{ record.isTop ? '置顶' : '普通' }}
              </a-tag>
            </template>
            <template v-if="column.dataIndex === 'publishTime'">
              <span>{{ xundaUtils.toDateText(record.publishTime) }}</span>
            </template>
            <template v-if="column.dataIndex === 'createTime'">
              <span>{{ xundaUtils.toDateText(record.createTime) }}</span>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: updateHandle.bind(null, record),
                  },
                  {
                    label: record.status ? '取消发布' : '发布',
                    color: record.status ? 'warning' : 'success',
                    onClick: togglePublish.bind(null, record),
                  },
                  {
                    label: record.isTop ? '取消置顶' : '置顶',
                    onClick: toggleTop.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                  {
                    label: t('common.detailText', '详情'),
                    onClick: goDetail.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" />
  </div>
</template>

<script lang="ts" setup>
  import { getList, batchDelete, updateStatus, updateTop } from '@/views/flow-up/news/api';
  import { ref, reactive, onMounted } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem, TableActionType } from '@/components/Table';
  import Form from '@/views/flow-up/news/Form.vue';
  import Detail from '@/views/flow-up/news/Detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { usePermission } from '@/hooks/web/usePermission';
  import { columns, searchSchemas } from './index';
  import { xundaUtils } from '@/utils/xunda';

  const route = useRoute();
  const { hasBtnP } = usePermission();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const userStore = useUserStore();

  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const cacheList = ref<any>([]);

  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '661876504619124230', // 新闻模块ID
    superQueryJson: '',
    dataType: 0,
  };
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });

  const getTableList = params => {
    return getList(params);
  };

  const [registerTable, { reload, setLoading, getFetchParams, getSelectRows, getSelectRowKeys, redoHeight, clearSelectedRowKeys }] = useTable({
    api: getTableList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 180,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.isTop,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    reload({ page: 1 });
  }

  // 编辑
  function updateHandle(record) {
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 查看详情
  function goDetail(record) {
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }

  // 新增
  function addHandle() {
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 切换发布状态
  function togglePublish(record) {
    const newStatus = !record.status;
    const statusText = newStatus ? '发布' : '取消发布';

    createConfirm({
      iconType: 'warning',
      title: '确认操作',
      content: `确定要${statusText}这篇新闻吗？`,
      onOk: () => {
        updateStatus({ id: record.id, status: newStatus }).then(res => {
          createMessage.success(res.msg);
          reload();
        });
      },
    });
  }

  // 切换置顶状态
  function toggleTop(record) {
    const newTopStatus = !record.isTop;
    const topText = newTopStatus ? '置顶' : '取消置顶';

    createConfirm({
      iconType: 'warning',
      title: '确认操作',
      content: `确定要${topText}这篇新闻吗？`,
      onOk: () => {
        updateTop({ id: record.id, isTop: newTopStatus }).then(res => {
          createMessage.success(res.msg);
          reload();
        });
      },
    });
  }

  onMounted(() => {
    // 初始化数据
  });
</script>
