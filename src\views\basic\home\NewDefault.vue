<template>
  <div class="new-default">
    <div class="new-default__title">
      <h1>医师随访管理平台</h1>
    </div>
    <div class="new-default__content">
      <p>欢迎来到医师随访管理平台。</p>
      <p>在这里，您可以轻松管理您的随访计划，跟踪患者的健康状况，并提供个性化的建议。</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NewDefault',
};
</script>

<style scoped>
.new-default {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  color: #fff;
  transition: box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.new-default:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

.new-default__title h1 {
  font-size: 3rem;
  margin-bottom: 2rem;
  font-weight: 600;
}

.new-default__content p {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.7;
  max-width: 600px;
}

@media (max-width: 768px) {
  .new-default__title h1 {
    font-size: 2.5rem;
  }

  .new-default__content p {
    font-size: 1.25rem;
  }
}
</style>
