<template>
  <a-table :data-source="dataSource" :columns="getColumns" size="small" :pagination="false"
    :scroll="{ x: 'max-content' }" :rowSelection="getRowSelection" rowKey="id">
    <template #title>{{ title }}</template>
    <template #headerCell="{ column }">
      <span class="required-sign" v-if="column.required">*</span>
      {{ column.title }}
      <BasicHelp :text="column.tipLabel" v-if="column.tipLabel && column.title" />
    </template>
    <template #bodyCell="{ column, index, record }">
      <template v-if="column.key === 'name'">
        <XundaSelect v-if="isEdit" v-model:value="record.name" placeholder="请选择" :allowClear="true"
          :style="{ width: '100%' }" :showSearch="false" :options="technicalTitleOptions"
          :fieldNames="technicalTitleProps">
        </XundaSelect>
        <template v-else>
          {{ getDictionaryFullName(record.name, technicalTitleOptions) }}
        </template>
      </template>
      <template v-else-if="column.key === 'index'">
        {{ '第' + numberToChinese(index + 1) + '职称' }}
      </template>
      <template v-else-if="column.key === 'action' && isEdit">
        <a-space>
          <a-button class="action-btn" type="link" @click="copyRow(index)" size="small">
            {{ t('common.copyText', '复制') }}
          </a-button>
          <a-button class="action-btn" type="link" color="error" @click="removeRow(index, true)" size="small">
            {{ t('common.delText', '删除') }}
          </a-button>
        </a-space>
      </template>
      <template v-else>
        <XundaInput v-if="isEdit" v-model:value="record[column.key]" placeholder="请输入" :allowClear="true"
          :style="{ width: '100%' }" :showCount="false">
        </XundaInput>
        <template v-else>
          {{ record[column.key] }}
        </template>
      </template>
    </template>
  </a-table>
  <a-space class="input-table-footer-btn" v-if="isEdit">
    <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addRow">
      {{ t('common.add1Text', '添加') }}
    </a-button>
    <a-button v-if="isEdit" type="danger" preIcon="icon-ym icon-ym-btn-clearn" @click="batchRemoveRow(true)">
      {{ t('common.batchDelText', '批量删除') }}
    </a-button>
  </a-space>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref, unref } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { cloneDeep } from 'lodash-es';
import { buildUUID } from '@/utils/uuid';
import { useI18n } from '@/hooks/web/useI18n';
import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
import { numberToChinese, getDictionaryFullName } from '@/utils/myUtil';

interface State {
  selectedRowKeys: any[];
  childIndex: any;
}

defineOptions({ name: 'TechnicalTitleItem', inheritAttrs: false });

const { createMessage, createConfirm } = useMessage();

const props = defineProps({
  dataSource: {
    type: Array<any>,
    default: () => {
      return []
    }
  },
  title: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['update:value']);

const baseColumns = unref<any>([
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    fixed: 'left',
    customRender: ({ index }) => {
      return index + 1;
    },
  },
  {
    title: '职称名称',
    key: 'name',
    dataIndex: 'name',
    editComponent: "Select",
    width: 120,
  },
])

const technicalTitleOptions = ref<any>([]);
const technicalTitleProps = ref<any>({ "label": "fullName", "value": "enCode", "multiple": false, "children": "" });

const state = reactive<State>({
  childIndex: -1,
  selectedRowKeys: [],
});
const { t } = useI18n();

const getRowSelection = computed(() => {
  if (!props.isEdit) return undefined;
  const rowSelection = {
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys: string[]) => {
      state.selectedRowKeys = selectedRowKeys;
    },
  };
  return rowSelection;
});

const getColumns = computed(() => {
  let columns = baseColumns || [];
  if (props.isEdit) {
    columns.push({
      title: t('common.actionText', '操作'),
      key: 'action',
      width: 120,
      align: 'center',
      fixed: 'right',
    });
  }
  return columns;
});

const addRow = () => {
  let item = {
    id: buildUUID(),
  };
  let dataSource = props.dataSource || [];
  dataSource.push(item);
  emit('update:value', dataSource);
  console.log('dataSource', dataSource);
  state.childIndex = dataSource.length - 1;
  state.childIndex = -1;
};

function copyRow(index) {
  let dataSource = props.dataSource || [];
  let item = cloneDeep(dataSource[index]);
  let copyData = {};
  for (let i = 0; i < unref(getColumns).length; i++) {
    const cur = unref(getColumns)[i];
    if (cur.key != 'index' && cur.key != 'action') {
      copyData[cur.dataIndex] = item[cur.dataIndex];
    }
  }
  const copyItem = { ...copyData, id: buildUUID() };
  dataSource.push(copyItem);
  emit('update:value', dataSource);
  state.childIndex = dataSource.length - 1;
  state.childIndex = -1;
}

const removeRow = (index, showConfirm = false) => {
  let dataSource = props.dataSource || [];
  if (showConfirm) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除该数据, 是否继续?',
      onOk: () => {
        dataSource.splice(index, 1);
        emit('update:value', dataSource);
      },
    });
  } else {
    dataSource.splice(index, 1);
    emit('update:value', dataSource);
  }
};

const batchRemoveRow = (showConfirm = false) => {
  if (!state.selectedRowKeys.length) return createMessage.error('请选择一条数据');
  const handleRemove = () => {
    let dataSource = props.dataSource || [];
    dataSource = dataSource.filter(o => !state.selectedRowKeys.includes(o.id));
    emit('update:value', dataSource);
    nextTick(() => {
      state.selectedRowKeys = [];
    });
  };
  if (showConfirm) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '此操作将永久删除该数据, 是否继续?',
      onOk: () => {
        handleRemove();
      },
    });
  } else {
    handleRemove();
  }
};

onMounted(() => {
  getDictionaryDataSelector('668748102693304389').then(res => {
    technicalTitleOptions.value = res.data.list;
  })
});
</script>

<style lang="scss" scoped></style>
