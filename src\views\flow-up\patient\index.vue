<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addHandle()"> {{ t('common.add2Text', '新增') }}</a-button>
            <a-button
              type="link"
              preIcon="icon-ym icon-ym-btn-download"
              @click="openExportModal(true, { columnList: exportColumnList, selectIds: getSelectRowKeys() })">
              {{ t('common.exportText', '导出') }}</a-button
            >
            <a-button type="link" preIcon="icon-ym icon-ym-btn-upload" @click="openImportModal(true, { url: 'flowup/patient', menuId: searchInfo.menuId })">
              {{ t('common.importText', '导入') }}</a-button
            >
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="!(record.top || column.id?.includes('-'))">
              <template v-if="column.dataIndex === 'pipeBedPhysician'">
                <p class="link-text" @click="toPhysicianDetail(record.pipeBedPhysician)"> {{ record.physicianName }}</p>
              </template>
              <template v-if="column.xundaKey === 'relationForm'">
                <p class="link-text" @click="toDetail(column.modelId, record[column.dataIndex + `_id`])"> {{ record[column.dataIndex] }}</p>
              </template>
              <template v-if="column.dataIndex === 'sex'">
                <p> {{ getDictionaryFullName(record.sex, allDictionaries.sexOptions) }}</p>
              </template>
              <template v-if="column.dataIndex === 'type'">
                <p> {{ getDictionaryFullName(record.type, allDictionaries.typeOptions) }}</p>
              </template>
              <template v-if="column.xundaKey === 'inputNumber'">
                <xunda-input-number v-model:value="record[column.prop]" :precision="column.precision" :thousands="column.thousands" disabled detailed />
              </template>
              <template v-if="column.xundaKey === 'calculate'">
                <xunda-calculate
                  v-model:value="record[column.prop]"
                  :isStorage="column.isStorage"
                  :precision="column.precision"
                  :thousands="column.thousands"
                  detailed />
              </template>
              <template v-if="column.xundaKey === 'sign'">
                <xunda-sign v-model:value="record[column.prop]" detailed />
              </template>
              <template v-if="column.xundaKey === 'signature'">
                <xunda-signature v-model:value="record[column.prop]" detailed />
              </template>
              <template v-if="column.xundaKey === 'rate'">
                <xunda-rate v-model:value="record[column.prop]" :count="column.count" :allowHalf="column.allowHalf" disabled />
              </template>
              <template v-if="column.xundaKey === 'slider'">
                <xunda-slider v-model:value="record[column.prop]" :min="column.min" :max="column.max" :step="column.step" disabled />
              </template>
              <template v-if="column.xundaKey === 'uploadImg'">
                <xunda-upload-img v-model:value="record[column.prop]" disabled detailed simple v-if="record[column.prop]?.length" />
              </template>
              <template v-if="column.xundaKey === 'uploadFile'">
                <xunda-upload-file v-model:value="record[column.prop]" disabled detailed simple v-if="record[column.prop]?.length" />
              </template>
              <template v-if="column.xundaKey === 'input'">
                <xunda-input v-model:value="record[column.prop]" :useMask="column.useMask" :maskConfig="column.maskConfig" :showOverflow="true" detailed />
              </template>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: updateHandle.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                  {
                    label: t('common.detailText', '详情'),
                    onClick: goDetail.bind(null, record),
                  },
                  {
                    label: t('common.flowupText', '随访'),
                    onClick: flowupHandle.bind(null, record),
                  },
                ]"
                :dropDownActions="[
                  {
                    label: t('common.flowupText', '生成账号'),
                    onClick: accountHandle.bind(null, record),
                    ifShow: false,
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleDownload" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <PhysicianDetail ref="physicianDetailRef" />
    <VisitRecordCreateForm ref="visitRecordCreateFormRef" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { getList, batchDelete, exportData, createAccount, createAllAccount } from '@/views/flow-up/patient/api';
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { ref, reactive, toRefs, onMounted, computed } from 'vue';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, TableActionType } from '@/components/Table';
  import Form from '@/views/flow-up/patient/Form.vue';
  import Detail from '@/views/flow-up/patient/Detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { columns, getPatientAllDictionaries, searchSchemas } from '@/views/flow-up/patient/index';
  import PhysicianDetail from '@/views/flow-up/physician/Detail.vue';
  import VisitRecordCreateForm from '@/views/flow-up/visitRecord/Form.vue';
  import { getDictionaryFullName } from '@/utils/myUtil';
  import { ExportModal } from '@/components/CommonModal';
  import { downloadByUrl } from '@/utils/file/download';
  import { ImportModal } from '@/components/CommonModal';

  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const physicianDetailRef = ref<any>(null);
  const relationDetailRef = ref<any>(null);
  const cacheList = ref<any>([]);
  const visitRecordCreateFormRef = ref<any>(null);

  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '661952363199727173',
    superQueryJson: '',
    dataType: 0,
  };
  const exportColumnList = computed(() => {
    return columns.map(o => {
      return {
        id: o.dataIndex,
        key: o.dataIndex,
        value: o.dataIndex,
        fullName: o.title,
        __config__: {
          xundakey: 'text',
        },
      };
    });
  });

  const allDictionaries = ref<any>({ sexOptions: {}, typeOptions: {} } as any);

  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });

  const getTableList = params => {
    createAllAccount();
    return getList(params);
  };

  const [registerTable, { reload, setLoading, getFetchParams, redoHeight, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: getTableList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
        // ...state.expandObj,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 200,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  // 关联表单查看详情
  function toDetail(modelId, id) {
    if (!id) return;
    getConfigData(modelId).then(res => {
      if (!res.data || !res.data.formData) return;
      const formConf = JSON.parse(res.data.formData);
      formConf.popupType = 'general';
      const data = { id, formConf, modelId };
      relationDetailRef.value?.init(data);
    });
  }

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchInfo);
    reload({ page: 1 });
  }

  // 编辑
  function updateHandle(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }
  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }
  // 查看详情
  function goDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }
  // 新增
  function addHandle() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }
  // 导出
  function handleDownload(data) {
    let query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        setLoading(true);
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }
  function toPhysicianDetail(id) {
    if (!id) return;
    physicianDetailRef.value?.init({ id });
  }
  function flowupHandle(record) {
    // 不带工作流
    const data = {
      id: '',
      patientId: record.id,
      menuId: '661955255939892805',
      allList: [],
    };
    visitRecordCreateFormRef.value?.init(data);
  }

  function accountHandle(record) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要创建账户吗, 是否继续?',
      onOk: () => {
        setLoading(true);
        createAccount({ patientId: record.id })
          .then(res => {
            createMessage.success(res.msg);
            reload();
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  }
  onMounted(() => {
    let options = {};
    getPatientAllDictionaries(options);
    allDictionaries.value = options;
    console.log('allDictionaries', allDictionaries.value);
  });
</script>
