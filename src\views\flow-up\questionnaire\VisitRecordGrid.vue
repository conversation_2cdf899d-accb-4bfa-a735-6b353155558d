<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="100%" :minHeight="100" :showOkBtn="false">
    <BasicTable @register="registerTable" ref="tableRef">
      <template #bodyCell="{ column, record }">
        <template v-if="!(record.top || column.id?.includes('-'))">
          <template v-if="column.dataIndex === 'patientId'">
            {{ record.patientName }}
          </template>
          <template v-if="column.dataIndex === 'physicianId'">
            {{ record.physicianName }}
          </template>
        </template>
        <template v-if="column.key === 'action' && !record.top">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { getList, batchDelete } from '@/views/flow-up/visitRecord/api';
  import { columns } from '@/views/flow-up/visitRecord/index';
  import { BasicModal, useModal } from '@/components/Modal';
  import { BasicTable, useTable, TableAction, ActionItem, SorterResult } from '@/components/Table';
  import { reactive, toRefs, nextTick, ref, unref } from 'vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { cloneDeep } from 'lodash-es';
  import { useMessage } from '@/hooks/web/useMessage';

  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    locationScope: any;
    patientId: string;
  }

  const emit = defineEmits(['reload']);
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    maskConfig: {},
    locationScope: {},
    patientId: '',
  });
  const { title, patientId } = toRefs(state);

  const [registerModal, { openModal }] = useModal();
  defineExpose({ init });

  const defaultSearchInfo = {
    moduleId: '661954965010384453',
    patientId: patientId,
    superQueryJson: '',
    dataType: 0,
  };
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });
  const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
    api: getList,
    immediate: false,
    columns: columns,
    pagination: { pageSize: 20 }, //有分页
    searchInfo: unref(searchInfo),
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      return list;
    },
    sortFn: (sortInfo: SorterResult | SorterResult[]) => {
      if (Array.isArray(sortInfo)) {
        const sortList = sortInfo.map(o => (o.order === 'descend' ? '-' : '') + o.field);
        return { sidx: sortList.join(',') };
      } else {
        const { field, order } = sortInfo;
        if (field && order) {
          // 排序字段
          return { sidx: (order === 'descend' ? '-' : '') + field };
        } else {
          return {};
        }
      }
    },
    ellipsis: true,
    bordered: true,
    actionColumn: {
      width: 50,
      title: t('component.table.action'),
      dataIndex: 'action',
    },
  });
  function getTableActions(record): ActionItem[] {
    return [
      {
        label: t('common.delText', '删除'),
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
      emit('reload');
    });
  }
  function init(data) {
    state.patientId = data.id;
    searchInfo.patientId = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    reload();
  }
</script>
