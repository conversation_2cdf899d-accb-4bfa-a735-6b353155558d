.right-container {
  width: 500px;
  flex-shrink: 0;
  background-color: @component-background;
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  border-radius: 8px;

  .header-container {
    flex: 50px 0 0;
    border-bottom: 1px solid @border-color-base1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-weight: 600;
    background-color: @app-content-background;
    .header-container-left {
      width: 58%;
      display: flex;
      .node-name {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        span {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          min-width: 0;
        }
      }
      i {
        padding-left: 5px;
        color: @text-color-secondary;
      }
    }
    .header-container-right {
      color: @text-color-secondary;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: right;
    }
  }
  .common-pane {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    .pane-tabs {
      flex-shrink: 0;
      .ant-tabs-nav {
        margin-bottom: 0;
        padding-left: 20px;
        .ant-tabs-tab + .ant-tabs-tab {
          margin-left: 35px;
        }
      }
    }
    .approver-pane-tabs {
      .ant-tabs-tab + .ant-tabs-tab {
        margin-left: 20px !important;
      }
    }
    .config-content {
      flex: 1;
      overflow: auto;
      padding: 15px;
      .ant-form-item {
        margin-bottom: 12px;
      }
      & > .ant-form-item {
        & > .ant-form-item-row > .ant-form-item-label {
          font-weight: 600;
        }
        & > .ant-form-item-row > .ant-form-item-control {
          padding: 0 10px;
        }
      }
      .file-info {
        margin-top: 10px;
        display: flex;
        align-items: center;
        span {
          flex: 1;
          padding: 3px 5px;
          border-radius: 2px;
          background-color: @component-background;
        }
        i {
          padding-left: 5px;
          font-size: 18px;
          cursor: pointer;
        }
      }
      .link-text {
        width: 145px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .parameter-box {
        display: block;
        min-width: 0;
        width: 112px;
        height: 30px;
        line-height: 30px;
        margin-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .btn-cell {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .ant-checkbox-wrapper {
          width: 80px;
          margin-right: 10px;
          flex-shrink: 0;
        }
        &:last-child {
          margin-bottom: unset;
        }
      }
      .conditions-content {
        margin-top: 10px;
        padding: 6px 0;
        min-height: 36px;
        display: flex;
        align-items: center;
        background-color: @app-content-background;
        border-radius: 4px;
        cursor: pointer;
        span {
          padding-left: 10px;
          flex: 1;
        }
        i {
          padding: 0 10px;
        }
      }
      .parameter-content {
        padding: 6px 0;
        display: flex;
        align-items: center;
        background-color: @app-content-background;
        border-radius: 4px;
        span {
          padding-left: 10px;
          flex: 1;
        }
        i {
          cursor: pointer;
          padding: 0 10px;
        }
      }
      .approver-rule-content {
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid @border-color-base;
        .approver-content {
          background-color: @app-content-background;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 10px;
          i {
            padding: 0 8px;
            cursor: pointer;
            font-size: 14px;
          }
        }
        .approver-text {
          padding: 10px 8px;
        }
      }
    }
  }

  .type-radio {
    .ant-radio-wrapper {
      width: calc(33% - 8px);
      line-height: 32px;
    }
  }
  .counterSign-radio {
    .ant-radio-wrapper {
      width: 100%;
      line-height: 32px;
      margin-right: 0;
    }
  }
  .countersign-cell {
    display: flex;
    align-items: center;
  }
}
.ant-modal.rule-modal {
  .ant-modal-body > .scrollbar {
    padding: 0 !important;
  }
  .node-tabs {
    .ant-tabs-nav {
      margin-bottom: 10px;
    }
    .ant-tabs-nav-wrap {
      padding: 0 20px;
    }
    .ant-tabs-tabpane {
      min-height: 300px !important;
      max-height: 500px !important;
      padding: 0 10px 10px;
      overflow: auto;
    }
    .rule-cell {
      line-height: 32px;
      &.mid {
        text-align: center;
        color: @primary-color;
      }
    }
    .icon-ym-nav-close {
      font-size: 12px;
    }
    .common-tip {
      color: @text-color-secondary;
      font-size: 14px;
      line-height: 30px;
    }
  }
}
