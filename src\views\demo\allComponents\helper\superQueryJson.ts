const superQuery<PERSON>son = [
	{
		"clearable":true,
		"maxlength":null,
		"useScan":false,
		"suffixIcon":"",
		"fullName":"单行输入",
		"fullNameI18nCode":[
			""
		],
		"addonAfter":"",
		"showCount":false,
		"__config__":{
			"formId":101,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"labelWidth":100,
			"className":[],
			"label":"单行输入",
			"trigger":"blur",
			"showLabel":true,
			"required":true,
			"tableName":"demo_allcomponents",
			"renderKey":1727145648554,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"__vModel__":"inputField101",
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"inputField101",
		"placeholder":"请输入",
		"prefixIcon":"",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	},
	{
		"clearable":true,
		"maxlength":null,
		"fullName":"多行输入",
		"fullNameI18nCode":[
			""
		],
		"autoSize":{
			"minRows":4,
			"maxRows":4
		},
		"showCount":true,
		"__config__":{
			"formId":102,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"多行输入",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"demo_allcomponents",
			"renderKey":1727145650150,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-textarea",
			"xundaKey":"textarea",
			"tag":"XundaTextarea",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"__vModel__":"textareaField102",
		"style":{
			"width":"100%"
		},
		"disabled":false,
		"id":"textareaField102",
		"placeholder":"请输入",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	}
]
export default superQueryJson