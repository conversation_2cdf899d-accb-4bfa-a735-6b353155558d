<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="name">
              <template #label>姓名 </template>
              <XundaInput
                v-model:value="dataForm.name"
                placeholder="请输入"
                disabled
                detailed
                allowClear
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.name">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="age">
              <template #label>年龄 </template>
              <XundaInputNumber v-model:value="dataForm.age" placeholder="请输入" disabled detailed :style="{ width: '100%' }" :step="1" :controls="false">
              </XundaInputNumber>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="sex">
              <template #label>性别 </template> <p>{{ getDictionaryFullName(dataForm.sex, state.sexOptions) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="admissionNo">
              <template #label>住院号 </template> <p>{{ dataForm.admissionNo }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="idCard">
              <template #label>身份证 </template>
              <XundaInput
                v-model:value="dataForm.idCard"
                placeholder="请输入"
                disabled
                detailed
                allowClear
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.idCard">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="addressDetail">
              <template #label>详细地址 </template>
              <XundaLocation v-model:value="dataForm.addressDetail" disabled detailed allowClear :adjustmentScope="500"> </XundaLocation>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="admittingDiagnosis">
              <template #label>入院诊断 </template>
              <XundaInput
                v-model:value="dataForm.admittingDiagnosis"
                placeholder="请输入"
                disabled
                detailed
                allowClear
                :style="{ width: '100%' }"
                :maskConfig="maskConfig.admittingDiagnosis">
              </XundaInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="dischargeDate">
              <template #label>出院日期 </template> <p>{{ toDateString(dataForm.dischargeDate, 'YYYY年MM月DD日') }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="admissionDate">
              <template #label>入院日期 </template> <p>{{ toDateString(dataForm.admissionDate, 'YYYY年MM月DD日') }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="pipeBedPhysician">
              <template #label>管床医师 </template>
              <p class="link-text" @click="toDetail('661876504619124229', dataForm.pipeBedPhysician)">{{ dataForm.physicianName }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="dnumber">
              <template #label>随访应完成人数 </template>
              <p>{{ dataForm.dnumber }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="fcount">
              <template #label>随访次数 </template>
              <p class="link-text" @click="toVisitRecordGrid(dataForm.id)">{{ dataForm.fcount }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="rate">
              <template #label>随访率 </template>
              <p>{{ toFixedPercent(dataForm.rate) }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="type">
              <template #label>随访类型 </template> <p>{{ getDictionaryFullName(dataForm.type, state.typeOptions, 'id') }}</p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <PhysicianDetail ref="detailRef" />
  <VisitRecordGrid ref="visitRecordGridRef" @reload="initData" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
  import { getDetailInfo } from '@/views/flow-up/patient/api';
  import { reactive, toRefs, nextTick, ref } from 'vue';
  import { BasicModal, useModal } from '@/components/Modal';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { toDateString, getDictionaryFullName, toFixedPercent } from '@/utils/myUtil';
  import { getDictionaryDataSelector } from '@/api/systemData/dictionary';
  import PhysicianDetail from '@/views/flow-up/physician/Detail.vue';
  import VisitRecordGrid from '@/views/flow-up/patient/VisitRecordGrid.vue';
  interface State {
    dataForm: any;
    title: string;
    maskConfig: any;
    locationScope: any;
    sexOptions: [];
    typeOptions: [];
  }

  defineOptions({ name: 'Detail' });
  const emit = defineEmits(['reload']);
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const { t } = useI18n();
  const detailRef = ref<any>(null);
  const visitRecordGridRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: t('common.detailText', '详情'),
    maskConfig: {
      name: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      idCard: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
      admittingDiagnosis: {
        prefixType: 1,
        useUnrealMask: false,
        maskType: 1,
        unrealMaskLength: 1,
        prefixLimit: 0,
        suffixLimit: 0,
        filler: '*',
        prefixSpecifyChar: '',
        suffixType: 1,
        ignoreChar: '',
        suffixSpecifyChar: '',
      },
    },
    locationScope: {
      addressDetail: [],
    },
    sexOptions: [],
    typeOptions: [],
  });

  const { title, dataForm, maskConfig } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
        getfsexOptions();
        getTypeOptions();
      });
    });
  }

  //数据选项--数据字典初始化方法
  function getfsexOptions() {
    getDictionaryDataSelector('963255a34ea64a2584c5d1ba269c1fe6').then(res => {
      state.sexOptions = res.data.list;
    });
  }

  //数据选项--数据字典初始化方法
  function getTypeOptions() {
    getDictionaryDataSelector('664466612501358213').then(res => {
      state.typeOptions = res.data.list;
    });
  }

  function toDetail(modelId, id) {
    if (!id) return;
    detailRef.value?.init({ id });
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }

  function toVisitRecordGrid(id) {
    if (!id) return;
    visitRecordGridRef.value?.init({ id });
  }
</script>
