@prefix-cls: ~'@{namespace}-basic-table';

// fix table unnecessary scrollbar
.@{prefix-cls} {
  .hide-scrollbar-y {
    .ant-spin-nested-loading {
      .ant-spin-container {
        .ant-table {
          .ant-table-container,
          .ant-table-content {
            .ant-table-scroll {
              .ant-table-hide-scrollbar {
                overflow-y: auto !important;
              }

              .ant-table-body {
                overflow-y: auto !important;
              }
            }
            // .ant-table-body {
            //   overflow-y: auto !important;
            // }

            .ant-table-fixed-right {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-y: auto !important;
                }
              }
            }

            .ant-table-fixed-left {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-y: auto !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .hide-scrollbar-x {
    .ant-spin-nested-loading {
      .ant-spin-container {
        .ant-table {
          .ant-table-container,
          .ant-table-content {
            .ant-table-scroll {
              .ant-table-hide-scrollbar {
                //overflow-x: auto !important;
              }

              .ant-table-body {
                overflow: auto !important;
              }
            }

            .ant-table-fixed-right {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-x: auto !important;
                }
              }
            }

            .ant-table-fixed-left {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-x: auto !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
.ant-table {
  .ant-table-tbody .ant-table-wrapper .ant-table {
    margin: 0 !important;
  }
  .ant-table-placeholder {
    .ant-table-cell {
      border-bottom: none !important;
      .ant-table-expanded-row-fixed {
        &::after {
          border-right: none !important;
        }
      }
    }
  }
}
.ant-table-wrapper {
  .ant-table-thead > tr > th,
  .ant-table-thead > tr > td {
    font-weight: 500;
  }
}
