export default [
  {
    options: {
      left: 235.5,
      top: 18,
      height: 33,
      width: 120,
      title: '劳动合同',
      coordinateSync: false,
      widthHeightSync: false,
      fontSize: 21,
      fontWeight: 'bold',
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      right: 353.99571990966797,
      bottom: 42.743614196777344,
      vCenter: 293.99571990966797,
      hCenter: 26.243614196777344,
      fontFamily: 'SimSun',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 73.5,
      height: 16.5,
      width: 213,
      title: '甲方（用人单位）：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 321,
      top: 73.5,
      height: 16.5,
      width: 213,
      title: '法定代表人：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 321,
      top: 105,
      height: 16.5,
      width: 213,
      title: '身份证号码：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 258.2428894042969,
      bottom: 85.74715805053711,
      vCenter: 151.74288940429688,
      hCenter: 77.49715805053711,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 105,
      height: 16.5,
      width: 213,
      title: '乙方（职工）：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 258.2428894042969,
      bottom: 85.74715805053711,
      vCenter: 151.74288940429688,
      hCenter: 77.49715805053711,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 46.5,
      top: 130.5,
      height: 64.5,
      width: 496.5,
      title: '     根据(中华人民共和国劳动法》和相关法律、法规的规定，甲乙双方按照平等自愿、协商一致的原则订立本合同。\n一、合同期限\n',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      lineHeight: 18,
      longTextIndent: 22.5,
      right: 542.25,
      bottom: 192.7473907470703,
      vCenter: 294,
      hCenter: 167.9973907470703,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 208.5,
      top: 180,
      height: 24,
      width: 93,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 298.74481201171875,
      bottom: 201.98959350585938,
      vCenter: 252.24481201171875,
      hCenter: 189.98959350585938,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 87,
      top: 180,
      height: 24,
      width: 93,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 180.23961639404297,
      bottom: 202.75000762939453,
      vCenter: 133.73961639404297,
      hCenter: 190.75000762939453,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 181.5,
      top: 187.5,
      height: 15,
      width: 27,
      title: '起至',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textContentVerticalAlign: 'middle',
      right: 205.4999771118164,
      bottom: 170.24785995483398,
      vCenter: 191.9999771118164,
      hCenter: 162.74785995483398,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 69,
      top: 187.5,
      height: 15,
      width: 16.5,
      title: '至',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textContentVerticalAlign: 'middle',
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 304.5,
      top: 187.5,
      height: 15,
      width: 27,
      title: '止。',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textContentVerticalAlign: 'middle',
      right: 330.2428665161133,
      bottom: 171.24077224731445,
      vCenter: 316.7428665161133,
      hCenter: 163.74077224731445,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 45,
      top: 208.5,
      height: 478.5,
      width: 496.5,
      title:
        '二、工作内容\n1.乙方的工作岗位为\n2.乙方的工作职责\n三、工作时间\n早九晚六，周末双休。\n四、工资待遇\n乙方正常工作时间的工资为\n五、劳动保护和劳动条件\n甲方按国家和省有关劳动保护规定提供符合国家劳动卫生标准的劳动作业场所，切实保护乙方在生产工作中的安全和健康。\n六、福利待遇\n甲方按规定给予乙方享受节日假、年休假、婚假、丧假、探亲假、产假、看护假等带薪假期，并按本合同约定的工资标准支付工资。\n七、合同的变更\n甲乙双方经协商一致，可以变更本合同，并办理变更本合同的手续。\n八、合同的解除\n经甲乙双方协商一致或有符合法律规定的情形可以解除本合同。\n九、合同的终止\n本合同期满或甲乙双方约定的本合同终止条件出现，本合同即行终止。\n十、劳动争议处理和违反劳动合同的法律责任\n本合同依法经双方签字或盖章订立后具有法律约束力，双方必须严格履行。如果发生劳动争议，双方可以协商解决，也可以依法申请调解、仲载、提起诉讼。任何一方违反本合同约定，应当承担相应的法律责任。\n十一、其他\n1、本合同一式两份，具有同等法律效力，甲乙双方各执一份，双方应妥善保管。\n2、本劳动合同自甲乙双方签字、盖章之日起生效。\n',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      lineHeight: 18,
      longTextIndent: 22.5,
      right: 539.98828125,
      bottom: 811.48828125,
      vCenter: 291.73828125,
      hCenter: 515.23828125,
    },
    printElementType: { title: '长文本', type: 'longText' },
  },
  {
    options: {
      left: 175.5,
      top: 219,
      height: 24,
      width: 147,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 322.4948043823242,
      bottom: 242.9973907470703,
      vCenter: 248.99480438232422,
      hCenter: 230.9973907470703,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 165,
      top: 235.5,
      height: 24,
      width: 330,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 311.49478912353516,
      bottom: 259.74739837646484,
      vCenter: 237.99478912353516,
      hCenter: 247.74739837646484,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 210,
      top: 309,
      height: 24,
      width: 87,
      coordinateSync: false,
      widthHeightSync: false,
      textAlign: 'center',
      textContentVerticalAlign: 'middle',
      borderBottom: 'solid',
      qrCodeLevel: 0,
      right: 539.5000076293945,
      bottom: 333.24478912353516,
      vCenter: 374.50000762939453,
      hCenter: 321.24478912353516,
      hideTitle: true,
    },
    printElementType: { title: '', type: 'text' },
  },
  {
    options: {
      left: 298.5,
      top: 318,
      height: 15,
      width: 198,
      title: '元/月，加班工资视情况另行协商。',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textContentVerticalAlign: 'middle',
      right: 329.9999771118164,
      bottom: 267.74573135375977,
      vCenter: 316.4999771118164,
      hCenter: 260.24573135375977,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 345,
      top: 703.5,
      height: 16.5,
      width: 193.5,
      title: '乙方：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 63,
      top: 703.5,
      height: 16.5,
      width: 213,
      title: '甲方：（盖章）',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 257.99146270751953,
      bottom: 65.24574279785156,
      vCenter: 151.49146270751953,
      hCenter: 56.99574279785156,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 63,
      top: 744,
      height: 16.5,
      width: 213,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 260.99146270751953,
      bottom: 639.7435684204102,
      vCenter: 154.49146270751953,
      hCenter: 631.4935684204102,
    },
    printElementType: { title: '文本', type: 'text' },
  },
  {
    options: {
      left: 345,
      top: 744,
      height: 16.5,
      width: 193.5,
      title: '日期：',
      coordinateSync: false,
      widthHeightSync: false,
      fontFamily: 'SimSun',
      fontSize: 11.25,
      textAlign: 'left',
      textContentVerticalAlign: 'middle',
      right: 260.99146270751953,
      bottom: 639.7435684204102,
      vCenter: 154.49146270751953,
      hCenter: 631.4935684204102,
    },
    printElementType: { title: '文本', type: 'text' },
  },
];
