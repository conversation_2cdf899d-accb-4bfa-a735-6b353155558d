import dayjs from "dayjs";
import { getTimeUnit } from "./xunda";
import { getAreaByIds } from '@/api/system/area';

export function getRelationDate(timeRule, timeType, timeTarget, timeValueData, dataValue) {
    let timeDataValue: any = null;
    let timeValue = Number(timeValueData);
    if (timeRule) {
        if (timeType == 1) {
            timeDataValue = timeValue;
        } else if (timeType == 2) {
            timeDataValue = dataValue;
        } else if (timeType == 3) {
            timeDataValue = new Date().getTime();
        } else if (timeType == 4 || timeType == 5) {
            const type = getTimeUnit(timeTarget);
            const method = timeType == 4 ? 'subtract' : 'add';
            timeDataValue = dayjs()[method](timeValue, type).valueOf();
        }
    }
    return timeDataValue;
}
export function getRelationTime(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
    let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType;
    let timeDataValue: any = null;
    if (timeRule) {
        if (timeType == 1) {
            timeDataValue = timeValue || '00:00:00';
            if (timeDataValue.split(':').length == 3) {
                timeDataValue = timeDataValue;
            } else {
                timeDataValue = timeDataValue + ':00';
            }
        } else if (timeType == 2) {
            timeDataValue = dataValue;
        } else if (timeType == 3) {
            timeDataValue = dayjs().format(format);
        } else if (timeType == 4 || timeType == 5) {
            const type = getTimeUnit(timeTarget + 3);
            const method = timeType == 4 ? 'subtract' : 'add';
            timeDataValue = dayjs()[method](timeValue, type).format(format);
        }
    }
    return timeDataValue;
}


export function toDateString(date, format: string = 'YYYY-MM-DD') {
    if (date) {
        return dayjs(date).format(format);
    }
    return '';
}
export function toLocalDateString(date, format: string = 'YYYY年MM月DD日') {
    if (date) {
        return dayjs(date).format(format);
    }
    return '';
}


export function toDateTimeString(date, format: string = 'YYYY-MM-DD HH:mm:ss') {
    if (date) {
        return dayjs(date).format(format);
    }
    return '';
}

export function getDictionaryFullName(value, options, key = 'enCode') {
    let fullName = '';
    if (value != undefined && options && options.length > 0) {
        options.forEach((item) => {
            if (item[key] && item[key] == value) {
                fullName = item.fullName;
            }
        });
    }
    return fullName;
}


export function toFixedNumber(num, fixed = 2) {
    if (num) {
        return parseFloat(num).toFixed(fixed);
    }
    return '0.00';
}
// 获取百分数
export function toFixedPercent(num, fixed = 2) {
    if (num) {
        return parseFloat(num * 100).toFixed(fixed) + '%';
    }
    return '0.00%';
}

/**
 * 根据数字输出中文
 * @param num 数字
 * @returns 
 */
export function numberToChinese(num: number) {
    var chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    var chnUnitSection = ["", "万", "亿", "万亿"];
    var chnUnitChar = ["", "十", "百", "千"];

    function SectionToChinese(section) {
        var strIns = '', chnStr = '';
        var unitPos = 0;
        var zero = true;
        while (section > 0) {
            var v = section % 10;
            if (v === 0) {
                if (!zero) {
                    zero = true;
                    chnStr = chnNumChar[v] + chnStr;
                }
            } else {
                zero = false;
                strIns = chnNumChar[v];
                strIns += chnUnitChar[unitPos];
                chnStr = strIns + chnStr;
            }
            unitPos++;
            section = Math.floor(section / 10);
        }
        return chnStr;
    }

    var unitPos = 0;
    var strIns = '', chnStr = '';
    var needZero = false;

    if (num === 0) {
        return chnNumChar[0];
    }

    while (num > 0) {
        var section = num % 10000;
        if (needZero) {
            chnStr = chnNumChar[0] + chnStr;
        }
        strIns = SectionToChinese(section);
        strIns += (section !== 0) ? chnUnitSection[unitPos] : chnUnitSection[0];
        chnStr = strIns + chnStr;
        needZero = (section < 1000) && (section > 0);
        num = Math.floor(num / 10000);
        unitPos++;
    }

    return chnStr;
}