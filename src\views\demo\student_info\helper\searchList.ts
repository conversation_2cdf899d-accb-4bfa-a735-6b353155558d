const searchList = [
	{
		"useScan":false,
		"suffixIcon":"",
		"fullNameI18nCode":[
			""
		],
		"showCount":false,
		"__config__":{
			"formId":101,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"姓名",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727332852322,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"prop":"S_Name",
		"xundaKey":"input",
		"__vModel__":"S_Name",
		"searchMultiple":false,
		"disabled":false,
		"id":"S_Name",
		"placeholder":"请输入",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		},
		"clearable":true,
		"searchType":2,
		"maxlength":null,
		"fullName":"姓名",
		"label":"姓名",
		"addonAfter":"",
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"isKeyword":false,
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"prefixIcon":"",
		"labelI18nCode":""
	},
	{
		"useScan":false,
		"suffixIcon":"",
		"fullNameI18nCode":[
			""
		],
		"showCount":false,
		"__config__":{
			"formId":102,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"学号",
			"trigger":"blur",
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727332952710,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"prop":"S_No",
		"xundaKey":"input",
		"__vModel__":"S_No",
		"searchMultiple":false,
		"disabled":false,
		"id":"S_No",
		"placeholder":"请输入",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		},
		"clearable":true,
		"searchType":2,
		"maxlength":null,
		"fullName":"学号",
		"label":"学号",
		"addonAfter":"",
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"isKeyword":false,
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"prefixIcon":"",
		"labelI18nCode":""
	},
	{
		"controls":false,
		"searchType":3,
		"fullName":"设计子表-成绩",
		"fullNameI18nCode":[
			"",
			""
		],
		"label":"设计子表-成绩",
		"thousands":false,
		"isAmountChinese":false,
		"addonAfter":"",
		"__config__":{
			"formId":106,
			"relationTable":"demo_student_score",
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"parentVModel":"tableField103",
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"className":[],
			"label":"成绩",
			"trigger":[
				"blur",
				"change"
			],
			"showLabel":true,
			"required":false,
			"tableName":"demo_student_info",
			"renderKey":1727332976168,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-number",
			"xundaKey":"inputNumber",
			"isSubTable":true,
			"tag":"XundaInputNumber",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"prop":"tableField103-SS_Score",
		"xundaKey":"inputNumber",
		"__vModel__":"SS_Score",
		"searchMultiple":false,
		"isKeyword":false,
		"style":{
			"width":"100%"
		},
		"step":1,
		"disabled":false,
		"id":"tableField103-SS_Score",
		"placeholder":"请输入",
		"value":[],
		"addonBefore":"",
		"labelI18nCode":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		}
	}
]
export default searchList
