<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" defaultFullscreen :minHeight="100"
    :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="middle" layout="horizontal" labelAlign="left"
        :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-tabs v-model:activeKey="state.activetab" tabPosition="top" class="mb-20">
          <a-tab-pane tab="个人信息" key="1" forceRender>
            <a-row :gutter="15">
              <!-- 具体表单 -->
              <a-col :span="12" class="ant-col-item">
                <a-row :gutter="15">
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="name">
                      <template #label>姓名 </template>
                      <p>{{ dataForm.name }}</p>
                    </a-form-item>
                  </a-col>
                  <a-col :span="24" class="ant-col-item">
                    <a-form-item name="sex">
                      <template #label>性别 </template>
                      <p>{{ getDictionaryFullName(dataForm.sex, optionsObj.sexOptions) }}</p>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item :labelCol="{ style: { width: '50px' } }" name="picture">
                  <template #label>照片 </template>
                  <XundaUploadImgSingle v-model:value="dataForm.picture" disabled detailed simple>
                  </XundaUploadImgSingle>
                </a-form-item>
              </a-col>

              <a-col :span="12" class="ant-col-item">
                <a-form-item name="birthdate">
                  <template #label>出生日期 </template>
                  <p>{{ toLocalDateString(dataForm.birthdate) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="nation">
                  <template #label>民族 </template>
                  <p>{{ getDictionaryFullName(dataForm.nation, optionsObj.nationOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="politicalGroup">
                  <template #label>党派 </template>
                  <p>{{ getDictionaryFullName(dataForm.politicalGroup, optionsObj.politicalGroupOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="phone">
                  <template #label>手机号 </template>
                  <p>{{ dataForm.phone }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="nativePlace">
                  <template #label>籍贯 </template>
                  <p>{{ dataForm.nativePlace }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="idCard">
                  <template #label>身份证号 </template>
                  <p>{{ dataForm.idCard }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="address">
                  <template #label>家庭住址 </template>
                  <p>{{ dataForm.address }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="position">
                  <template #label>单位任职 </template>
                  <p>{{ getDictionaryFullName(dataForm.position, optionsObj.positionOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="medicalLevel">
                  <template #label>医师级别 </template>
                  <p>{{ getDictionaryFullName(dataForm.medicalLevel, optionsObj.medicalLevelOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="vocationalStatus">
                  <template #label>执业状况 </template>
                  <p>{{ getDictionaryFullName(dataForm.vocationalStatus, optionsObj.vocationalStatusOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-row :gutter="15">
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="certificateNo">
                      <template #label>执业证书编号 </template>
                      <p>{{ dataForm.certificateNo }}</p>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="certificate">
                      <template #label>执业证书 </template>
                      <XundaUploadFile v-model:value="dataForm.certificate" disabled detailed :fileSize="10"
                        sizeUnit="MB" :limit="9" pathType="defaultPath" timeFormat="YYYY" buttonText="点击上传">
                      </XundaUploadFile>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-row :gutter="15">
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="qualificationCertificateNo">
                      <template #label>资格证书编号 </template>
                      <p>{{ dataForm.qualificationCertificateNo }}</p>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="qualificationCertificate">
                      <template #label>资格证书 </template>
                      <XundaUploadFile v-model:value="dataForm.qualificationCertificate" disabled detailed
                        :fileSize="10" sizeUnit="MB" :limit="9" pathType="defaultPath" timeFormat="YYYY"
                        buttonText="点击上传">
                      </XundaUploadFile>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-row :gutter="15">
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="workCertificateNo">
                      <template #label>工作证书编号 </template>
                      <p>{{ dataForm.workCertificateNo }}</p>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12" class="ant-col-item">
                    <a-form-item name="workCertificate">
                      <template #label>工作证书 </template>
                      <XundaUploadFile v-model:value="dataForm.workCertificate" disabled detailed :fileSize="10"
                        sizeUnit="MB" :limit="9" pathType="defaultPath" timeFormat="YYYY" buttonText="点击上传">
                      </XundaUploadFile>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="technicalTitle">
                  <TechnicalTitleItem title="技术职称" v-model:value="dataForm.technicalTitle"
                    :data-source="dataForm.technicalTitle"></TechnicalTitleItem>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="remarks">
                  <template #label>备注说明 </template>
                  <p>{{ dataForm.remarks }}</p>
                </a-form-item>
              </a-col>
              <!-- 表单结束 -->
            </a-row>
          </a-tab-pane>
          <a-tab-pane tab="专业方向" key="2" forceRender>
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="graduationSchool">
                  <template #label>毕业学校 </template>
                  <XundaInput v-model:value="dataForm.graduationSchool" placeholder="请输入" disabled detailed allowClear
                    :style="{ width: '100%' }" :maskConfig="maskConfig.graduationSchool">
                  </XundaInput>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="graduationTime">
                  <template #label>毕业时间 </template>
                  <p>{{ toLocalDateString(dataForm.graduationTime) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="educationLevel">
                  <template #label>学历 </template>
                  <p>{{ getDictionaryFullName(dataForm.educationLevel, optionsObj.educationLevelOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="degree">
                  <template #label>学位 </template>
                  <p>{{ getDictionaryFullName(dataForm.degree, optionsObj.degreeOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="major">
                  <template #label>专业 </template>
                  <p>{{ getDictionaryFullName(dataForm.major, optionsObj.majorOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="workingSeniority">
                  <template #label>从业时间 </template>
                  <p>{{ toLocalDateString(dataForm.workingSeniority) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="12" class="ant-col-item">
                <a-form-item name="workType">
                  <template #label>从业类型 </template>
                  <p>{{ getDictionaryFullName(dataForm.workType, optionsObj.workTypeOptions) }}</p>
                </a-form-item>
              </a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="majorField">
                  <MajorFieldItem title="专业方向" v-model:value="dataForm.majorField" :data-source="dataForm.majorField">
                  </MajorFieldItem>
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane tab="工作经历" key="3" forceRender>
            <a-row :gutter="15">
              <a-col :span="24" class="ant-col-item">
                <a-col :span="24" class="ant-col-item">
                  <a-form-item name="provinceAndCity">
                    <template #label>省份 </template>
                    <p>{{ dataForm.provinceAndCityString }}</p>
                  </a-form-item>
                </a-col>
                <a-col :span="24" class="ant-col-item">
                  <a-form-item name="hospitalName">
                    <template #label>医院名称 </template>
                    <p>{{ dataForm.hospitalName }}</p>
                  </a-form-item>
                </a-col>
                <a-col :span="12" class="ant-col-item">
                  <a-form-item name="department">
                    <template #label>科室 </template>
                    <p>{{ dataForm.department }}</p>
                  </a-form-item>
                </a-col>
                <a-col :span="12" class="ant-col-item">
                  <a-form-item name="departmentName">
                    <template #label>科室名称 </template>
                    <p>{{ dataForm.departmentName }}</p>
                  </a-form-item>
                </a-col>
                <a-col :span="24" class="ant-col-item">
                  <a-form-item name="workHistory">
                    <FormItemTable title="学术任职" :columns="workHistoryColumns" readonly
                      v-model:value="dataForm.workHistory" :data-source="dataForm.workHistory"></FormItemTable>
                  </a-form-item> </a-col></a-col>
              <a-col :span="24" class="ant-col-item">
                <a-form-item name="academicPosition">
                  <FormItemTable title="学术任职" :columns="academicPositionColumns" readonly
                    v-model:value="dataForm.academicPosition" :data-source="dataForm.academicPosition"></FormItemTable>
                </a-form-item>
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-row>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <RelationDetail ref="relationDetailRef" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
import { getDetailInfo } from './api';
import { getConfigData } from '@/api/onlineDev/visualDev';
import { reactive, toRefs, nextTick, ref } from 'vue';
import { BasicModal, useModal } from '@/components/Modal';
// 有关联表单详情
import RelationDetail from '@/views/common/dynamicModel/list/detail/index.vue';
// 表单权限
import { usePermission } from '@/hooks/web/usePermission';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { getDictionaryFullName, toLocalDateString } from '@/utils/myUtil';
import { getPhysicianAllDictionaryData, academicPositionColumns, workHistoryColumns } from '.';
import FormItemTable from '@/components/FormItemTable/FormItemTable.vue';

import TechnicalTitleItem from './TechnicalTitleItem.vue';
import MajorFieldItem from './MajorFieldItem.vue';
import { getAreaByIds } from '@/api/system/area';

interface State {
  dataForm: any;
  optionsObj: any;
  title: string;
  maskConfig: any;
  locationScope: any;
  activetab: string;
}

defineOptions({ name: 'Detail' });
const { createMessage, createConfirm } = useMessage();
const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
const { t } = useI18n();

// 学术任职子表
const relationDetailRef = ref<any>(null);
const state = reactive<State>({
  activetab: '1',

  dataForm: {},
  title: t('common.detailText', '详情'),
  maskConfig: {
    name: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    phone: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    nativePlace: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    idCard: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    politicalGroup: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    address: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    position: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    technicalTitle: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    vocationalStatus: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    graduationSchool: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    major: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    degree: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    educationLevel: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    workType: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    majorField: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    hospitalName: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    department: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    academicPosition: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
    workHistory: {
      prefixType: 1,
      useUnrealMask: false,
      maskType: 1,
      unrealMaskLength: 1,
      prefixLimit: 0,
      suffixLimit: 0,
      filler: '*',
      prefixSpecifyChar: '',
      suffixType: 1,
      ignoreChar: '',
      suffixSpecifyChar: '',
    },
  },
  locationScope: {},
  optionsObj: {
    defaultProps: { label: 'fullName', value: 'enCode' },
  },
});
const { title, dataForm, maskConfig, optionsObj } = toRefs(state);
// 表单权限
const { hasFormP } = usePermission();

defineExpose({ init });

function init(data) {
  getPhysicianAllDictionaryData(state.optionsObj);
  state.dataForm.id = data.id;
  openModal();
  nextTick(() => {
    setTimeout(initData, 0);
  });
}
function initData() {
  changeLoading(true);
  state.activetab = '1';
  if (state.dataForm.id) {
    getData(state.dataForm.id);
  } else {
    closeModal();
  }
}
function getData(id) {
  getDetailInfo(id).then(res => {
    state.dataForm = res.data || {};
    if (state.dataForm.provinceAndCity && state.dataForm.provinceAndCity.length > 0
    ) {
      getAreaByIds([state.dataForm.provinceAndCity]).then(res => {
        if (res.data && res.data.length > 0) state.dataForm.provinceAndCityString = res.data[0].join('/');
      });
    }
    if (!state.dataForm.picture) {
      state.dataForm.picture = '/api/file/Image/userAvatar/001.png';
    }
    nextTick(() => {
      changeLoading(false);
    });
  });
}

function toDetail(modelId, id) {
  if (!id) return;
  getConfigData(modelId).then(res => {
    if (!res.data || !res.data.formData) return;
    const formConf = JSON.parse(res.data.formData);
    formConf.popupType = 'general';
    const data = { id, formConf, modelId };
    relationDetailRef.value?.init(data);
  });
}
function setFormProps(data) {
  setModalProps(data);
}
function changeLoading(loading) {
  setFormProps({ loading });
}
</script>
