<template>
  <div class="xunda-content-wrapper">
    <div class="xunda-content-wrapper-center">
      <div class="xunda-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="xunda-content-wrapper-content bg-white">
        <BasicTable @register="registerTable" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addHandle()"> {{ t('common.add2Text', '新增') }}</a-button>

            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handelBatchRemove()"> {{ t('common.batchDelText', '批量删除') }}</a-button>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'picture'">
              <xunda-upload-img-single v-model:value="record['picture']" disabled detailed simple />
            </template>
            <template v-if="column.dataIndex === 'sex'">
              <span>{{ getDictionaryFullName(record['sex'], allDictionaryData.sexOptions) }}</span>
            </template>
            <template v-if="column.dataIndex === 'nation'">
              <span>{{ getDictionaryFullName(record['nation'], allDictionaryData.nationOptions) }}</span>
            </template>
            <template v-if="column.dataIndex === 'position'">
              <span>{{ getDictionaryFullName(record['position'], allDictionaryData.positionOptions) }}</span>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: t('common.editText', '编辑'),
                    onClick: updateHandle.bind(null, record),
                  },
                  {
                    label: t('common.delText', '删除'),
                    color: 'error',
                    modelConfirm: {
                      onOk: handleDelete.bind(null, record.id),
                    },
                  },
                  {
                    label: t('common.detailText', '详情'),
                    onClick: goDetail.bind(null, record),
                  },
                ]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" />
  </div>
</template>

<script lang="ts" setup>
  import { getList, batchDelete, createAllAccount } from './api';
  import { ref, reactive, onMounted } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useUserStore } from '@/store/modules/user';
  import { BasicForm, useForm } from '@/components/Form';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem, TableActionType } from '@/components/Table';
  import Form from './Form.vue';
  import Detail from './Detail.vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { usePermission } from '@/hooks/web/usePermission';
  import { columns, getPhysicianAllDictionaryData, searchSchemas } from './index';
  import { getDictionaryFullName } from '@/utils/myUtil';

  const route = useRoute();
  const { hasBtnP } = usePermission();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const userStore = useUserStore();

  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const relationDetailRef = ref<any>(null);
  const cacheList = ref<any>([]);

  const allDictionaryData = ref<any>([]);

  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '661876504619124229',
    superQueryJson: '',
    dataType: 0,
  };
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });

  const [registerSearchForm] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });

  const getTableList = params => {
    createAllAccount();
    return getList(params);
  };

  const [registerTable, { reload, setLoading, getFetchParams, getSelectRows, getSelectRowKeys, redoHeight, clearSelectedRowKeys }] = useTable({
    api: getTableList,
    columns: columns,
    searchInfo: searchInfo,
    clickToRowSelect: false,
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
      }));
      cacheList.value = cloneDeep(list);
      return list;
    },
    actionColumn: {
      width: 120,
      title: t('common.actionText', '操作'),
      dataIndex: 'action',
      fixed: 'right',
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({
        disabled: record.top,
      }),
    },
  });

  function handleSearchReset() {
    clearSelectedRowKeys();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchInfo);
    reload({ page: 1 });
  }

  // 编辑
  function updateHandle(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }
  // 删除
  function handleDelete(id) {
    const query = { ids: [id] };
    batchDelete(query).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }
  // 查看详情
  function goDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }
  // 新增
  function addHandle() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: cacheList.value,
    };
    formRef.value?.init(data);
  }
  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要删除这些数据吗, 是否继续?',
      onOk: () => {
        const query = { ids: ids };
        batchDelete(query).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }
  onMounted(() => {
    getPhysicianAllDictionaryData(allDictionaryData.value);
  });
</script>
