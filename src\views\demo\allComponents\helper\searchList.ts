const searchList = [
	{
		"useScan":false,
		"suffixIcon":"",
		"fullNameI18nCode":[
			""
		],
		"showCount":false,
		"__config__":{
			"formId":101,
			"visibility":[
				"pc",
				"app"
			],
			"noShow":false,
			"tipLabel":"",
			"tableFixed":"none",
			"dragDisabled":false,
			"labelWidth":100,
			"className":[],
			"label":"单行输入",
			"trigger":"blur",
			"showLabel":true,
			"required":true,
			"tableName":"demo_allcomponents",
			"renderKey":1727145648554,
			"layout":"colFormItem",
			"tagIcon":"icon-ym icon-ym-generator-input",
			"xundaKey":"input",
			"tag":"XundaInput",
			"regList":[],
			"tableAlign":"left",
			"span":24
		},
		"readonly":false,
		"prop":"inputField101",
		"xundaKey":"input",
		"__vModel__":"inputField101",
		"searchMultiple":false,
		"disabled":false,
		"id":"inputField101",
		"placeholder":"请输入",
		"addonBefore":"",
		"on":{
			"change":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
			"blur":"({ data, rowIndex, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
		},
		"clearable":true,
		"searchType":2,
		"maxlength":null,
		"fullName":"单行输入",
		"label":"单行输入",
		"addonAfter":"",
		"maskConfig":{
			"prefixType":1,
			"useUnrealMask":false,
			"maskType":1,
			"unrealMaskLength":1,
			"prefixLimit":0,
			"suffixLimit":0,
			"filler":"*",
			"prefixSpecifyChar":"",
			"suffixType":1,
			"ignoreChar":"",
			"suffixSpecifyChar":""
		},
		"isKeyword":false,
		"useMask":false,
		"showPassword":false,
		"style":{
			"width":"100%"
		},
		"prefixIcon":"",
		"labelI18nCode":""
	}
]
export default searchList