/* windicss layer base */
*,
::before,
::after {
  box-sizing: border-box;
}
* {
  --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
}
* > .enter-x:nth-child(1) {
  transform: translateX(50px);
}
* > .-enter-x:nth-child(1) {
  transform: translateX(-50px);
}
* > .enter-x:nth-child(1),
* > .-enter-x:nth-child(1) {
  z-index: 9;
  opacity: 0;
  animation: enter-x-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.1s;
}
* > .enter-y:nth-child(1) {
  transform: translateY(50px);
}
* > .-enter-y:nth-child(1) {
  transform: translateY(-50px);
}
* > .enter-y:nth-child(1),
* > .-enter-y:nth-child(1) {
  z-index: 9;
  opacity: 0;
  animation: enter-y-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.1s;
}
* > .enter-x:nth-child(2) {
  transform: translateX(50px);
}
* > .-enter-x:nth-child(2) {
  transform: translateX(-50px);
}
* > .enter-x:nth-child(2),
* > .-enter-x:nth-child(2) {
  z-index: 8;
  opacity: 0;
  animation: enter-x-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.2s;
}
* > .enter-y:nth-child(2) {
  transform: translateY(50px);
}
* > .-enter-y:nth-child(2) {
  transform: translateY(-50px);
}
* > .enter-y:nth-child(2),
* > .-enter-y:nth-child(2) {
  z-index: 8;
  opacity: 0;
  animation: enter-y-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.2s;
}
* > .enter-x:nth-child(3) {
  transform: translateX(50px);
}
* > .-enter-x:nth-child(3) {
  transform: translateX(-50px);
}
* > .enter-x:nth-child(3),
* > .-enter-x:nth-child(3) {
  z-index: 7;
  opacity: 0;
  animation: enter-x-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.3s;
}
* > .enter-y:nth-child(3) {
  transform: translateY(50px);
}
* > .-enter-y:nth-child(3) {
  transform: translateY(-50px);
}
* > .enter-y:nth-child(3),
* > .-enter-y:nth-child(3) {
  z-index: 7;
  opacity: 0;
  animation: enter-y-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.3s;
}
* > .enter-x:nth-child(4) {
  transform: translateX(50px);
}
* > .-enter-x:nth-child(4) {
  transform: translateX(-50px);
}
* > .enter-x:nth-child(4),
* > .-enter-x:nth-child(4) {
  z-index: 6;
  opacity: 0;
  animation: enter-x-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.4s;
}
* > .enter-y:nth-child(4) {
  transform: translateY(50px);
}
* > .-enter-y:nth-child(4) {
  transform: translateY(-50px);
}
* > .enter-y:nth-child(4),
* > .-enter-y:nth-child(4) {
  z-index: 6;
  opacity: 0;
  animation: enter-y-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.4s;
}
* > .enter-x:nth-child(5) {
  transform: translateX(50px);
}
* > .-enter-x:nth-child(5) {
  transform: translateX(-50px);
}
* > .enter-x:nth-child(5),
* > .-enter-x:nth-child(5) {
  z-index: 5;
  opacity: 0;
  animation: enter-x-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.5s;
}
* > .enter-y:nth-child(5) {
  transform: translateY(50px);
}
* > .-enter-y:nth-child(5) {
  transform: translateY(-50px);
}
* > .enter-y:nth-child(5),
* > .-enter-y:nth-child(5) {
  z-index: 5;
  opacity: 0;
  animation: enter-y-animation 0.4s ease-in-out 0.3s;
  animation-fill-mode: forwards;
  animation-delay: 0.5s;
}
:root {
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}
:-moz-focusring {
  outline: 1px dotted ButtonText;
}
:-moz-ui-invalid {
  box-shadow: none;
}
::moz-focus-inner {
  border-style: none;
  padding: 0;
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
abbr[title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
a {
  color: inherit;
  text-decoration: inherit;
}
body {
  margin: 0;
  font-family: inherit;
  line-height: inherit;
}
b {
  font-weight: bolder;
}
button {
  text-transform: none;
  background-color: transparent;
  background-image: none;
}
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}
button,
[role='button'] {
  cursor: pointer;
}
html {
  -webkit-text-size-adjust: 100%;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  line-height: 1.5;
}
h2,
h4,
h1,
h3,
h5 {
  font-size: inherit;
  font-weight: inherit;
}
input,
textarea,
button {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  padding: 0;
  line-height: inherit;
  color: inherit;
}
input::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::webkit-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input:-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
img,
svg,
canvas,
iframe,
embed,
video,
audio {
  display: block;
  vertical-align: middle;
}
img,
video {
  max-width: 100%;
  height: auto;
}
pre,
code {
  font-size: 1em;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}
p,
h2,
h4,
h1,
pre,
h3,
h5 {
  margin: 0;
}
table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}
textarea {
  resize: vertical;
}
textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}
textarea::webkit-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}
textarea:-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
textarea::-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
@keyframes enter-x-animation {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes enter-y-animation {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}
